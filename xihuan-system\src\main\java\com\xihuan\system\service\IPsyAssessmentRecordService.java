package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyAssessmentAnswer;
import com.xihuan.common.core.domain.entity.PsyAssessmentRecord;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;

import java.util.List;
import java.util.Map;

/**
 * 心理测评记录Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyAssessmentRecordService {
    
    /**
     * 查询测评记录列表
     * 
     * @param record 测评记录信息
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordList(PsyAssessmentRecord record);

    /**
     * 根据ID查询测评记录
     * 
     * @param id 测评记录ID
     * @return 测评记录信息
     */
    PsyAssessmentRecord selectRecordById(Long id);

    /**
     * 查询测评记录详情（包含用户、量表等信息）
     * 
     * @param id 测评记录ID
     * @return 测评记录详情
     */
    PsyAssessmentRecord selectRecordWithDetails(Long id);

    /**
     * 根据会话ID查询测评记录
     * 
     * @param sessionId 会话ID
     * @return 测评记录信息
     */
    PsyAssessmentRecord selectRecordBySessionId(String sessionId);

    /**
     * 根据用户ID查询测评记录列表
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordsByUserId(Long userId);

    /**
     * 根据量表ID查询测评记录列表
     * 
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordsByScaleId(Long scaleId);

    /**
     * 新增测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    int insertRecord(PsyAssessmentRecord record);

    /**
     * 修改测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    int updateRecord(PsyAssessmentRecord record);

    /**
     * 删除测评记录
     * 
     * @param ids 需要删除的测评记录ID
     * @return 结果
     */
    int deleteRecordByIds(Long[] ids);

    /**
     * 查询用户在指定量表的最新测评记录
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录信息
     */
    PsyAssessmentRecord selectLatestRecordByUserAndScale(Long userId, Long scaleId);

    /**
     * 查询用户在指定量表的测评次数
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评次数
     */
    int countRecordsByUserAndScale(Long userId, Long scaleId);

    /**
     * 查询用户的测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserRecordStats(Long userId);

    /**
     * 查询量表的测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleRecordStats(Long scaleId);

    /**
     * 查询进行中的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectInProgressRecords(Long userId);

    /**
     * 查询已完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectCompletedRecords(Long userId);

    /**
     * 开始测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param isAnonymous 是否匿名
     * @return 测评记录信息
     */
    PsyAssessmentRecord startTest(Long userId, Long scaleId, Integer isAnonymous);

    /**
     * 提交答案
     * 
     * @param sessionId 会话ID
     * @param questionId 题目ID
     * @param optionId 选项ID
     * @param answerText 答案内容
     * @param timeSpent 答题耗时
     * @return 结果
     */
    int submitAnswer(String sessionId, Long questionId, Long optionId, String answerText, Integer timeSpent);

    /**
     * 完成测评
     * 
     * @param sessionId 会话ID
     * @return 测评记录信息
     */
    PsyAssessmentRecord completeTest(String sessionId);

    /**
     * 中断测评
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    int interruptTest(String sessionId);

    /**
     * 查询测评排行榜
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordRanking(Long scaleId, Integer limit);

    /**
     * 查询用户测评历史
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectUserTestHistory(Long userId, Long scaleId);

    /**
     * 统计测评记录数量
     * 
     * @param record 查询条件
     * @return 数量
     */
    int countRecords(PsyAssessmentRecord record);

    /**
     * 查询测评趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<Map<String, Object>> selectRecordTrend(Integer days);

    /**
     * 获取测评记录DTO
     * 
     * @param id 测评记录ID
     * @return 测评记录DTO
     */
    PsyAssessmentDTO.RecordDTO getRecordDTO(Long id);

    /**
     * 获取测评记录列表DTO
     * 
     * @param record 查询条件
     * @return 测评记录DTO列表
     */
    List<PsyAssessmentDTO.RecordDTO> getRecordListDTO(PsyAssessmentRecord record);

    /**
     * 获取测评结果DTO
     * 
     * @param sessionId 会话ID
     * @return 测评结果DTO
     */
    PsyAssessmentDTO.RecordDTO getTestResultDTO(String sessionId);

    /**
     * 获取测评答案列表
     * 
     * @param recordId 测评记录ID
     * @return 答案列表
     */
    List<PsyAssessmentAnswer> getAnswerList(Long recordId);

    /**
     * 获取测评答案DTO列表
     * 
     * @param recordId 测评记录ID
     * @return 答案DTO列表
     */
    List<PsyAssessmentDTO.AnswerDTO> getAnswerListDTO(Long recordId);

    /**
     * 计算测评得分
     * 
     * @param recordId 测评记录ID
     * @return 得分信息
     */
    Map<String, Object> calculateScore(Long recordId);

    /**
     * 生成测评报告
     * 
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    String generateReport(Long recordId);
}
