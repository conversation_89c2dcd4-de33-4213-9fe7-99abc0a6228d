package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyCourseChapter;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyCourseChapterMapper;
import com.xihuan.system.service.IPsyCourseChapterService;
import com.xihuan.system.service.IPsyCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 课程章节内容表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyCourseChapterServiceImpl implements IPsyCourseChapterService {
    
    @Autowired
    private PsyCourseChapterMapper chapterMapper;
    
    @Autowired
    private IPsyCourseService courseService;

    /**
     * 查询章节列表
     * 
     * @param chapter 章节信息
     * @return 章节集合
     */
    @Override
    public List<PsyCourseChapter> selectChapterList(PsyCourseChapter chapter) {
        return chapterMapper.selectChapterList(chapter);
    }

    /**
     * 根据ID查询章节
     * 
     * @param id 章节ID
     * @return 章节信息
     */
    @Override
    public PsyCourseChapter selectChapterById(Long id) {
        return chapterMapper.selectChapterById(id);
    }

    /**
     * 根据课程ID查询章节列表
     * 
     * @param courseId 课程ID
     * @return 章节集合
     */
    @Override
    public List<PsyCourseChapter> selectChaptersByCourseId(Long courseId) {
        return chapterMapper.selectChaptersByCourseId(courseId);
    }

    /**
     * 根据课程ID查询章节树结构
     * 
     * @param courseId 课程ID
     * @return 章节树结构
     */
    @Override
    public List<PsyCourseChapter> selectChapterTreeByCourseId(Long courseId) {
        return chapterMapper.selectChapterTreeByCourseId(courseId);
    }

    /**
     * 新增章节
     * 
     * @param chapter 章节信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertChapter(PsyCourseChapter chapter) {
        chapter.setCreateTime(DateUtils.getNowDate());
        chapter.setDelFlag(0);
        
        // 如果是一级章节，设置父ID为0
        if (chapter.getLevel() == 1) {
            chapter.setParentId(0L);
        }
        
        int result = chapterMapper.insertChapter(chapter);
        
        // 更新课程统计信息
        if (result > 0) {
            courseService.updateCourseStatistics(chapter.getCourseId());
        }
        
        return result;
    }

    /**
     * 修改章节
     * 
     * @param chapter 章节信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateChapter(PsyCourseChapter chapter) {
        chapter.setUpdateTime(DateUtils.getNowDate());
        
        int result = chapterMapper.updateChapter(chapter);
        
        // 更新课程统计信息
        if (result > 0) {
            courseService.updateCourseStatistics(chapter.getCourseId());
        }
        
        return result;
    }

    /**
     * 删除章节
     * 
     * @param id 章节ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteChapterById(Long id) {
        // 获取章节信息
        PsyCourseChapter chapter = chapterMapper.selectChapterById(id);
        if (chapter == null) {
            return 0;
        }
        
        // 删除章节
        int result = chapterMapper.deleteChapterById(id);
        
        // 更新课程统计信息
        if (result > 0) {
            courseService.updateCourseStatistics(chapter.getCourseId());
        }
        
        return result;
    }

    /**
     * 批量删除章节
     * 
     * @param ids 需要删除的章节ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteChapterByIds(Long[] ids) {
        // 获取第一个章节的课程ID
        if (ids.length > 0) {
            PsyCourseChapter chapter = chapterMapper.selectChapterById(ids[0]);
            if (chapter != null) {
                int result = chapterMapper.deleteChapterByIds(ids);
                
                // 更新课程统计信息
                if (result > 0) {
                    courseService.updateCourseStatistics(chapter.getCourseId());
                }
                
                return result;
            }
        }
        
        return chapterMapper.deleteChapterByIds(ids);
    }

    /**
     * 更新章节排序
     *
     * @param id 章节ID
     * @param chapterOrder 排序值
     * @return 结果
     */
    @Override
    public int updateChapterOrder(Long id, Integer chapterOrder) {
        return chapterMapper.updateChapterOrder(id, chapterOrder);
    }

    /**
     * 获取所有章节（仅返回id、父id、名称）
     *
     * @return 章节集合
     */
    @Override
    public List<PsyCourseChapter> selectAllChapters() {
        return chapterMapper.selectAllChapters();
    }
}
