package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;

/**
 * 咨询师实际排班Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTimeCounselorScheduleMapper {
    
    /**
     * 查询排班列表
     * 
     * @param schedule 排班记录
     * @return 排班记录集合
     */
    List<PsyTimeCounselorSchedule> selectScheduleList(PsyTimeCounselorSchedule schedule);
    
    /**
     * 根据ID查询排班记录
     * 
     * @param id 排班记录主键
     * @return 排班记录
     */
    PsyTimeCounselorSchedule selectScheduleById(Long id);
    
    /**
     * 根据咨询师和日期查询排班
     * 
     * @param counselorId 咨询师ID
     * @param scheduleDate 排班日期
     * @return 排班记录
     */
    PsyTimeCounselorSchedule selectScheduleByCounselorAndDate(
        @Param("counselorId") Long counselorId,
        @Param("scheduleDate") LocalDate scheduleDate
    );
    
    /**
     * 查询咨询师在日期范围内的排班
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 排班记录集合
     */
    List<PsyTimeCounselorSchedule> selectSchedulesByDateRange(
        @Param("counselorId") Long counselorId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );
    
    /**
     * 查询指定日期的所有咨询师排班
     * 
     * @param scheduleDate 排班日期
     * @param centerId 咨询中心ID
     * @return 排班记录集合
     */
    List<PsyTimeCounselorSchedule> selectSchedulesByDate(
        @Param("scheduleDate") LocalDate scheduleDate,
        @Param("centerId") Long centerId
    );
    
    /**
     * 新增排班记录
     * 
     * @param schedule 排班记录
     * @return 结果
     */
    int insertSchedule(PsyTimeCounselorSchedule schedule);
    
    /**
     * 批量新增排班记录
     * 
     * @param schedules 排班记录列表
     * @return 结果
     */
    int batchInsertSchedules(@Param("schedules") List<PsyTimeCounselorSchedule> schedules);
    
    /**
     * 修改排班记录
     * 
     * @param schedule 排班记录
     * @return 结果
     */
    int updateSchedule(PsyTimeCounselorSchedule schedule);
    
    /**
     * 删除排班记录
     * 
     * @param id 排班记录主键
     * @return 结果
     */
    int deleteScheduleById(Long id);
    
    /**
     * 批量删除排班记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteScheduleByIds(Long[] ids);
    
    /**
     * 删除指定日期范围的排班
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 结果
     */
    int deleteSchedulesByDateRange(
        @Param("counselorId") Long counselorId,
        @Param("startDate") LocalDate startDate,
        @Param("endDate") LocalDate endDate
    );
    
    /**
     * 检查排班是否存在
     * 
     * @param counselorId 咨询师ID
     * @param scheduleDate 排班日期
     * @return 排班记录数量
     */
    int checkScheduleExists(
        @Param("counselorId") Long counselorId,
        @Param("scheduleDate") LocalDate scheduleDate
    );
}
