package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyTabbarMenu;

import java.util.List;

/**
 * 心理咨询移动端导航菜单Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTabbarMenuService {
    /**
     * 查询菜单列表
     * 
     * @param menu 菜单信息
     * @return 菜单集合
     */
    public List<PsyTabbarMenu> selectMenuList(PsyTabbarMenu menu);

    /**
     * 根据权限查询菜单列表
     * 
     * @param permissions 权限列表
     * @return 菜单集合
     */
    public List<PsyTabbarMenu> selectMenuByPermissions(List<String> permissions);

    /**
     * 新增菜单
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public int insertMenu(PsyTabbarMenu menu);

    /**
     * 修改菜单
     * 
     * @param menu 菜单信息
     * @return 结果
     */
    public int updateMenu(PsyTabbarMenu menu);

    /**
     * 批量删除菜单
     * 
     * @param ids 需要删除的菜单ID
     * @return 结果
     */
    public int deleteMenuByIds(Long[] ids);

    /**
     * 删除菜单信息
     * 
     * @param id 菜单ID
     * @return 结果
     */
    public int deleteMenuById(Long id);
} 