<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentQuestionMapper">

    <!-- 结果映射 -->
    <resultMap id="QuestionResultMap" type="PsyAssessmentQuestion">
        <id property="id" column="id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="questionNo" column="question_no"/>
        <result property="questionText" column="question_text"/>
        <result property="questionType" column="question_type"/>
        <result property="isRequired" column="is_required"/>
        <result property="scoreType" column="score_type"/>
        <result property="dimension" column="dimension"/>
        <result property="orderNum" column="order_num"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 带选项的结果映射 -->
    <resultMap id="QuestionWithOptionsMap" type="PsyAssessmentQuestion" extends="QuestionResultMap">
        <collection property="options" ofType="PsyAssessmentOption" column="id" select="com.xihuan.system.mapper.PsyAssessmentOptionMapper.selectOptionsByQuestionId"/>
    </resultMap>

    <!-- 查询题目列表 -->
    <select id="selectQuestionList" parameterType="PsyAssessmentQuestion" resultMap="QuestionResultMap">
        SELECT * FROM psy_t_question
        WHERE del_flag = 0
        <if test="scaleId != null">
            AND scale_id = #{scaleId}
        </if>
        <if test="questionType != null">
            AND question_type = #{questionType}
        </if>
        <if test="dimension != null and dimension != ''">
            AND dimension = #{dimension}
        </if>
        <if test="questionText != null and questionText != ''">
            AND question_text LIKE CONCAT('%', #{questionText}, '%')
        </if>
        ORDER BY scale_id, order_num, question_no
    </select>

    <!-- 根据ID查询题目 -->
    <select id="selectQuestionById" parameterType="Long" resultMap="QuestionResultMap">
        SELECT * FROM psy_t_question WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询题目详情（包含选项信息） -->
    <select id="selectQuestionWithOptions" parameterType="Long" resultMap="QuestionWithOptionsMap">
        SELECT * FROM psy_t_question WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据量表ID查询题目列表 -->
    <select id="selectQuestionsByScaleId" parameterType="Long" resultMap="QuestionResultMap">
        SELECT * FROM psy_t_question 
        WHERE scale_id = #{scaleId} AND del_flag = 0
        ORDER BY order_num, question_no
    </select>

    <!-- 根据量表ID查询题目列表（包含选项信息） -->
    <select id="selectQuestionsWithOptionsByScaleId" parameterType="Long" resultMap="QuestionWithOptionsMap">
        SELECT * FROM psy_t_question 
        WHERE scale_id = #{scaleId} AND del_flag = 0
        ORDER BY order_num, question_no
    </select>

    <!-- 新增题目 -->
    <insert id="insertQuestion" parameterType="PsyAssessmentQuestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_question (
            scale_id, question_no, question_text, question_type, is_required, score_type,
            dimension, order_num, del_flag, create_by, create_time, update_by, update_time
        ) VALUES (
            #{scaleId}, #{questionNo}, #{questionText}, #{questionType}, #{isRequired}, #{scoreType},
            #{dimension}, #{orderNum}, #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate()
        )
    </insert>

    <!-- 修改题目 -->
    <update id="updateQuestion" parameterType="PsyAssessmentQuestion">
        UPDATE psy_t_question
        <set>
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="questionNo != null">question_no = #{questionNo},</if>
            <if test="questionText != null">question_text = #{questionText},</if>
            <if test="questionType != null">question_type = #{questionType},</if>
            <if test="isRequired != null">is_required = #{isRequired},</if>
            <if test="scoreType != null">score_type = #{scoreType},</if>
            <if test="dimension != null">dimension = #{dimension},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除题目 -->
    <update id="deleteQuestionById" parameterType="Long">
        UPDATE psy_t_question SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除题目 -->
    <update id="deleteQuestionByIds" parameterType="Long">
        UPDATE psy_t_question SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据量表ID删除题目 -->
    <update id="deleteQuestionsByScaleId" parameterType="Long">
        UPDATE psy_t_question SET del_flag = 1 WHERE scale_id = #{scaleId}
    </update>

    <!-- 查询量表的题目数量 -->
    <select id="countQuestionsByScaleId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_question WHERE scale_id = #{scaleId} AND del_flag = 0
    </select>

    <!-- 查询量表的维度列表 -->
    <select id="selectDimensionsByScaleId" parameterType="Long" resultType="String">
        SELECT DISTINCT dimension FROM psy_t_question 
        WHERE scale_id = #{scaleId} AND del_flag = 0 AND dimension IS NOT NULL
        ORDER BY dimension
    </select>

    <!-- 根据维度查询题目列表 -->
    <select id="selectQuestionsByDimension" resultMap="QuestionResultMap">
        SELECT * FROM psy_t_question 
        WHERE scale_id = #{scaleId} AND dimension = #{dimension} AND del_flag = 0
        ORDER BY order_num, question_no
    </select>

    <!-- 查询下一题 -->
    <select id="selectNextQuestion" resultMap="QuestionResultMap">
        SELECT * FROM psy_t_question 
        WHERE scale_id = #{scaleId} AND question_no > #{currentQuestionNo} AND del_flag = 0
        ORDER BY question_no
        LIMIT 1
    </select>

    <!-- 查询上一题 -->
    <select id="selectPreviousQuestion" resultMap="QuestionResultMap">
        SELECT * FROM psy_t_question 
        WHERE scale_id = #{scaleId} AND question_no &lt; #{currentQuestionNo} AND del_flag = 0
        ORDER BY question_no DESC
        LIMIT 1
    </select>

    <!-- 批量插入题目 -->
    <insert id="batchInsertQuestions" parameterType="java.util.List">
        INSERT INTO psy_t_question (
            scale_id, question_no, question_text, question_type, is_required, score_type,
            dimension, order_num, del_flag, create_by, create_time, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.scaleId}, #{item.questionNo}, #{item.questionText}, #{item.questionType}, 
                #{item.isRequired}, #{item.scoreType}, #{item.dimension}, #{item.orderNum}, 
                #{item.delFlag}, #{item.createBy}, sysdate(), #{item.updateBy}, sysdate()
            )
        </foreach>
    </insert>

    <!-- 更新题目序号 -->
    <update id="updateQuestionNo">
        UPDATE psy_t_question SET question_no = #{questionNo} WHERE id = #{id}
    </update>

    <!-- 检查题目是否被使用 -->
    <select id="checkQuestionInUse" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_answer WHERE question_id = #{id} AND del_flag = 0
    </select>

    <!-- 查询题目统计信息 -->
    <select id="selectQuestionStats" parameterType="Long" resultType="java.util.Map">
        SELECT 
            q.id,
            q.question_no,
            q.question_text,
            q.dimension,
            COUNT(a.id) as answer_count,
            AVG(a.score) as avg_score
        FROM psy_t_question q
        LEFT JOIN psy_t_answer a ON q.id = a.question_id AND a.del_flag = 0
        WHERE q.scale_id = #{scaleId} AND q.del_flag = 0
        GROUP BY q.id, q.question_no, q.question_text, q.dimension
        ORDER BY q.order_num, q.question_no
    </select>
</mapper>
