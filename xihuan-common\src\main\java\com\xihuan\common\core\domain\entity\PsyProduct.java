package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.math.BigDecimal;
import java.util.List;

/**
 * 心理咨询产品表 psy_product
 */
@Data
public class PsyProduct extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 产品ID */
    private Long productId;

    /** 产品名称 */
    @NotBlank(message = "产品名称不能为空")
    private String productName;

    /** 头图地址 */
    private String productImage;

    /** 服务方式（字典类型） */
    private String serviceMethod;

    /** 服务保障（字典类型） */
    private String serviceGuarantee;

    /** 服务方向 */
    private String serviceDirection;

    /** 服务方向类型 */
    private String serviceDirectionType;

    /** 咨询师等级 */
    private String consultantGrade;

    /** 服务时长（字典类型） */
    private String serviceDuration;

    /** 图文详情 */
    private String graphicDetails;

    /** 补充说明 */
    private String supplementInfo;

    /** 原价 */
    private BigDecimal originalPrice;

    /** 折扣价 */
    private BigDecimal discountPrice;

    /** 折扣率 */
    private BigDecimal discountRate;

    /** 适用门店IDS(逗号分隔) */
    private String applicableStores;

    /** 有效期 */
    private String validityPeriod;

    /** 不可用日期(JSON数组) */
    private String unavailableDates;

    /** 需预约(0否 1是) */
    private String needAppointment;

    /** 单人购买上限 */
    private Integer singlePurchaseLimit;

    /** 适用年龄范围 */
    private String ageRange;

    /** 禁用状态（0启用 1禁用） */
    private String disableFlag;

    /** 删除标志 */
    private String delFlag;

    /** 包含的服务项目列表 */
    private List<PsyServiceItem> serviceItems;

    /** 所属分类列表 */
    private List<PsyCategory> categories;

    /** 分类ID(查询用) */
    private Long categoryId;
}