package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessDays;
import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessHours;
import com.xihuan.common.core.domain.entity.store.PsyStoreContacts;
import com.xihuan.common.core.domain.entity.store.PsyStores;

import java.util.List;

/**
 * 心理咨询门店Service接口
 */
public interface IPsyStoresService {
    /**
     * 查询门店列表
     * 
     * @param psyStores 门店信息
     * @return 门店集合
     */
    List<PsyStores> selectPsyStoresList(PsyStores psyStores);

    /**
     * 查询门店信息
     * 
     * @param id 门店主键
     * @return 门店信息
     */
    PsyStores selectPsyStoresById(Long id);

    /**
     * 新增门店
     * 
     * @param psyStores 门店信息
     * @param contacts 联系方式列表
     * @param businessDays 营业日配置
     * @param businessHours 营业时间段列表
     * @return 结果
     */
    int insertPsyStores(PsyStores psyStores, List<PsyStoreContacts> contacts,
                        PsyStoreBusinessDays businessDays, List<PsyStoreBusinessHours> businessHours);

    /**
     * 修改门店
     * 
     * @param psyStores 门店信息
     * @param contacts 联系方式列表
     * @param businessDays 营业日配置
     * @param businessHours 营业时间段列表
     * @return 结果
     */
    int updatePsyStores(PsyStores psyStores, List<PsyStoreContacts> contacts, 
                       PsyStoreBusinessDays businessDays, List<PsyStoreBusinessHours> businessHours);

    /**
     * 删除门店
     * 
     * @param id 门店主键
     * @return 结果
     */
    int deletePsyStoresById(Long id);

    /**
     * 批量删除门店
     * 
     * @param ids 需要删除的门店主键集合
     * @return 结果
     */
    int deletePsyStoresByIds(Long[] ids);

    /**
     * 获取门店联系方式列表
     * 
     * @param storeId 门店ID
     * @return 联系方式列表
     */
    List<PsyStoreContacts> getStoreContacts(Long storeId);

    /**
     * 获取门店营业日配置
     * 
     * @param storeId 门店ID
     * @return 营业日配置
     */
    PsyStoreBusinessDays getStoreBusinessDays(Long storeId);

    /**
     * 获取门店营业时间段列表
     * 
     * @param storeId 门店ID
     * @return 营业时间段列表
     */
    List<PsyStoreBusinessHours> getStoreBusinessHours(Long storeId);

    /**
     * 更新门店状态
     * 
     * @param id 门店ID
     * @param status 状态
     * @return 结果
     */
    int updatePsyStoresStatus(Long id, String status);

} 