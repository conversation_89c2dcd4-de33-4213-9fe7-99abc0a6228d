<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyUserFavoriteMapper">
    
    <resultMap type="PsyUserFavorite" id="PsyUserFavoriteResult">
        <id     property="favoriteId"      column="favorite_id"      />
        <result property="userId"          column="user_id"          />
        <result property="targetType"      column="target_type"      />
        <result property="targetId"        column="target_id"        />
        <result property="targetTitle"     column="target_title"     />
        <result property="targetImage"     column="target_image"     />
        <result property="sort"            column="sort"             />
        <result property="tags"            column="tags"             />
        <result property="notes"           column="notes"            />
        <result property="isPublic"        column="is_public"        />
        <result property="favoriteTime"    column="favorite_time"    />
        <result property="lastViewTime"    column="last_view_time"   />
        <result property="viewCount"       column="view_count"       />
        <result property="delFlag"         column="del_flag"         />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
        <!-- 兼容旧版本字段 -->
        <result property="counselorId"     column="counselor_id"     />
        <result property="productId"       column="product_id"       />
    </resultMap>
    
    <sql id="selectPsyUserFavoriteVo">
        select favorite_id, user_id, target_type, target_id, target_title, target_image,
        sort, tags, notes, is_public, favorite_time, last_view_time, view_count,
        del_flag, create_by, create_time, update_by, update_time, remark
        from psy_user_favorite
    </sql>
    
    <select id="selectFavoriteList" parameterType="PsyUserFavorite" resultMap="PsyUserFavoriteResult">
        <include refid="selectPsyUserFavoriteVo"/>
        <where>
            del_flag = '0'
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="targetType != null">
                AND target_type = #{targetType}
            </if>
            <if test="targetId != null">
                AND target_id = #{targetId}
            </if>
            <if test="isPublic != null">
                AND is_public = #{isPublic}
            </if>
            <if test="tags != null and tags != ''">
                AND tags LIKE CONCAT('%', #{tags}, '%')
            </if>
        </where>
        ORDER BY sort ASC, favorite_time DESC
    </select>

    <select id="selectFavoriteById" parameterType="Long" resultMap="PsyUserFavoriteResult">
        <include refid="selectPsyUserFavoriteVo"/>
        WHERE favorite_id = #{favoriteId} AND del_flag = '0'
    </select>
    
    <select id="checkFavorite" resultMap="PsyUserFavoriteResult">
        <include refid="selectPsyUserFavoriteVo"/>
        WHERE del_flag = '0'
        AND user_id = #{userId}
        AND target_type = #{targetType}
        AND target_id = #{targetId}
        LIMIT 1
    </select>

    <!-- 兼容旧版本的检查方法 -->
    <select id="checkFavoriteOld" resultMap="PsyUserFavoriteResult">
        <include refid="selectPsyUserFavoriteVo"/>
        <where>
            del_flag = '0'
            AND user_id = #{userId}
            AND target_type = #{targetType}
            <choose>
                <when test="targetType == 1">
                    AND target_id = #{counselorId}
                </when>
                <when test="targetType == 2">
                    AND target_id = #{productId}
                </when>
            </choose>
        </where>
        LIMIT 1
    </select>
    
    <insert id="insertFavorite" parameterType="PsyUserFavorite" useGeneratedKeys="true" keyProperty="favoriteId">
        INSERT INTO psy_user_favorite (
            user_id, target_type, target_id,
            <if test="targetTitle != null and targetTitle != ''">target_title,</if>
            <if test="targetImage != null and targetImage != ''">target_image,</if>
            <if test="sort != null">sort,</if>
            <if test="tags != null and tags != ''">tags,</if>
            <if test="notes != null and notes != ''">notes,</if>
            <if test="isPublic != null">is_public,</if>
            favorite_time,
            <if test="createBy != null and createBy != ''">create_by,</if>
            create_time
        ) VALUES (
            #{userId}, #{targetType}, #{targetId},
            <if test="targetTitle != null and targetTitle != ''">#{targetTitle},</if>
            <if test="targetImage != null and targetImage != ''">#{targetImage},</if>
            <if test="sort != null">#{sort},</if>
            <if test="tags != null and tags != ''">#{tags},</if>
            <if test="notes != null and notes != ''">#{notes},</if>
            <if test="isPublic != null">#{isPublic},</if>
            NOW(),
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            NOW()
        )
    </insert>

    <update id="updateFavorite" parameterType="PsyUserFavorite">
        UPDATE psy_user_favorite
        <trim prefix="SET" suffixOverrides=",">
            <if test="targetTitle != null">target_title = #{targetTitle},</if>
            <if test="targetImage != null">target_image = #{targetImage},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </trim>
        WHERE favorite_id = #{favoriteId}
    </update>
    
    <update id="deleteFavoriteByIds" parameterType="Long[]">
        UPDATE psy_user_favorite SET del_flag = '2'
        WHERE favorite_id IN
        <foreach collection="array" item="favoriteId" open="(" separator="," close=")">
            #{favoriteId}
        </foreach>
    </update>

    <!-- 查询收藏详情（包含目标对象信息） -->
    <select id="selectFavoriteWithDetails" parameterType="PsyUserFavorite" resultType="Map">
        SELECT
            f.favorite_id,
            f.user_id,
            f.target_type,
            f.target_id,
            f.target_title,
            f.target_image,
            f.sort,
            f.tags,
            f.notes,
            f.is_public,
            f.favorite_time,
            f.last_view_time,
            f.view_count,
            CASE f.target_type
                WHEN 1 THEN '咨询师'
                WHEN 2 THEN '课程'
                WHEN 3 THEN '冥想'
                WHEN 4 THEN '测评'
                ELSE '未知'
            END as target_type_name
        FROM psy_user_favorite f
        <where>
            f.del_flag = '0'
            <if test="userId != null">
                AND f.user_id = #{userId}
            </if>
            <if test="targetType != null">
                AND f.target_type = #{targetType}
            </if>
            <if test="isPublic != null">
                AND f.is_public = #{isPublic}
            </if>
        </where>
        ORDER BY f.sort ASC, f.favorite_time DESC
    </select>

    <!-- 更新查看次数 -->
    <update id="updateViewCount" parameterType="Long">
        UPDATE psy_user_favorite
        SET view_count = view_count + 1,
            last_view_time = NOW()
        WHERE favorite_id = #{favoriteId}
    </update>

    <!-- 查询用户收藏统计 -->
    <select id="selectUserFavoriteStats" parameterType="Long" resultType="Map">
        SELECT
            COUNT(*) as total_count,
            COUNT(CASE WHEN target_type = 1 THEN 1 END) as consultant_count,
            COUNT(CASE WHEN target_type = 2 THEN 1 END) as course_count,
            COUNT(CASE WHEN target_type = 3 THEN 1 END) as meditation_count,
            COUNT(CASE WHEN target_type = 4 THEN 1 END) as assessment_count,
            COUNT(CASE WHEN is_public = 1 THEN 1 END) as public_count,
            SUM(view_count) as total_views
        FROM psy_user_favorite
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <!-- 查询目标被收藏次数 -->
    <select id="countFavoriteByTarget" resultType="int">
        SELECT COUNT(*)
        FROM psy_user_favorite
        WHERE target_type = #{targetType}
        AND target_id = #{targetId}
        AND del_flag = '0'
    </select>

    <!-- 查询用户收藏的目标ID列表 -->
    <select id="selectUserFavoriteTargetIds" resultType="Long">
        SELECT target_id
        FROM psy_user_favorite
        WHERE user_id = #{userId}
        AND target_type = #{targetType}
        AND del_flag = '0'
        ORDER BY favorite_time DESC
    </select>

</mapper>