package com.xihuan.framework.websocket;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyMessage;
import com.xihuan.common.core.domain.entity.PsyMessageConversation;
import com.xihuan.system.service.wxService.IPsyMessageConversationService;
import com.xihuan.system.service.wxService.IPsyMessageService;
import com.xihuan.system.service.wxService.IPsyMessageStatusService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;

/**
 * WebSocket消息处理器
 */
@Component
public class WebSocketMessageHandler {
    private static final Logger log = LoggerFactory.getLogger(WebSocketMessageHandler.class);

    /**
     * 管理员ID
     */
    private static final Long ADMIN_ID = 1L;

    @Autowired
    private IPsyMessageService messageService;

    @Autowired
    private IPsyMessageConversationService conversationService;
    
    @Autowired
    private IPsyMessageStatusService messageStatusService;

    /**
     * 处理接收到的消息
     */
    public void handleMessage(String messageContent, Long userId) {
        try {
            JSONObject jsonObject = JSON.parseObject(messageContent);
            String type = jsonObject.getString("type");
            
            switch (type) {
                case "chat":
                    handleChatMessage(jsonObject, userId);
                    break;
                case "read":
                    handleReadMessage(jsonObject, userId);
                    break;
                case "withdraw":
                    handleWithdrawMessage(jsonObject, userId);
                    break;
                case "admin_reply":
                    // 只有管理员可以使用此功能
                    if (isAdmin(userId)) {
                        handleAdminReplyMessage(jsonObject, userId);
                    } else {
                        log.warn("非管理员用户{}尝试使用管理员回复功能", userId);
                    }
                    break;
                default:
                    log.warn("未知消息类型: {}", type);
            }
        } catch (Exception e) {
            log.error("处理消息失败", e);
        }
    }

    /**
     * 判断是否为管理员
     */
    private boolean isAdmin(Long userId) {
        return ADMIN_ID.equals(userId);
    }

    /**
     * 处理聊天消息
     */
    private void handleChatMessage(JSONObject jsonObject, Long senderId) {
        Long receiverId = jsonObject.getLong("receiverId");
        Long conversationId = jsonObject.getLong("conversationId");
        String content = jsonObject.getString("content");
        String messageType = jsonObject.getString("messageType");
        String fileUrl = jsonObject.getString("fileUrl");
        String type = jsonObject.getString("type");
        Boolean isUser = jsonObject.getBoolean("isUser");

        // 创建消息对象
        PsyMessage message = new PsyMessage();
        message.setSenderId(senderId);
        message.setReceiverId(receiverId);
        message.setConversationId(conversationId);
        message.setContent(content);
        message.setMessageType(messageType);
        message.setFileUrl(fileUrl);
        message.setSendTime(new Date());
        message.setIsWithdrawn("0");
        message.setIsUser(isUser);

        // 保存消息
        PsyMessage savedMessage = messageService.sendMessage(message);
        
        // 更新会话最后消息
        conversationService.updateLastMessage(conversationId, senderId, content);
        
        // 更新未读计数
//        boolean isUserReceiver = receiverId != senderId;
//        conversationService.incrementUnreadCount(conversationId, isUserReceiver);
        
        // 发送消息给接收者
        if (WebSocketServer.isOnline(receiverId)) {
            savedMessage.setType(type);
            WebSocketServer.sendMessage(receiverId, savedMessage);
        }
        
        // 如果不是管理员发送的消息，也发送给管理员
        if (!isAdmin(senderId) && WebSocketServer.isOnline(ADMIN_ID)) {
            savedMessage.setType(type);
            WebSocketServer.sendMessage(ADMIN_ID, savedMessage);
        }
    }

    /**
     * 处理管理员代咨询师回复消息
     */
    private void handleAdminReplyMessage(JSONObject jsonObject, Long adminId) {
        Long consultantId = jsonObject.getLong("consultantId"); // 代表哪个咨询师回复
        Long userId = jsonObject.getLong("userId"); // 回复给哪个用户
        Long conversationId = jsonObject.getLong("conversationId");
        String content = jsonObject.getString("content");
        String messageType = jsonObject.getString("messageType");
        String fileUrl = jsonObject.getString("fileUrl");
        Boolean isUser = jsonObject.getBoolean("isUser");

        // 检查会话是否存在
        PsyMessageConversation conversation = conversationService.getConversation(conversationId);
        if (conversation == null) {
            log.error("会话不存在: {}", conversationId);
            return;
        }

        // 验证consultantId是否匹配会话中的咨询师
        if (!consultantId.equals(conversation.getConsultantId())) {
            log.error("咨询师ID与会话不匹配, 会话ID: {}, 咨询师ID: {}, 会话中的咨询师ID: {}", 
                    conversationId, consultantId, conversation.getConsultantId());
            return;
        }

        // 创建消息对象，发送者显示为咨询师ID
        PsyMessage message = new PsyMessage();
        message.setSenderId(consultantId); // 使用咨询师ID作为发送者
        message.setReceiverId(userId);
        message.setConversationId(conversationId);
        message.setContent(content);
        message.setMessageType(messageType);
        message.setFileUrl(fileUrl);
        message.setSendTime(new Date());
        message.setIsWithdrawn("0");
        message.setIsUser(isUser);

        // 保存消息
        PsyMessage savedMessage = messageService.sendMessage(message);
        
        // 更新会话最后消息
        conversationService.updateLastMessage(conversationId, consultantId, content);
        
        // 更新用户未读计数
//        conversationService.incrementUnreadCount(conversationId, true);
        
        // 发送消息给用户
        if (WebSocketServer.isOnline(userId)) {
            WebSocketServer.sendMessage(userId, savedMessage);
        }
        
        // 如果咨询师在线，也发送给咨询师
        if (WebSocketServer.isOnline(consultantId)) {
            WebSocketServer.sendMessage(consultantId, savedMessage);
        }
        
        // 给管理员发送确认消息
        JSONObject confirmMessage = new JSONObject();
        confirmMessage.put("type", "admin_reply_confirm");
        confirmMessage.put("messageId", savedMessage.getMessageId());
        confirmMessage.put("conversationId", conversationId);
        confirmMessage.put("userId", userId);
        confirmMessage.put("consultantId", consultantId);
        confirmMessage.put("timestamp", System.currentTimeMillis());
        
        WebSocketServer.sendTextMessage(adminId, "admin_reply_confirm", "代咨询师回复成功");
        
        log.info("管理员{}代咨询师{}回复用户{}成功，消息ID: {}", 
                adminId, consultantId, userId, savedMessage.getMessageId());
    }

    /**
     * 处理消息已读
     */
    private void handleReadMessage(JSONObject jsonObject, Long userId) {
        Long messageId = jsonObject.getLong("messageId");
        Long conversationId = jsonObject.getLong("conversationId");
        
        // 更新消息已读状态
        messageService.markAsRead(messageId, userId);
        
        // 重置未读计数
        boolean isUser = jsonObject.getBoolean("isUser");
        conversationService.resetUnreadCount(conversationId, isUser);
        
        // 通知发送者消息已读
        PsyMessage message = messageService.getMessage(messageId);
        if (message != null && WebSocketServer.isOnline(message.getSenderId())) {
            JSONObject readNotification = new JSONObject();
            readNotification.put("type", "read_notification");
            readNotification.put("messageId", messageId);
            readNotification.put("readerId", userId);
            
            PsyMessage notification = new PsyMessage();
            notification.setContent(readNotification.toJSONString());
            notification.setMessageType("system");
            
            WebSocketServer.sendMessage(message.getSenderId(), notification);
        }
    }

    /**
     * 处理撤回消息
     */
    private void handleWithdrawMessage(JSONObject jsonObject, Long userId) {
        Long messageId = jsonObject.getLong("messageId");
        PsyMessage message = messageService.getMessage(messageId);
        
        if (message == null) {
            sendErrorMessage(userId, "消息不存在");
            return;
        }

        // 管理员可以撤回任何消息，其他用户需要通过服务层验证
        if (isAdmin(userId)) {
            message.setIsWithdrawn("1");
            messageService.sendMessage(message);
        } else {
            AjaxResult result = messageService.withdrawMessage(messageId, userId);
            if (result.isError()) {
                String errorMessage = (String) result.get("msg");
                sendErrorMessage(userId, errorMessage);
                return;
            }
        }

        // 创建撤回通知
        JSONObject withdrawNotification = new JSONObject();
        withdrawNotification.put("type", "withdraw_notification");
        withdrawNotification.put("messageId", messageId);
        
        PsyMessage notification = new PsyMessage();
        notification.setContent(withdrawNotification.toJSONString());
        notification.setMessageType("system");
        
        // 发送撤回通知给接收者
        WebSocketServer.sendMessage(message.getReceiverId(), notification);
        
        // 发送撤回通知给发送者（如果不是发送者撤回的）
        if (!userId.equals(message.getSenderId())) {
            WebSocketServer.sendMessage(message.getSenderId(), notification);
        }
        
        // 发送撤回通知给管理员（如果不是管理员撤回的）
        if (!isAdmin(userId) && WebSocketServer.isOnline(ADMIN_ID)) {
            WebSocketServer.sendMessage(ADMIN_ID, notification);
        }
    }

    /**
     * 发送错误消息给用户
     */
    private void sendErrorMessage(Long userId, String errorMessage) {
        JSONObject errorNotification = new JSONObject();
        errorNotification.put("type", "error_notification");
        errorNotification.put("message", errorMessage);
        
        PsyMessage notification = new PsyMessage();
        notification.setContent(errorNotification.toJSONString());
        notification.setMessageType("system");
        
        WebSocketServer.sendMessage(userId, notification);
    }
} 