package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyConsultantInterruption;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 咨询中断记录表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyConsultantInterruptionMapper {
    
    /**
     * 查询中断记录列表
     * 
     * @param interruption 中断记录信息
     * @return 中断记录集合
     */
    List<PsyConsultantInterruption> selectInterruptionList(PsyConsultantInterruption interruption);

    /**
     * 根据ID查询中断记录
     * 
     * @param id 中断记录ID
     * @return 中断记录信息
     */
    PsyConsultantInterruption selectInterruptionById(Long id);

    /**
     * 根据咨询记录ID查询中断记录列表
     * 
     * @param recordId 咨询记录ID
     * @return 中断记录集合
     */
    List<PsyConsultantInterruption> selectInterruptionsByRecordId(Long recordId);

    /**
     * 新增中断记录
     * 
     * @param interruption 中断记录信息
     * @return 结果
     */
    int insertInterruption(PsyConsultantInterruption interruption);

    /**
     * 修改中断记录
     * 
     * @param interruption 中断记录信息
     * @return 结果
     */
    int updateInterruption(PsyConsultantInterruption interruption);

    /**
     * 删除中断记录
     * 
     * @param id 中断记录ID
     * @return 结果
     */
    int deleteInterruptionById(Long id);

    /**
     * 批量删除中断记录
     * 
     * @param ids 需要删除的中断记录ID
     * @return 结果
     */
    int deleteInterruptionByIds(Long[] ids);

    /**
     * 根据咨询记录ID删除中断记录
     * 
     * @param recordId 咨询记录ID
     * @return 结果
     */
    int deleteInterruptionByRecordId(Long recordId);

    /**
     * 统计咨询记录的中断次数
     * 
     * @param recordId 咨询记录ID
     * @return 中断次数
     */
    int countInterruptionsByRecordId(Long recordId);

    /**
     * 统计咨询记录的总中断时长
     * 
     * @param recordId 咨询记录ID
     * @return 总中断时长(分钟)
     */
    int sumInterruptionDuration(Long recordId);

    /**
     * 根据中断类型统计
     * 
     * @param recordId 咨询记录ID
     * @param interruptType 中断类型
     * @return 中断次数
     */
    int countInterruptionsByType(@Param("recordId") Long recordId, @Param("interruptType") String interruptType);
}
