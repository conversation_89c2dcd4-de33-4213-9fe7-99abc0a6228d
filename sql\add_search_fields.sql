-- 为现有表添加搜索相关字段

-- 1. 咨询师表添加搜索字段 (psy_consultants)
ALTER TABLE `psy_consultants`
ADD COLUMN `search_keywords` text COMMENT '搜索关键词（姓名、专长、地区、标签等）',
ADD COLUMN `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数',
ADD COLUMN `view_count` int(11) DEFAULT 0 COMMENT '查看次数';

-- 2. 课程表添加搜索字段 (psy_course)
ALTER TABLE `psy_course`
ADD COLUMN `search_keywords` text COMMENT '搜索关键词（课程名、讲师、标签、简介等）',
ADD COLUMN `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数';
-- view_count 字段已存在，不需要添加

-- 3. 冥想表添加搜索字段 (psy_meditation)
ALTER TABLE `psy_meditation`
ADD COLUMN `search_keywords` text COMMENT '搜索关键词（标题、引导师、标签、描述等）',
ADD COLUMN `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数';
-- play_count 字段已存在，相当于view_count

-- 创建搜索相关索引
CREATE INDEX idx_psy_consultants_search ON psy_consultants(search_count, view_count);
CREATE INDEX idx_psy_course_search ON psy_course(search_count, view_count);
CREATE INDEX idx_psy_meditation_search ON psy_meditation(search_count, play_count);

-- 创建全文索引（MySQL 5.7+支持中文全文索引，可选）
-- ALTER TABLE `psy_consultants` ADD FULLTEXT(`name`, `personal_intro`, `search_keywords`);
-- ALTER TABLE `psy_course` ADD FULLTEXT(`title`, `summary`, `search_keywords`);
-- ALTER TABLE `psy_meditation` ADD FULLTEXT(`title`, `description`, `search_keywords`);

