package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantReview;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsyConsultantReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序咨询师评价管理Controller（咨询师端）
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/consultant/reviewManage")
public class MiniAppConsultantReviewManageController extends BaseController {
    
    @Autowired
    private IPsyConsultantReviewService reviewService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取咨询师的评价列表
     */
    @GetMapping("/myReviews")
    public AjaxResult getMyReviews(HttpServletRequest request) {
        try {
            // 获取当前咨询师
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            // 验证是否为咨询师
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantReview> reviews = reviewService.selectReviewsByConsultantId(consultantId);
            return success(reviews);
        } catch (Exception e) {
            logger.error("获取咨询师评价失败", e);
            return error("获取评价列表失败");
        }
    }

    /**
     * 获取已通过审核的评价
     */
    @GetMapping("/approved")
    public AjaxResult getApprovedReviews(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantReview> reviews = reviewService.selectApprovedReviews(consultantId);
            return success(reviews);
        } catch (Exception e) {
            logger.error("获取已审核评价失败", e);
            return error("获取评价列表失败");
        }
    }

    /**
     * 回复评价
     */
    @PostMapping("/reply/{reviewId}")
    public AjaxResult replyReview(@PathVariable Long reviewId,
                                @RequestParam String consultantReply,
                                HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 验证评价归属
            PsyConsultantReview review = reviewService.selectReviewById(reviewId);
            if (review == null || !review.getConsultantId().equals(loginUser.getUserId())) {
                return error("评价不存在或无权限操作");
            }
            
            int result = reviewService.replyReview(reviewId, consultantReply, loginUser.getUsername());
            return toAjax(result);
        } catch (Exception e) {
            logger.error("回复评价失败", e);
            return error("回复评价失败");
        }
    }

    /**
     * 获取评价详情
     */
    @GetMapping("/{reviewId}")
    public AjaxResult getReviewDetails(@PathVariable Long reviewId, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            PsyConsultantReview review = reviewService.selectReviewWithDetails(reviewId);
            if (review == null) {
                return error("评价不存在");
            }
            
            // 验证评价归属
            if (!review.getConsultantId().equals(loginUser.getUserId())) {
                return error("无权限查看该评价");
            }
            
            return success(review);
        } catch (Exception e) {
            logger.error("获取评价详情失败", e);
            return error("获取评价详情失败");
        }
    }

    /**
     * 获取评价统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getReviewStatistics(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            
            // 计算平均评分
            java.math.BigDecimal avgRating = reviewService.calculateAverageRating(consultantId);
            
            // 统计评价数量
            int totalCount = reviewService.countReviewsByConsultantId(consultantId);
            
            // 获取评分分布
            Map<String, Object> distribution = reviewService.getRatingDistribution(consultantId);
            
            Map<String, Object> statistics = new HashMap<>();
            statistics.put("avgRating", avgRating);
            statistics.put("totalCount", totalCount);
            statistics.put("distribution", distribution);
            
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取评价统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取最新评价
     */
    @GetMapping("/latest")
    public AjaxResult getLatestReviews(@RequestParam(defaultValue = "5") Integer limit, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantReview> allReviews = reviewService.selectReviewsByConsultantId(consultantId);
            
            // 限制返回数量
            List<PsyConsultantReview> latestReviews = allReviews.stream()
                .filter(review -> review.getAdminCheck() != null && review.getAdminCheck() == 1)
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
            
            return success(latestReviews);
        } catch (Exception e) {
            logger.error("获取最新评价失败", e);
            return error("获取最新评价失败");
        }
    }

    /**
     * 获取待回复的评价
     */
    @GetMapping("/pendingReply")
    public AjaxResult getPendingReplyReviews(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantReview> allReviews = reviewService.selectReviewsByConsultantId(consultantId);
            
            // 过滤待回复的评价（已审核通过但未回复）
            List<PsyConsultantReview> pendingReviews = allReviews.stream()
                .filter(review -> review.getAdminCheck() != null && review.getAdminCheck() == 1)
                .filter(review -> review.getConsultantReply() == null || review.getConsultantReply().trim().isEmpty())
                .collect(java.util.stream.Collectors.toList());
            
            return success(pendingReviews);
        } catch (Exception e) {
            logger.error("获取待回复评价失败", e);
            return error("获取待回复评价失败");
        }
    }

    /**
     * 获取高分评价
     */
    @GetMapping("/highRating")
    public AjaxResult getHighRatingReviews(@RequestParam(defaultValue = "4.0") Double minRating,
                                         @RequestParam(defaultValue = "10") Integer limit,
                                         HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantReview> allReviews = reviewService.selectReviewsByConsultantId(consultantId);
            
            // 过滤高分评价
            List<PsyConsultantReview> highRatingReviews = allReviews.stream()
                .filter(review -> review.getAdminCheck() != null && review.getAdminCheck() == 1)
                .filter(review -> review.getRating() != null && 
                    review.getRating().doubleValue() >= minRating)
                .limit(limit)
                .collect(java.util.stream.Collectors.toList());
            
            return success(highRatingReviews);
        } catch (Exception e) {
            logger.error("获取高分评价失败", e);
            return error("获取高分评价失败");
        }
    }

    /**
     * 获取评价类型统计
     */
    @GetMapping("/typeStatistics")
    public AjaxResult getReviewTypeStatistics(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantReview> reviews = reviewService.selectReviewsByConsultantId(consultantId);
            
            Map<String, Long> typeCount = new HashMap<>();
            typeCount.put("语音咨询", reviews.stream().filter(r -> "语音咨询".equals(r.getConsultType())).count());
            typeCount.put("视频咨询", reviews.stream().filter(r -> "视频咨询".equals(r.getConsultType())).count());
            typeCount.put("线下咨询", reviews.stream().filter(r -> "线下咨询".equals(r.getConsultType())).count());
            
            return success(typeCount);
        } catch (Exception e) {
            logger.error("获取评价类型统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取月度评价趋势
     */
    @GetMapping("/monthlyTrend")
    public AjaxResult getMonthlyReviewTrend(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultantReview> reviews = reviewService.selectReviewsByConsultantId(consultantId);
            
            // 按月份统计评价数量和平均评分
            Map<String, Map<String, Object>> monthlyData = new HashMap<>();
            
            reviews.stream()
                .filter(review -> review.getReviewTime() != null)
                .forEach(review -> {
                    java.time.LocalDate reviewDate = review.getReviewTime().toInstant()
                        .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                    String monthKey = reviewDate.getYear() + "-" + String.format("%02d", reviewDate.getMonthValue());
                    
                    monthlyData.computeIfAbsent(monthKey, k -> {
                        Map<String, Object> data = new HashMap<>();
                        data.put("count", 0L);
                        data.put("totalRating", 0.0);
                        return data;
                    });
                    
                    Map<String, Object> data = monthlyData.get(monthKey);
                    data.put("count", (Long) data.get("count") + 1);
                    if (review.getRating() != null) {
                        data.put("totalRating", (Double) data.get("totalRating") + review.getRating().doubleValue());
                    }
                });
            
            // 计算平均评分
            monthlyData.forEach((month, data) -> {
                Long count = (Long) data.get("count");
                Double totalRating = (Double) data.get("totalRating");
                if (count > 0) {
                    data.put("avgRating", totalRating / count);
                } else {
                    data.put("avgRating", 0.0);
                }
            });
            
            return success(monthlyData);
        } catch (Exception e) {
            logger.error("获取月度评价趋势失败", e);
            return error("获取趋势数据失败");
        }
    }

    /**
     * 检查是否为咨询师
     */
    private boolean isConsultant(LoginUser loginUser) {
        return loginUser.getUser().getDeptId() != null && 
               loginUser.getUser().getDeptId().equals(201L);
    }
}
