package com.xihuan.common.core.domain.entity.store;

import com.xihuan.common.core.domain.BaseEntity;
import com.xihuan.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店联系方式对象 psy_store_contacts
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyStoreContacts extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 关联门店ID */
    @Excel(name = "关联门店ID")
    private Long storeId;

    /** 客服手机号 */
    @Excel(name = "客服手机号")
    private String phone;

    /** 联系方式类型：DEFAULT-默认, BACKUP-备用 */
    @Excel(name = "联系方式类型", readConverterExp = "DEFAULT=默认,BACKUP=备用")
    private String contactType;
} 