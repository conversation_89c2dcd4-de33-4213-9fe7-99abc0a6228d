package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyCategory;

import java.util.List;

public interface PsyCategoryMapper {

    List<PsyCategory> selectCategoryList(PsyCategory category);

    PsyCategory selectCategoryById(Long categoryId);

    List<PsyCategory> selectChildrenByParentId(Long parentId);

    int insertCategory(PsyCategory category);

    int updateCategory(PsyCategory category);

    int deleteCategoryById(Long categoryId);

    List<PsyCategory> selectCategoryWithProducts(Long categoryId);

    /**
     * 校验分类唯一性
     */
    int checkCategoryUnique(PsyCategory category);

    /**
     * 检查分类是否已被使用
     */
    int checkExistProducts(Long categoryId);

    List<PsyCategory> selectCategoryListWithProducts();
}