package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 心理测评题目选项表对象 psy_t_option
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentOption extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 选项ID */
    @Excel(name = "选项ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 题目ID */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /** 选项内容 */
    @Excel(name = "选项内容")
    @NotBlank(message = "选项内容不能为空")
    @Size(max = 500, message = "选项内容不能超过500个字符")
    private String optionText;

    /** 选项值 */
    @Excel(name = "选项值")
    @Size(max = 50, message = "选项值不能超过50个字符")
    private String optionValue;

    /** 选项分值 */
    @Excel(name = "选项分值")
    private Integer score;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    @Min(value = 0, message = "显示顺序不能为负数")
    private Integer orderNum;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 题目信息 */
    private PsyAssessmentQuestion question;

    /** 是否被选中 */
    private Boolean selected;

    // 常量定义
    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 获取显示文本（选项值 + 选项内容）
     */
    public String getDisplayText() {
        if (optionValue != null && !optionValue.isEmpty()) {
            return optionValue + ". " + optionText;
        }
        return optionText;
    }
}
