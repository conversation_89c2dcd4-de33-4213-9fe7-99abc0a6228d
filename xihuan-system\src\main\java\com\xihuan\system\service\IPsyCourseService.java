package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyCourse;

import java.util.List;

/**
 * 心理咨询课程主表Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyCourseService {
    
    /**
     * 查询课程列表
     * 
     * @param course 课程信息
     * @return 课程集合
     */
    List<PsyCourse> selectCourseList(PsyCourse course);

    /**
     * 查询课程详情（包含章节、讲师、分类等信息）
     * 
     * @param id 课程ID
     * @return 课程详情
     */
    PsyCourse selectCourseWithDetails(Long id);

    /**
     * 根据ID查询课程
     * 
     * @param id 课程ID
     * @return 课程信息
     */
    PsyCourse selectCourseById(Long id);

    /**
     * 新增课程
     * 
     * @param course 课程信息
     * @return 结果
     */
    int insertCourse(PsyCourse course);

    /**
     * 修改课程
     * 
     * @param course 课程信息
     * @return 结果
     */
    int updateCourse(PsyCourse course);

    /**
     * 删除课程
     * 
     * @param id 课程ID
     * @return 结果
     */
    int deleteCourseById(Long id);

    /**
     * 批量删除课程
     * 
     * @param ids 需要删除的课程ID
     * @return 结果
     */
    int deleteCourseByIds(Long[] ids);

    /**
     * 根据分类ID查询课程列表
     * 
     * @param categoryId 分类ID
     * @return 课程集合
     */
    List<PsyCourse> selectCoursesByCategoryId(Long categoryId);

    /**
     * 根据讲师ID查询课程列表
     * 
     * @param instructorId 讲师ID
     * @return 课程集合
     */
    List<PsyCourse> selectCoursesByInstructorId(Long instructorId);

    /**
     * 更新课程统计信息
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    int updateCourseStatistics(Long courseId);

    /**
     * 更新课程评分信息
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    int updateCourseRating(Long courseId);

    /**
     * 增加课程观看次数
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    int incrementViewCount(Long courseId);

    /**
     * 增加课程销售数量
     * 
     * @param courseId 课程ID
     * @param count 增加数量
     * @return 结果
     */
    int incrementSalesCount(Long courseId, Integer count);

    /**
     * 发布课程
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    int publishCourse(Long courseId);

    /**
     * 下架课程
     * 
     * @param courseId 课程ID
     * @return 结果
     */
    int unpublishCourse(Long courseId);

    /**
     * 检查课程是否可以删除
     * 
     * @param courseId 课程ID
     * @return 是否可以删除
     */
    boolean canDeleteCourse(Long courseId);
}
