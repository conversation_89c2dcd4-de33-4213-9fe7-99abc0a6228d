# 微信小程序支付系统API文档

## 📋 概述

本文档描述了微信小程序支付系统的API接口，支持课程、冥想、咨询、测评等多种订单类型的支付功能。

## 🔗 基础信息

- **基础路径**: `/miniapp`
- **认证方式**: Bearer Token
- **数据格式**: JSON
- **支付方式**: 微信支付

## 📊 订单类型说明

| 订单类型 | 订单前缀 | 说明 |
|----------|----------|------|
| course | COURSE_ | 课程订单 |
| meditation | MEDITATION_ | 冥想订单 |
| consultant | CONSULTANT_ | 咨询订单 |
| assessment | ASSESSMENT_ | 测评订单 |

## 🛒 订单管理接口

### 1. 创建课程订单

**接口地址**: `POST /miniapp/order/course/create`

**请求参数**:
```json
{
  "productId": 123,
  "paymentAmount": 99.00,
  "originalPrice": 129.00,
  "couponId": 456,
  "couponDiscount": 20.00,
  "pointsUsed": 100,
  "pointsDiscount": 10.00
}
```

**参数说明**:
- `productId` (必填): 课程ID
- `paymentAmount` (必填): 实际支付金额
- `originalPrice` (必填): 原价
- `couponId` (可选): 优惠券ID
- `couponDiscount` (可选): 优惠券折扣金额
- `pointsUsed` (可选): 使用积分数量
- `pointsDiscount` (可选): 积分抵扣金额

**响应示例**:
```json
{
  "code": 200,
  "msg": "创建课程订单成功",
  "data": {
    "orderId": 789,
    "orderNo": "COURSE_20240115143025001",
    "userId": 123,
    "courseId": 123,
    "paymentAmount": 99.00,
    "originalPrice": 129.00,
    "status": 0,
    "createTime": "2024-01-15 14:30:25"
  }
}
```

### 2. 创建冥想订单

**接口地址**: `POST /miniapp/order/meditation/create`

**请求参数**:
```json
{
  "productId": 456,
  "paymentAmount": 29.00,
  "originalPrice": 39.00,
  "couponId": 789,
  "couponDiscount": 10.00
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "创建冥想订单成功",
  "data": {
    "orderId": 790,
    "orderNo": "MEDITATION_20240115143030001",
    "userId": 123,
    "meditationId": 456,
    "paymentAmount": 29.00,
    "originalPrice": 39.00,
    "status": 0,
    "createTime": "2024-01-15 14:30:30"
  }
}
```

## 💳 支付相关接口

### 1. 发起支付

**接口地址**: `POST /miniapp/order/pay`

**请求参数**:
```json
{
  "orderNo": "COURSE_20240115143025001"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "创建支付订单成功",
  "data": {
    "orderNo": "COURSE_20240115143025001",
    "amount": 99.00,
    "expireTime": "2024-01-15T14:35:25",
    "payParams": {
      "appId": "wx8df6fcafd17d7348",
      "timeStamp": "**********",
      "nonceStr": "abc123def456",
      "packageValue": "prepay_id=wx15143025001234567890",
      "signType": "RSA",
      "paySign": "signature_here"
    }
  }
}
```

**小程序调起支付示例**:
```javascript
const payParams = response.data.payParams;

wx.requestPayment({
  appId: payParams.appId,
  timeStamp: payParams.timeStamp,
  nonceStr: payParams.nonceStr,
  package: payParams.packageValue,
  signType: payParams.signType,
  paySign: payParams.paySign,
  success: function(res) {
    console.log('支付成功', res);
    // 跳转到支付成功页面
  },
  fail: function(res) {
    console.log('支付失败', res);
    // 处理支付失败
  }
});
```

### 2. 查询支付状态

**接口地址**: `GET /miniapp/order/pay/status/{orderNo}`

**路径参数**:
- `orderNo`: 订单号

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询支付状态成功",
  "data": {
    "outTradeNo": "COURSE_20240115143025001",
    "transactionId": "4200001234202401151234567890",
    "tradeState": "SUCCESS",
    "tradeStateDesc": "支付成功",
    "bankType": "CMC",
    "successTime": "2024-01-15T14:31:25+08:00",
    "payer": {
      "openid": "oUpF8uMuAJO_M2pxb1Q9zNjWeS6o"
    },
    "amount": {
      "total": 9900,
      "payerTotal": 9900,
      "currency": "CNY",
      "payerCurrency": "CNY"
    }
  }
}
```

### 3. 申请退款

**接口地址**: `POST /miniapp/order/refund`

**请求参数**:
```json
{
  "orderNo": "COURSE_20240115143025001",
  "refundAmount": 99.00,
  "reason": "用户主动申请退款"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "申请退款成功",
  "data": {
    "refundId": "50000001234567890123456789",
    "outRefundNo": "REFUND_COURSE_20240115143500001",
    "transactionId": "4200001234202401151234567890",
    "outTradeNo": "COURSE_20240115143025001",
    "channel": "ORIGINAL",
    "userReceivedAccount": "招商银行信用卡0403",
    "successTime": "2024-01-15T14:35:25+08:00",
    "createTime": "2024-01-15T14:35:00+08:00",
    "status": "SUCCESS",
    "amount": {
      "total": 9900,
      "refund": 9900,
      "payerTotal": 9900,
      "payerRefund": 9900,
      "currency": "CNY"
    }
  }
}
```

### 4. 取消订单

**接口地址**: `POST /miniapp/order/cancel/{orderNo}`

**路径参数**:
- `orderNo`: 订单号

**响应示例**:
```json
{
  "code": 200,
  "msg": "取消订单成功",
  "data": null
}
```

## 🔔 支付回调接口

### 1. 支付成功回调

**接口地址**: `POST /miniapp/payment/notify/pay`

**说明**: 此接口由微信支付系统调用，用于通知支付结果。

**请求头**:
```
Wechatpay-Timestamp: **********
Wechatpay-Nonce: abc123def456
Wechatpay-Signature: signature_here
Wechatpay-Serial: serial_no_here
Content-Type: application/json
```

**请求体**:
```json
{
  "id": "notification_id",
  "create_time": "2024-01-15T14:31:25+08:00",
  "event_type": "TRANSACTION.SUCCESS",
  "resource_type": "encrypt-resource",
  "resource": {
    "algorithm": "AEAD_AES_256_GCM",
    "ciphertext": "encrypted_data_here",
    "associated_data": "transaction",
    "nonce": "nonce_here"
  },
  "summary": "支付成功"
}
```

**响应**: 
- 成功: 返回 "SUCCESS"
- 失败: 返回 "FAIL"

### 2. 退款成功回调

**接口地址**: `POST /miniapp/payment/notify/refund`

**说明**: 此接口由微信支付系统调用，用于通知退款结果。

## 📱 前端集成示例

### 1. 创建订单并支付

```javascript
// 1. 创建订单
async function createOrder(productId, amount) {
  const response = await wx.request({
    url: '/miniapp/order/course/create',
    method: 'POST',
    header: {
      'Authorization': 'Bearer ' + getToken()
    },
    data: {
      productId: productId,
      paymentAmount: amount,
      originalPrice: amount
    }
  });
  
  if (response.data.code === 200) {
    return response.data.data.orderNo;
  } else {
    throw new Error(response.data.msg);
  }
}

// 2. 发起支付
async function payOrder(orderNo) {
  const response = await wx.request({
    url: '/miniapp/order/pay',
    method: 'POST',
    header: {
      'Authorization': 'Bearer ' + getToken()
    },
    data: {
      orderNo: orderNo
    }
  });
  
  if (response.data.code === 200) {
    const payParams = response.data.data.payParams;
    
    // 调起微信支付
    return new Promise((resolve, reject) => {
      wx.requestPayment({
        appId: payParams.appId,
        timeStamp: payParams.timeStamp,
        nonceStr: payParams.nonceStr,
        package: payParams.packageValue,
        signType: payParams.signType,
        paySign: payParams.paySign,
        success: resolve,
        fail: reject
      });
    });
  } else {
    throw new Error(response.data.msg);
  }
}

// 3. 完整的购买流程
async function buyProduct(productId, amount) {
  try {
    // 创建订单
    const orderNo = await createOrder(productId, amount);
    
    // 发起支付
    await payOrder(orderNo);
    
    // 支付成功，跳转到成功页面
    wx.navigateTo({
      url: '/pages/pay-success/index?orderNo=' + orderNo
    });
    
  } catch (error) {
    console.error('购买失败:', error);
    wx.showToast({
      title: '购买失败: ' + error.message,
      icon: 'none'
    });
  }
}
```

### 2. 查询支付状态

```javascript
async function checkPayStatus(orderNo) {
  const response = await wx.request({
    url: `/miniapp/order/pay/status/${orderNo}`,
    method: 'GET',
    header: {
      'Authorization': 'Bearer ' + getToken()
    }
  });
  
  if (response.data.code === 200) {
    const payStatus = response.data.data.tradeState;
    return payStatus === 'SUCCESS';
  }
  
  return false;
}
```

## ⚠️ 注意事项

### 1. 安全要求
- 所有接口都需要用户登录认证
- 支付回调接口需要验证微信签名
- 敏感信息（如商户密钥）不能暴露在前端

### 2. 错误处理
- 网络异常时需要重试机制
- 支付失败时需要给用户明确提示
- 订单状态异常时需要同步处理

### 3. 用户体验
- 支付过程中显示加载状态
- 支付成功后及时跳转和提示
- 支持订单查询和退款功能

### 4. 配置要求
- 需要在微信商户平台配置回调地址
- 需要上传商户证书文件
- 需要配置正确的APPID和商户号

这个支付系统提供了完整的微信小程序支付功能，支持多种订单类型，具有良好的安全性和用户体验。
