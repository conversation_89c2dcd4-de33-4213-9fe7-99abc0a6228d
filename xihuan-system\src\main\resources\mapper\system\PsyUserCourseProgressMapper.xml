<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyUserCourseProgressMapper">

    <resultMap id="BaseResultMap" type="PsyUserCourseProgress">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="course_id" property="courseId"/>
        <result column="chapter_id" property="chapterId"/>
        <result column="progress_percent" property="progressPercent"/>
        <result column="last_position" property="lastPosition"/>
        <result column="is_completed" property="isCompleted"/>
        <result column="study_time" property="studyTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="ProgressWithDetailsMap" type="PsyUserCourseProgress" extends="BaseResultMap">
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
        </association>
        <association property="course" javaType="PsyCourse">
            <id column="course_id" property="id"/>
            <result column="course_title" property="title"/>
        </association>
        <association property="chapter" javaType="PsyCourseChapter">
            <id column="chapter_id" property="id"/>
            <result column="chapter_title" property="chapterTitle"/>
            <result column="duration" property="duration"/>
        </association>
    </resultMap>

    <select id="selectProgressList" parameterType="PsyUserCourseProgress" resultMap="BaseResultMap">
        SELECT * FROM psy_user_course_progress
        <where>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="courseId != null">AND course_id = #{courseId}</if>
            <if test="chapterId != null">AND chapter_id = #{chapterId}</if>
            <if test="isCompleted != null">AND is_completed = #{isCompleted}</if>
        </where>
        ORDER BY update_time DESC
    </select>

    <select id="selectProgressById" resultMap="BaseResultMap">
        SELECT * FROM psy_user_course_progress WHERE id = #{id}
    </select>

    <select id="selectProgressByUserAndChapter" resultMap="BaseResultMap">
        SELECT * FROM psy_user_course_progress 
        WHERE user_id = #{userId} AND chapter_id = #{chapterId}
    </select>

    <select id="selectProgressByUserAndCourse" resultMap="BaseResultMap">
        SELECT * FROM psy_user_course_progress 
        WHERE user_id = #{userId} AND course_id = #{courseId}
        ORDER BY update_time DESC
    </select>

    <select id="selectProgressByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_user_course_progress 
        WHERE user_id = #{userId}
        ORDER BY update_time DESC
    </select>

    <insert id="insertProgress" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_user_course_progress (
            user_id, course_id, chapter_id, progress_percent, last_position,
            is_completed, study_time, create_time, update_time
        ) VALUES (
            #{userId}, #{courseId}, #{chapterId}, #{progressPercent}, #{lastPosition},
            #{isCompleted}, #{studyTime}, #{createTime}, #{updateTime}
        )
    </insert>

    <update id="updateProgress" parameterType="PsyUserCourseProgress">
        UPDATE psy_user_course_progress
        <set>
            <if test="progressPercent != null">progress_percent = #{progressPercent},</if>
            <if test="lastPosition != null">last_position = #{lastPosition},</if>
            <if test="isCompleted != null">is_completed = #{isCompleted},</if>
            <if test="studyTime != null">study_time = #{studyTime},</if>
            update_time = #{updateTime}
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteProgressById">
        DELETE FROM psy_user_course_progress WHERE id = #{id}
    </delete>

    <delete id="deleteProgressByIds">
        DELETE FROM psy_user_course_progress
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteProgressByUserId">
        DELETE FROM psy_user_course_progress WHERE user_id = #{userId}
    </delete>

    <delete id="deleteProgressByCourseId">
        DELETE FROM psy_user_course_progress WHERE course_id = #{courseId}
    </delete>

    <update id="updateStudyProgress">
        UPDATE psy_user_course_progress
        SET progress_percent = #{progressPercent},
            last_position = #{lastPosition},
            study_time = #{studyTime},
            update_time = NOW()
        WHERE user_id = #{userId} AND chapter_id = #{chapterId}
    </update>

    <update id="markChapterCompleted">
        UPDATE psy_user_course_progress
        SET is_completed = 1,
            progress_percent = 100,
            update_time = NOW()
        WHERE user_id = #{userId} AND chapter_id = #{chapterId}
    </update>

    <select id="calculateCourseProgress" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(progress_percent), 0) 
        FROM psy_user_course_progress 
        WHERE user_id = #{userId} AND course_id = #{courseId}
    </select>

    <select id="countCompletedChapters" resultType="int">
        SELECT COUNT(*) 
        FROM psy_user_course_progress 
        WHERE user_id = #{userId} AND course_id = #{courseId} AND is_completed = 1
    </select>

    <select id="sumStudyTime" resultType="int">
        SELECT COALESCE(SUM(study_time), 0) 
        FROM psy_user_course_progress 
        WHERE user_id = #{userId} AND course_id = #{courseId}
    </select>

</mapper>
