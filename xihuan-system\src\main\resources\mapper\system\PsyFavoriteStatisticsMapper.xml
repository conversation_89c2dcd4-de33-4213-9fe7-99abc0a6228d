<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyFavoriteStatisticsMapper">
    
    <resultMap type="PsyFavoriteStatistics" id="FavoriteStatisticsResult">
        <id     property="id"                column="id"                />
        <result property="targetType"        column="target_type"       />
        <result property="targetId"          column="target_id"         />
        <result property="favoriteCount"     column="favorite_count"    />
        <result property="todayCount"        column="today_count"       />
        <result property="weekCount"         column="week_count"        />
        <result property="monthCount"        column="month_count"       />
        <result property="lastFavoriteTime"  column="last_favorite_time"/>
        <result property="updateTime"        column="update_time"       />
    </resultMap>
    
    <sql id="selectFavoriteStatisticsVo">
        SELECT id, target_type, target_id, favorite_count, today_count, week_count, month_count, 
               last_favorite_time, update_time
        FROM psy_favorite_statistics
    </sql>
    
    <select id="selectStatsByTarget" resultMap="FavoriteStatisticsResult">
        <include refid="selectFavoriteStatisticsVo"/>
        WHERE target_type = #{targetType} AND target_id = #{targetId}
    </select>
    
    <insert id="insertStats" parameterType="PsyFavoriteStatistics" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_favorite_statistics (
            target_type, target_id, favorite_count, today_count, week_count, month_count, last_favorite_time
        ) VALUES (
            #{targetType}, #{targetId}, #{favoriteCount}, #{todayCount}, #{weekCount}, #{monthCount}, #{lastFavoriteTime}
        )
    </insert>
    
    <update id="updateStats" parameterType="PsyFavoriteStatistics">
        UPDATE psy_favorite_statistics
        <trim prefix="SET" suffixOverrides=",">
            <if test="favoriteCount != null">favorite_count = #{favoriteCount},</if>
            <if test="todayCount != null">today_count = #{todayCount},</if>
            <if test="weekCount != null">week_count = #{weekCount},</if>
            <if test="monthCount != null">month_count = #{monthCount},</if>
            <if test="lastFavoriteTime != null">last_favorite_time = #{lastFavoriteTime},</if>
        </trim>
        WHERE target_type = #{targetType} AND target_id = #{targetId}
    </update>
    
    <update id="incrementFavoriteCount">
        UPDATE psy_favorite_statistics
        SET favorite_count = favorite_count + 1,
            today_count = today_count + 1,
            week_count = week_count + 1,
            month_count = month_count + 1,
            last_favorite_time = NOW()
        WHERE target_type = #{targetType} AND target_id = #{targetId}
    </update>
    
    <update id="decrementFavoriteCount">
        UPDATE psy_favorite_statistics
        SET favorite_count = GREATEST(favorite_count - 1, 0)
        WHERE target_type = #{targetType} AND target_id = #{targetId}
    </update>
    
    <select id="selectHotFavorites" resultType="Map">
        SELECT 
            s.target_type,
            s.target_id,
            s.favorite_count,
            s.today_count,
            s.week_count,
            s.month_count,
            s.last_favorite_time,
            CASE s.target_type
                WHEN 1 THEN c.name
                WHEN 2 THEN course.title
                WHEN 3 THEN med.title
                WHEN 4 THEN scale.name
            END as title,
            CASE s.target_type
                WHEN 1 THEN c.image_url
                WHEN 2 THEN course.cover_image
                WHEN 3 THEN med.cover_image
                WHEN 4 THEN scale.image_url
            END as image_url
        FROM psy_favorite_statistics s
        LEFT JOIN psy_consultants c ON s.target_type = 1 AND s.target_id = c.id AND c.del_flag = '0'
        LEFT JOIN psy_course course ON s.target_type = 2 AND s.target_id = course.id AND course.del_flag = '0'
        LEFT JOIN psy_meditation med ON s.target_type = 3 AND s.target_id = med.id AND med.del_flag = '0'
        LEFT JOIN psy_scale scale ON s.target_type = 4 AND s.target_id = scale.id AND scale.del_flag = '0'
        <where>
            <if test="targetType != null">
                s.target_type = #{targetType}
            </if>
        </where>
        ORDER BY s.favorite_count DESC
        LIMIT #{limit}
    </select>
    
    <select id="selectFavoriteTrend" resultType="Map">
        SELECT 
            DATE_FORMAT(f.favorite_time, '%Y-%m-%d') as date,
            COUNT(*) as count,
            COUNT(CASE WHEN f.target_type = 1 THEN 1 END) as consultant_count,
            COUNT(CASE WHEN f.target_type = 2 THEN 1 END) as course_count,
            COUNT(CASE WHEN f.target_type = 3 THEN 1 END) as meditation_count,
            COUNT(CASE WHEN f.target_type = 4 THEN 1 END) as assessment_count
        FROM psy_user_favorite f
        WHERE f.favorite_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY)
        <if test="targetType != null">
            AND f.target_type = #{targetType}
        </if>
        GROUP BY DATE_FORMAT(f.favorite_time, '%Y-%m-%d')
        ORDER BY date ASC
    </select>
    
    <update id="resetDailyStats">
        UPDATE psy_favorite_statistics
        SET today_count = 0
    </update>
    
    <update id="resetWeeklyStats">
        UPDATE psy_favorite_statistics
        SET week_count = 0
    </update>
    
    <update id="resetMonthlyStats">
        UPDATE psy_favorite_statistics
        SET month_count = 0
    </update>
    
</mapper>
