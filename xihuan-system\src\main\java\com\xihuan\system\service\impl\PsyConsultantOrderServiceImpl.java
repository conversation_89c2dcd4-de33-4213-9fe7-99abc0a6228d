package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyConsultantOrder;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.DateRangeUtils;
import com.xihuan.system.mapper.PsyConsultantOrderMapper;
import com.xihuan.system.service.IPsyConsultantOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 心理咨询订单表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyConsultantOrderServiceImpl implements IPsyConsultantOrderService {
    
    @Autowired
    private PsyConsultantOrderMapper orderMapper;

    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    @Override
    public List<PsyConsultantOrder> selectOrderList(PsyConsultantOrder order) {
        return orderMapper.selectOrderList(order);
    }

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    @Override
    public PsyConsultantOrder selectOrderById(Long id) {
        return orderMapper.selectOrderById(id);
    }

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    @Override
    public PsyConsultantOrder selectOrderByOrderNo(String orderNo) {
        return orderMapper.selectOrderByOrderNo(orderNo);
    }

    /**
     * 查询订单详情（包含用户、咨询师等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    @Override
    public PsyConsultantOrder selectOrderWithDetails(Long id) {
        return orderMapper.selectOrderWithDetails(id);
    }

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOrder(PsyConsultantOrder order) {
        // 生成订单号
        if (order.getOrderNo() == null || order.getOrderNo().isEmpty()) {
            order.setOrderNo(generateOrderNo());
        }
        
        // 检查时间冲突
        Date endTime = new Date(order.getScheduledTime().getTime() + order.getDuration() * 60 * 1000);
        boolean hasConflict = checkTimeConflict(order.getConsultantId(), order.getScheduledTime(), endTime, null);
        if (hasConflict) {
            throw new ServiceException("该时间段已被预约，请选择其他时间");
        }
        
        // 设置默认值
        order.setCreateTime(DateUtils.getNowDate());
        order.setDelFlag("0");
        order.setStatus("待支付");
        
        // 计算金额
        if (order.getOriginalAmount() == null && order.getUnitPrice() != null && order.getDuration() != null) {
            order.setOriginalAmount(order.getUnitPrice().multiply(new BigDecimal(order.getDuration())));
        }
        
        if (order.getDiscountAmount() == null) {
            order.setDiscountAmount(BigDecimal.ZERO);
        }
        
        if (order.getPaymentAmount() == null) {
            order.setPaymentAmount(order.getOriginalAmount().subtract(order.getDiscountAmount()));
        }
        
        return orderMapper.insertOrder(order);
    }

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    @Override
    public int updateOrder(PsyConsultantOrder order) {
        order.setUpdateTime(DateUtils.getNowDate());
        return orderMapper.updateOrder(order);
    }

    /**
     * 删除订单
     * 
     * @param id 订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderById(Long id) {
        return orderMapper.deleteOrderById(id);
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderByIds(Long[] ids) {
        return orderMapper.deleteOrderByIds(ids);
    }

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<PsyConsultantOrder> selectOrdersByUserId(Long userId) {
        return orderMapper.selectOrdersByUserId(userId);
    }

    /**
     * 根据咨询师ID查询订单列表
     * 
     * @param consultantId 咨询师ID
     * @return 订单集合
     */
    @Override
    public List<PsyConsultantOrder> selectOrdersByConsultantId(Long consultantId) {
        return orderMapper.selectOrdersByConsultantId(consultantId);
    }

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param status 订单状态
     * @param paymentMethod 支付方式
     * @param paymentTime 支付时间
     * @return 结果
     */
    @Override
    public int updateOrderPaymentStatus(String orderNo, String status, String paymentMethod, Date paymentTime) {
        return orderMapper.updateOrderPaymentStatus(orderNo, status, paymentMethod, paymentTime);
    }

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 订单状态
     * @return 结果
     */
    @Override
    public int updateOrderStatus(Long id, String status) {
        return orderMapper.updateOrderStatus(id, status);
    }

    /**
     * 取消订单
     * 
     * @param id 订单ID
     * @param cancelReason 取消原因
     * @return 结果
     */
    @Override
    public int cancelOrder(Long id, String cancelReason) {
        if (!canCancel(id)) {
            throw new ServiceException("该订单不能取消");
        }
        
        return orderMapper.cancelOrder(id, cancelReason, DateUtils.getNowDate());
    }

    /**
     * 退款订单
     * 
     * @param id 订单ID
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 结果
     */
    @Override
    public int refundOrder(Long id, BigDecimal refundAmount, String refundReason) {
        if (!canRefund(id)) {
            throw new ServiceException("该订单不能退款");
        }
        
        return orderMapper.refundOrder(id, refundAmount, refundReason, DateUtils.getNowDate());
    }

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    @Override
    public String generateOrderNo() {
        return orderMapper.generateOrderNo();
    }

    /**
     * 检查时间段冲突
     * 
     * @param consultantId 咨询师ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeOrderId 排除的订单ID
     * @return 是否冲突
     */
    @Override
    public boolean checkTimeConflict(Long consultantId, Date startTime, Date endTime, Long excludeOrderId) {
        int count = orderMapper.checkTimeConflict(consultantId, startTime, endTime, excludeOrderId);
        return count > 0;
    }

    /**
     * 查询即将到期的订单
     * 
     * @param minutes 提前分钟数
     * @return 订单集合
     */
    @Override
    public List<PsyConsultantOrder> selectExpiringOrders(Integer minutes) {
        return orderMapper.selectExpiringOrders(minutes);
    }

    /**
     * 查询已过期的订单
     * 
     * @return 订单集合
     */
    @Override
    public List<PsyConsultantOrder> selectExpiredOrders() {
        return orderMapper.selectExpiredOrders();
    }

    /**
     * 处理过期订单
     * 
     * @return 处理数量
     */
    @Override
    @Transactional
    public int processExpiredOrders() {
        List<PsyConsultantOrder> expiredOrders = selectExpiredOrders();
        int count = 0;
        
        for (PsyConsultantOrder order : expiredOrders) {
            if ("待支付".equals(order.getStatus())) {
                // 未支付订单设为已过期
                updateOrderStatus(order.getId(), "已过期");
                count++;
            } else if ("已支付".equals(order.getStatus())) {
                // 已支付但未开始咨询的订单设为待咨询
                updateOrderStatus(order.getId(), "待咨询");
            }
        }
        
        return count;
    }

    /**
     * 统计用户订单数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getUserOrderStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 订单总数
        int totalOrders = orderMapper.countUserOrders(userId);
        stats.put("totalOrders", totalOrders);
        
        // 各状态订单数量
        PsyConsultantOrder queryOrder = new PsyConsultantOrder();
        queryOrder.setUserId(userId);
        
        queryOrder.setStatus("已完成");
        List<PsyConsultantOrder> completedOrders = orderMapper.selectOrderList(queryOrder);
        stats.put("completedOrders", completedOrders.size());
        
        queryOrder.setStatus("待支付");
        List<PsyConsultantOrder> pendingOrders = orderMapper.selectOrderList(queryOrder);
        stats.put("pendingOrders", pendingOrders.size());
        
        return stats;
    }

    /**
     * 统计咨询师订单数据
     * 
     * @param consultantId 咨询师ID
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getConsultantOrderStats(Long consultantId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 订单总数
        int totalOrders = orderMapper.countConsultantOrders(consultantId);
        stats.put("totalOrders", totalOrders);
        
        // 总收入
        BigDecimal totalIncome = orderMapper.sumOrderIncome(consultantId, null, null);
        stats.put("totalIncome", totalIncome);
        
        // 本月收入
        Date startOfMonth = DateRangeUtils.getStartOfMonth();
        Date endOfMonth = DateRangeUtils.getEndOfMonth();
        BigDecimal monthIncome = orderMapper.sumOrderIncome(consultantId, startOfMonth, endOfMonth);
        stats.put("monthIncome", monthIncome);
        
        return stats;
    }

    /**
     * 统计订单收入
     * 
     * @param consultantId 咨询师ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 收入金额
     */
    @Override
    public BigDecimal sumOrderIncome(Long consultantId, Date startTime, Date endTime) {
        return orderMapper.sumOrderIncome(consultantId, startTime, endTime);
    }

    /**
     * 检查订单是否可以取消
     * 
     * @param orderId 订单ID
     * @return 是否可以取消
     */
    @Override
    public boolean canCancel(Long orderId) {
        PsyConsultantOrder order = selectOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // 只有待支付和已支付状态的订单可以取消
        String status = order.getStatus();
        if (!"待支付".equals(status) && !"已支付".equals(status)) {
            return false;
        }
        
        // 已支付的订单需要在咨询开始前2小时才能取消
        if ("已支付".equals(status)) {
            Date now = new Date();
            Date scheduledTime = order.getScheduledTime();
            long timeDiff = scheduledTime.getTime() - now.getTime();
            return timeDiff > 2 * 60 * 60 * 1000; // 2小时
        }
        
        return true;
    }

    /**
     * 检查订单是否可以退款
     * 
     * @param orderId 订单ID
     * @return 是否可以退款
     */
    @Override
    public boolean canRefund(Long orderId) {
        PsyConsultantOrder order = selectOrderById(orderId);
        if (order == null) {
            return false;
        }
        
        // 只有已支付、已完成状态的订单可以退款
        String status = order.getStatus();
        return "已支付".equals(status) || "已完成".equals(status);
    }
}
