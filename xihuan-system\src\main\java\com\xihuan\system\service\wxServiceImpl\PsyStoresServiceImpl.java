package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessDays;
import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessHours;
import com.xihuan.common.core.domain.entity.store.PsyStoreContacts;
import com.xihuan.common.core.domain.entity.store.PsyStores;
import com.xihuan.system.mapper.PsyStoreBusinessDaysMapper;
import com.xihuan.system.mapper.PsyStoreBusinessHoursMapper;
import com.xihuan.system.mapper.PsyStoreContactsMapper;
import com.xihuan.system.mapper.PsyStoresMapper;
import com.xihuan.system.service.wxService.IPsyStoresService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 心理咨询门店Service实现类
 */
@Service
public class PsyStoresServiceImpl implements IPsyStoresService {
    @Autowired
    private PsyStoresMapper psyStoresMapper;

    @Autowired
    private PsyStoreContactsMapper psyStoreContactsMapper;

    @Autowired
    private PsyStoreBusinessDaysMapper psyStoreBusinessDaysMapper;

    @Autowired
    private PsyStoreBusinessHoursMapper psyStoreBusinessHoursMapper;

    /**
     * 查询门店列表
     */
    @Override
    public List<PsyStores> selectPsyStoresList(PsyStores psyStores) {
        return psyStoresMapper.selectPsyStoresList(psyStores);
    }

    /**
     * 查询门店信息
     */
    @Override
    public PsyStores selectPsyStoresById(Long id) {
        return psyStoresMapper.selectPsyStoresById(id);
    }

    /**
     * 新增门店
     */
    @Override
    @Transactional
    public int insertPsyStores(PsyStores psyStores, List<PsyStoreContacts> contacts,
                               PsyStoreBusinessDays businessDays, List<PsyStoreBusinessHours> businessHours) {
        // 新增门店信息
        int rows = psyStoresMapper.insertPsyStores(psyStores);
        if (rows > 0) {
            Long storeId = psyStores.getId();
            
            // 新增联系方式
            if (contacts != null && !contacts.isEmpty()) {
                for (PsyStoreContacts contact : contacts) {
                    contact.setStoreId(storeId);
                    psyStoreContactsMapper.insertPsyStoreContacts(contact);
                }
            }
            
            // 新增营业日配置
            if (businessDays != null) {
                businessDays.setStoreId(storeId);
                psyStoreBusinessDaysMapper.insertPsyStoreBusinessDays(businessDays);
            }
            
            // 新增营业时间段
            if (businessHours != null && !businessHours.isEmpty()) {
                for (PsyStoreBusinessHours hours : businessHours) {
                    hours.setStoreId(storeId);
                    psyStoreBusinessHoursMapper.insertPsyStoreBusinessHours(hours);
                }
            }
        }
        return rows;
    }

    /**
     * 修改门店
     */
    @Override
    @Transactional
    public int updatePsyStores(PsyStores psyStores, List<PsyStoreContacts> contacts, 
                             PsyStoreBusinessDays businessDays, List<PsyStoreBusinessHours> businessHours) {
        Long storeId = psyStores.getId();
        
        // 删除原有关联数据
        psyStoreContactsMapper.deletePsyStoreContactsByStoreId(storeId);
        psyStoreBusinessDaysMapper.deletePsyStoreBusinessDaysByStoreId(storeId);
        psyStoreBusinessHoursMapper.deletePsyStoreBusinessHoursByStoreId(storeId);
        
        // 新增联系方式
        if (contacts != null && !contacts.isEmpty()) {
            for (PsyStoreContacts contact : contacts) {
                contact.setStoreId(storeId);
                psyStoreContactsMapper.insertPsyStoreContacts(contact);
            }
        }
        
        // 新增营业日配置
        if (businessDays != null) {
            businessDays.setStoreId(storeId);
            psyStoreBusinessDaysMapper.insertPsyStoreBusinessDays(businessDays);
        }
        
        // 新增营业时间段
        if (businessHours != null && !businessHours.isEmpty()) {
            for (PsyStoreBusinessHours hours : businessHours) {
                hours.setStoreId(storeId);
                psyStoreBusinessHoursMapper.insertPsyStoreBusinessHours(hours);
            }
        }
        
        // 更新门店信息
        return psyStoresMapper.updatePsyStores(psyStores);
    }

    /**
     * 删除门店
     */
    @Override
    @Transactional
    public int deletePsyStoresById(Long id) {
        // 删除关联数据
        psyStoreContactsMapper.deletePsyStoreContactsByStoreId(id);
        psyStoreBusinessDaysMapper.deletePsyStoreBusinessDaysByStoreId(id);
        psyStoreBusinessHoursMapper.deletePsyStoreBusinessHoursByStoreId(id);
        
        // 删除门店信息
        return psyStoresMapper.deletePsyStoresById(id);
    }

    /**
     * 批量删除门店
     */
    @Override
    @Transactional
    public int deletePsyStoresByIds(Long[] ids) {
        for (Long id : ids) {
            deletePsyStoresById(id);
        }
        return ids.length;
    }

    /**
     * 获取门店联系方式列表
     */
    @Override
    public List<PsyStoreContacts> getStoreContacts(Long storeId) {
        return psyStoreContactsMapper.selectPsyStoreContactsByStoreId(storeId);
    }

    /**
     * 获取门店营业日配置
     */
    @Override
    public PsyStoreBusinessDays getStoreBusinessDays(Long storeId) {
        return psyStoreBusinessDaysMapper.selectPsyStoreBusinessDaysByStoreId(storeId);
    }

    /**
     * 获取门店营业时间段列表
     */
    @Override
    public List<PsyStoreBusinessHours> getStoreBusinessHours(Long storeId) {
        return psyStoreBusinessHoursMapper.selectPsyStoreBusinessHoursByStoreId(storeId);
    }

    /**
     * 更新门店状态
     */
    @Override
    public int updatePsyStoresStatus(Long id, String status) {
        PsyStores store = new PsyStores();
        store.setId(id);
        store.setStatus(status);
        return psyStoresMapper.updatePsyStores(store);
    }

} 