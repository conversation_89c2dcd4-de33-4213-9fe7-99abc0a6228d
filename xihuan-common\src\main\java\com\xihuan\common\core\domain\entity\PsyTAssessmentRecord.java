package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 测评记录表 psy_t_assessment_record
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTAssessmentRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @Excel(name = "记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    private Long userId;

    /** 会话ID */
    @Excel(name = "会话ID")
    @NotBlank(message = "会话ID不能为空")
    @Size(max = 64, message = "会话ID不能超过64个字符")
    private String sessionId;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    /** 完成时间 */
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completionTime;

    /** 总分 */
    @Excel(name = "总分")
    private BigDecimal totalScore;

    /** 结果等级 */
    @Excel(name = "结果等级")
    @Size(max = 50, message = "结果等级不能超过50个字符")
    private String resultLevel;

    /** 结果描述 */
    @Excel(name = "结果描述")
    private String resultDescription;

    /** 建议 */
    @Excel(name = "建议")
    private String suggestions;

    /** 状态(0进行中 1已完成 2已放弃) */
    @Excel(name = "状态", readConverterExp = "0=进行中,1=已完成,2=已放弃")
    private Integer status;

    /** IP地址 */
    @Excel(name = "IP地址")
    @Size(max = 50, message = "IP地址不能超过50个字符")
    private String ipAddress;

    /** 用户代理 */
    @Excel(name = "用户代理")
    @Size(max = 500, message = "用户代理不能超过500个字符")
    private String userAgent;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    /** 用户信息 */
    private SysUser user;

    /** 答题记录列表 */
    private List<PsyTAnswerRecord> answers;

    /** 订单信息 */
    private PsyTAssessmentOrder order;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 用户昵称 */
    private String nickName;

    /** 完成进度 */
    private BigDecimal progress;

    /** 当前题目序号 */
    private Integer currentQuestionNo;

    /** 总题数 */
    private Integer totalQuestions;

    /** 已答题数 */
    private Integer answeredQuestions;

    /** 测评用时(秒) */
    private Integer duration;

    /** 分量表得分 */
    private String subscaleScores;

    /** 最大分数 */
    private BigDecimal maxScore;

    /** 得分百分比 */
    private BigDecimal scorePercentage;

    // 常量定义
    /** 状态：进行中 */
    public static final Integer STATUS_IN_PROGRESS = 0;
    
    /** 状态：已完成 */
    public static final Integer STATUS_COMPLETED = 1;
    
    /** 状态：已放弃 */
    public static final Integer STATUS_ABANDONED = 2;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "进行中";
            case 1: return "已完成";
            case 2: return "已放弃";
            default: return "未知";
        }
    }

    /**
     * 计算测评用时
     */
    public Integer calculateDuration() {
        if (startTime == null) return 0;
        Date endTime = completionTime != null ? completionTime : new Date();
        return (int) ((endTime.getTime() - startTime.getTime()) / 1000);
    }

    /**
     * 计算完成进度
     */
    public BigDecimal calculateProgress() {
        if (totalQuestions == null || totalQuestions == 0) return BigDecimal.ZERO;
        if (answeredQuestions == null) return BigDecimal.ZERO;
        return new BigDecimal(answeredQuestions)
                .divide(new BigDecimal(totalQuestions), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal(100));
    }

    /**
     * 是否进行中
     */
    public boolean isInProgress() {
        return STATUS_IN_PROGRESS.equals(status);
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }

    /**
     * 是否已放弃
     */
    public boolean isAbandoned() {
        return STATUS_ABANDONED.equals(status);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }
}
