package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTSubscale;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 分量表定义Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTSubscaleMapper {
    
    /**
     * 查询分量表列表
     * 
     * @param subscale 分量表信息
     * @return 分量表集合
     */
    List<PsyTSubscale> selectSubscaleList(PsyTSubscale subscale);

    /**
     * 根据ID查询分量表
     * 
     * @param id 分量表ID
     * @return 分量表信息
     */
    PsyTSubscale selectSubscaleById(Long id);

    /**
     * 查询分量表详情（包含题目关系、计分规则等信息）
     * 
     * @param id 分量表ID
     * @return 分量表详情
     */
    PsyTSubscale selectSubscaleWithDetails(Long id);

    /**
     * 根据量表ID查询分量表列表
     * 
     * @param scaleId 量表ID
     * @return 分量表集合
     */
    List<PsyTSubscale> selectSubscalesByScaleId(Long scaleId);

    /**
     * 根据量表ID查询分量表列表（包含题目关系）
     * 
     * @param scaleId 量表ID
     * @return 分量表集合
     */
    List<PsyTSubscale> selectSubscalesWithQuestionsByScaleId(Long scaleId);

    /**
     * 新增分量表
     * 
     * @param subscale 分量表信息
     * @return 结果
     */
    int insertSubscale(PsyTSubscale subscale);

    /**
     * 修改分量表
     * 
     * @param subscale 分量表信息
     * @return 结果
     */
    int updateSubscale(PsyTSubscale subscale);

    /**
     * 删除分量表
     * 
     * @param id 分量表ID
     * @return 结果
     */
    int deleteSubscaleById(Long id);

    /**
     * 批量删除分量表
     * 
     * @param ids 需要删除的分量表ID
     * @return 结果
     */
    int deleteSubscaleByIds(Long[] ids);

    /**
     * 根据量表ID删除分量表
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteSubscalesByScaleId(Long scaleId);

    /**
     * 检查分量表名称唯一性
     * 
     * @param scaleId 量表ID
     * @param name 分量表名称
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkSubscaleNameUnique(@Param("scaleId") Long scaleId, 
                               @Param("name") String name, 
                               @Param("excludeId") Long excludeId);

    /**
     * 检查分量表缩写唯一性
     * 
     * @param scaleId 量表ID
     * @param alias 分量表缩写
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkSubscaleAliasUnique(@Param("scaleId") Long scaleId, 
                                @Param("alias") String alias, 
                                @Param("excludeId") Long excludeId);

    /**
     * 统计量表分量表数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countSubscalesByScaleId(Long scaleId);

    /**
     * 查询分量表题目数量统计
     * 
     * @param subscaleId 分量表ID
     * @return 题目数量
     */
    int countQuestionsBySubscaleId(Long subscaleId);

    /**
     * 查询分量表得分统计
     * 
     * @param subscaleId 分量表ID
     * @return 统计信息
     */
    Map<String, Object> selectSubscaleScoreStats(Long subscaleId);

    /**
     * 查询分量表使用情况
     * 
     * @param subscaleId 分量表ID
     * @return 使用情况
     */
    Map<String, Object> selectSubscaleUsageInfo(Long subscaleId);

    /**
     * 复制分量表到新量表
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    int copySubscalesToScale(@Param("sourceScaleId") Long sourceScaleId, 
                            @Param("targetScaleId") Long targetScaleId);

    /**
     * 查询分量表得分分布
     * 
     * @param subscaleId 分量表ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectSubscaleScoreDistribution(Long subscaleId);

    /**
     * 查询分量表相关性分析
     * 
     * @param scaleId 量表ID
     * @return 相关性信息
     */
    List<Map<String, Object>> selectSubscaleCorrelationAnalysis(Long scaleId);

    /**
     * 查询分量表信度分析
     * 
     * @param subscaleId 分量表ID
     * @return 信度信息
     */
    Map<String, Object> selectSubscaleReliabilityAnalysis(Long subscaleId);

    /**
     * 搜索分量表
     * 
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @return 分量表集合
     */
    List<PsyTSubscale> searchSubscales(@Param("keyword") String keyword,
                                      @Param("scaleId") Long scaleId);
}
