package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTimeRange;
import java.util.List;

/**
 * 时间段定义Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTimeRangeService {
    
    /**
     * 查询时间段定义列表
     * 
     * @param timeRange 时间段定义
     * @return 时间段定义集合
     */
    List<PsyTimeRange> selectTimeRangeList(PsyTimeRange timeRange);
    
    /**
     * 查询所有有效的时间段定义
     * 
     * @return 时间段定义集合
     */
    List<PsyTimeRange> selectAllActiveTimeRanges();
    
    /**
     * 根据ID查询时间段定义
     * 
     * @param id 时间段定义主键
     * @return 时间段定义
     */
    PsyTimeRange selectTimeRangeById(Long id);
    
    /**
     * 根据时间查询所属时间段
     * 
     * @param hour 小时（0-23）
     * @return 时间段定义
     */
    PsyTimeRange selectTimeRangeByHour(Integer hour);
    
    /**
     * 新增时间段定义
     * 
     * @param timeRange 时间段定义
     * @return 结果
     */
    int insertTimeRange(PsyTimeRange timeRange);
    
    /**
     * 修改时间段定义
     * 
     * @param timeRange 时间段定义
     * @return 结果
     */
    int updateTimeRange(PsyTimeRange timeRange);
    
    /**
     * 批量删除时间段定义
     * 
     * @param ids 需要删除的时间段定义主键集合
     * @return 结果
     */
    int deleteTimeRangeByIds(Long[] ids);
    
    /**
     * 删除时间段定义信息
     * 
     * @param id 时间段定义主键
     * @return 结果
     */
    int deleteTimeRangeById(Long id);
    
    /**
     * 校验时间段名称是否唯一
     * 
     * @param timeRange 时间段定义信息
     * @return 结果
     */
    String checkTimeRangeNameUnique(PsyTimeRange timeRange);
    
    /**
     * 初始化默认时间段
     * 
     * @return 结果
     */
    int initDefaultTimeRanges();
}
