package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 心理测评记录Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentRecordMapper {
    
    /**
     * 查询测评记录列表
     * 
     * @param record 测评记录信息
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordList(PsyAssessmentRecord record);

    /**
     * 根据ID查询测评记录
     * 
     * @param id 测评记录ID
     * @return 测评记录信息
     */
    PsyAssessmentRecord selectRecordById(Long id);

    /**
     * 查询测评记录详情（包含用户、量表等信息）
     * 
     * @param id 测评记录ID
     * @return 测评记录详情
     */
    PsyAssessmentRecord selectRecordWithDetails(Long id);

    /**
     * 根据会话ID查询测评记录
     * 
     * @param sessionId 会话ID
     * @return 测评记录信息
     */
    PsyAssessmentRecord selectRecordBySessionId(String sessionId);

    /**
     * 根据用户ID查询测评记录列表
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordsByUserId(Long userId);

    /**
     * 根据量表ID查询测评记录列表
     * 
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordsByScaleId(Long scaleId);

    /**
     * 新增测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    int insertRecord(PsyAssessmentRecord record);

    /**
     * 修改测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    int updateRecord(PsyAssessmentRecord record);

    /**
     * 删除测评记录
     * 
     * @param id 测评记录ID
     * @return 结果
     */
    int deleteRecordById(Long id);

    /**
     * 批量删除测评记录
     * 
     * @param ids 需要删除的测评记录ID
     * @return 结果
     */
    int deleteRecordByIds(Long[] ids);

    /**
     * 查询用户在指定量表的最新测评记录
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录信息
     */
    PsyAssessmentRecord selectLatestRecordByUserAndScale(@Param("userId") Long userId, @Param("scaleId") Long scaleId);

    /**
     * 查询用户在指定量表的测评次数
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评次数
     */
    int countRecordsByUserAndScale(@Param("userId") Long userId, @Param("scaleId") Long scaleId);

    /**
     * 查询用户的测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectUserRecordStats(Long userId);

    /**
     * 查询量表的测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectScaleRecordStats(Long scaleId);

    /**
     * 查询进行中的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectInProgressRecords(Long userId);

    /**
     * 查询已完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectCompletedRecords(Long userId);

    /**
     * 查询指定时间范围的测评记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordsByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 查询今日测评记录
     * 
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectTodayRecords();

    /**
     * 查询本周测评记录
     * 
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectWeekRecords();

    /**
     * 查询本月测评记录
     * 
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectMonthRecords();

    /**
     * 更新测评进度
     * 
     * @param sessionId 会话ID
     * @param currentQuestionNo 当前题目序号
     * @param progress 进度
     * @return 结果
     */
    int updateProgress(@Param("sessionId") String sessionId, 
                      @Param("currentQuestionNo") Integer currentQuestionNo, 
                      @Param("progress") java.math.BigDecimal progress);

    /**
     * 完成测评
     * 
     * @param sessionId 会话ID
     * @param endTime 结束时间
     * @param duration 测评时长
     * @param totalScore 总分
     * @param percentage 得分率
     * @param resultLevel 结果等级
     * @param resultDescription 结果描述
     * @param suggestions 建议
     * @param dimensionScores 维度得分
     * @return 结果
     */
    int completeRecord(@Param("sessionId") String sessionId,
                      @Param("endTime") Date endTime,
                      @Param("duration") Integer duration,
                      @Param("totalScore") java.math.BigDecimal totalScore,
                      @Param("percentage") java.math.BigDecimal percentage,
                      @Param("resultLevel") String resultLevel,
                      @Param("resultDescription") String resultDescription,
                      @Param("suggestions") String suggestions,
                      @Param("dimensionScores") String dimensionScores);

    /**
     * 中断测评
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    int interruptRecord(String sessionId);

    /**
     * 查询测评排行榜
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectRecordRanking(@Param("scaleId") Long scaleId, @Param("limit") Integer limit);

    /**
     * 查询用户测评历史
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    List<PsyAssessmentRecord> selectUserTestHistory(@Param("userId") Long userId, @Param("scaleId") Long scaleId);

    /**
     * 统计测评记录数量
     * 
     * @param record 查询条件
     * @return 数量
     */
    int countRecords(PsyAssessmentRecord record);

    /**
     * 查询测评趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectRecordTrend(@Param("days") Integer days);
}
