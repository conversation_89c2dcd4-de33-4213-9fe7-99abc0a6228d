package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.consultant.PsyDictExpertise;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.wxService.IPsyDictExpertiseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 咨询领域Controller
 */
@RestController
@RequestMapping("/system/expertise")
public class PsyDictExpertiseController extends BaseController {
    @Autowired
    private IPsyDictExpertiseService psyDictExpertiseService;

    /**
     * 查询咨询领域列表
     */
    @PreAuthorize("@ss.hasPermi('system:expertise:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyDictExpertise psyDictExpertise) {
        startPage();
        List<PsyDictExpertise> list = psyDictExpertiseService.selectPsyDictExpertiseList(psyDictExpertise);
        return getDataTable(list);
    }

    /**
     * 获取树形结构的咨询领域列表
     */
    @GetMapping("/tree")
    public AjaxResult tree() {
        List<PsyDictExpertise> list = psyDictExpertiseService.buildExpertiseTree();
        return success(list);
    }

    /**
     * 导出咨询领域列表
     */
    @PreAuthorize("@ss.hasPermi('system:expertise:export')")
    @Log(title = "咨询领域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyDictExpertise psyDictExpertise) {
        List<PsyDictExpertise> list = psyDictExpertiseService.selectPsyDictExpertiseList(psyDictExpertise);
        ExcelUtil<PsyDictExpertise> util = new ExcelUtil<>(PsyDictExpertise.class);
        util.exportExcel(response, list, "咨询领域数据");
    }

    /**
     * 获取咨询领域详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:expertise:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(psyDictExpertiseService.selectPsyDictExpertiseById(id));
    }

    /**
     * 新增咨询领域
     */
    @PreAuthorize("@ss.hasPermi('system:expertise:add')")
    @Log(title = "咨询领域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyDictExpertise psyDictExpertise) {
        return toAjax(psyDictExpertiseService.insertPsyDictExpertise(psyDictExpertise));
    }

    /**
     * 修改咨询领域
     */
    @PreAuthorize("@ss.hasPermi('system:expertise:edit')")
    @Log(title = "咨询领域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyDictExpertise psyDictExpertise) {
        return toAjax(psyDictExpertiseService.updatePsyDictExpertise(psyDictExpertise));
    }

    /**
     * 删除咨询领域
     */
    @PreAuthorize("@ss.hasPermi('system:expertise:remove')")
    @Log(title = "咨询领域", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(psyDictExpertiseService.deletePsyDictExpertiseByIds(ids));
    }
} 