package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalTime;

/**
 * 咨询时间槽主表实体类
 * 对应数据库表：psy_time_slot
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTimeSlot extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 时间槽ID */
    private Long id;

    /** 排班ID */
    private Long scheduleId;

    /** 是否公开时段 */
    private Integer isPublic;

    /** 咨询中心ID */
    private Long centerId;

    /** 咨询师ID */
    private Long counselorId;

    /** 日期KEY（YYYY-MM-DD） */
    private String dateKey;

    /** 星期几 */
    private String weekDay;

    /** 所属时间段ID */
    private Long rangeId;

    /** 开始时间 */
    private LocalTime startTime;

    /** 结束时间 */
    private LocalTime endTime;

    /** 状态（0可用 1已预约 2已过期） */
    private Integer status;

    /** 删除标志 */
    private Integer delFlag;

    /** 父时间槽ID（用于1小时时段分组） */
    private Long parentSlotId;

    /** 时间组哈希值 */
    private String timeGroupHash;

    // 关联对象（非数据库字段）
    /** 时间段信息 */
    private PsyTimeRange timeRange;

    /** 咨询师信息 */
    private com.xihuan.common.core.domain.consultant.PsyConsultant consultant;
}
