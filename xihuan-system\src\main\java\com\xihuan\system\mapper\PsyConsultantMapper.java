package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.vo.ConsultantSimpleVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 心理咨询师数据层
 */
public interface PsyConsultantMapper {
    /**
     * 新增咨询师
     */
    int insertConsultant(PsyConsultant consultant);

    /**
     * 更新咨询师信息
     */
    int updateConsultant(PsyConsultant consultant);

    /**
     * 根据ID查询咨询师（管理员查询，包含敏感信息）
     */
    PsyConsultant selectConsultantWithUserById(Long id);

    /**
     * 根据用户ID查询咨询师（管理员查询，包含敏感信息）
     */
    PsyConsultant selectConsultantByUserId(Long userId);

    /**
     * 分页查询咨询师列表（管理员查询，包含敏感信息）
     */
    List<PsyConsultant> selectConsultantListByPage(Map<String, Object> params);

    /**
     * 查询所有可用咨询师（普通用户查询，不含敏感信息）
     */
    List<PsyConsultant> selectAllConsultantsWithDetails();

    /**
     * 根据条件查询咨询师列表（普通用户查询，不含敏感信息）
     */
    List<PsyConsultant> selectAllConsultants(Map<String, Object> params);

    /**
     * 根据ID查询咨询师完整信息（普通用户查询，不含敏感信息）
     */
    PsyConsultant selectConsultantFullDetails(Long id);

    /**
     * 根据ID删除咨询师
     */
    int deleteConsultantById(Long id);

    /**
     * 批量删除咨询师
     */
    int deleteConsultantByIds(Long[] ids);

    /**
     * 更新咨询师工作状态
     */
    int updateConsultantWorkStatus(@Param("id") Long id, @Param("workStatus") String workStatus);

    /**
     * 更新咨询师审核状态
     */
    int updateConsultantAuditStatus(@Param("id") Long id, @Param("auditStatus") String auditStatus);

    /**
     * 查询所有咨询师简单信息列表
     *
     * @return 咨询师简单信息列表
     */
    List<ConsultantSimpleVO> selectAllSimple();

    /**
     * 根据选项ID列表查询匹配的咨询师
     * @param optionIds 选项ID列表
     * @param optionCount 选项数量（用于HAVING判断）
     * @return 匹配的咨询师列表
     */
    List<PsyConsultant> selectByOptionIds(@Param("optionIds") List<Long> optionIds, @Param("optionCount") int optionCount);

    /**
     * 根据选项ID筛选咨询师
     */
    List<PsyConsultant> selectByOptionIds(
        @Param("optionIds") List<Long> optionIds,
        @Param("optionCount") Integer optionCount,
        @Param("expertiseIds") List<Long> expertiseIds,
        @Param("expertiseCount") Integer expertiseCount
    );

    /**
     * 根据咨询领域ID筛选咨询师
     */
    List<PsyConsultant> selectByExpertiseIds(
        @Param("expertiseIds") List<Long> expertiseIds,
        @Param("expertiseCount") Integer expertiseCount
    );

    /**
     * 根据选项ID和咨询领域ID筛选咨询师
     */
    List<PsyConsultant> selectByOptionAndExpertiseIds(
        @Param("optionIds") List<Long> optionIds,
        @Param("optionCount") Integer optionCount,
        @Param("expertiseIds") List<Long> expertiseIds,
        @Param("expertiseCount") Integer expertiseCount
    );

    /**
     * 根据选项ID列表和已筛选的咨询师ID列表查询咨询师
     * @param optionIds 选项ID列表
     * @param optionCount 要求匹配的选项数量
     * @param consultantIds 已筛选的咨询师ID列表
     * @return 匹配的咨询师列表
     */
    List<PsyConsultant> selectByOptionIdsAndConsultantIds(
        @Param("optionIds") List<Long> optionIds,
        @Param("optionCount") Integer optionCount,
        @Param("consultantIds") List<Long> consultantIds
    );

    /**
     * 根据专业领域ID列表和已筛选的咨询师ID列表查询咨询师
     * @param expertiseIds 专业领域ID列表
     * @param expertiseCount 要求匹配的专业领域数量
     * @param consultantIds 已筛选的咨询师ID列表
     * @return 匹配的咨询师列表
     */
    List<PsyConsultant> selectByExpertiseIdsAndConsultantIds(
        @Param("expertiseIds") List<Long> expertiseIds,
        @Param("expertiseCount") Integer expertiseCount,
        @Param("consultantIds") List<Long> consultantIds
    );

    /**
     * 根据性别和价格范围筛选咨询师
     */
    List<PsyConsultant> selectConsultantsByGenderAndPrice(
        @Param("gender") Integer gender,
        @Param("priceRange") Integer priceRange
    );

    /**
     * 根据性别、价格范围和选项ID筛选咨询师
     */
    List<PsyConsultant> selectByGenderPriceAndOption(
        @Param("gender") Integer gender,
        @Param("priceRange") Integer priceRange,
        @Param("optionIds") List<Long> optionIds,
        @Param("optionCount") Integer optionCount
    );

    /**
     * 根据性别、价格范围和咨询领域ID筛选咨询师
     */
    List<PsyConsultant> selectByGenderPriceAndExpertise(
        @Param("gender") Integer gender,
        @Param("priceRange") Integer priceRange,
        @Param("expertiseIds") List<Long> expertiseIds,
        @Param("expertiseCount") Integer expertiseCount
    );

    /**
     * 根据性别、价格范围、选项ID和咨询领域ID筛选咨询师
     */
    List<PsyConsultant> selectByGenderPriceOptionAndExpertise(
        @Param("gender") Integer gender,
        @Param("priceRange") Integer priceRange,
        @Param("optionIds") List<Long> optionIds,
        @Param("optionCount") Integer optionCount,
        @Param("expertiseIds") List<Long> expertiseIds,
        @Param("expertiseCount") Integer expertiseCount
    );
} 