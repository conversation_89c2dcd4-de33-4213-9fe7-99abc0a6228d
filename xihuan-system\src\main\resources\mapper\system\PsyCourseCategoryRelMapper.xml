<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCourseCategoryRelMapper">

    <resultMap id="BaseResultMap" type="PsyCourseCategoryRel">
        <result column="course_id" property="courseId"/>
        <result column="category_id" property="categoryId"/>
    </resultMap>

    <resultMap id="RelWithDetailsMap" type="PsyCourseCategoryRel" extends="BaseResultMap">
        <association property="course" javaType="PsyCourse">
            <id column="course_id" property="id"/>
            <result column="course_title" property="title"/>
            <result column="course_status" property="status"/>
        </association>
        <association property="category" javaType="PsyCategory">
            <id column="category_id" property="categoryId"/>
            <result column="category_name" property="categoryName"/>
            <result column="parent_id" property="parentId"/>
        </association>
    </resultMap>

    <select id="selectRelList" parameterType="PsyCourseCategoryRel" resultMap="BaseResultMap">
        SELECT * FROM psy_course_category_rel
        <where>
            <if test="courseId != null">AND course_id = #{courseId}</if>
            <if test="categoryId != null">AND category_id = #{categoryId}</if>
        </where>
        ORDER BY course_id, category_id
    </select>

    <select id="selectCategoryIdsByCourseId" resultType="Long">
        SELECT category_id FROM psy_course_category_rel WHERE course_id = #{courseId}
    </select>

    <select id="selectCourseIdsByCategoryId" resultType="Long">
        SELECT course_id FROM psy_course_category_rel WHERE category_id = #{categoryId}
    </select>

    <insert id="insertRel" parameterType="PsyCourseCategoryRel">
        INSERT INTO psy_course_category_rel (course_id, category_id)
        VALUES (#{courseId}, #{categoryId})
    </insert>

    <insert id="batchInsertRel">
        INSERT INTO psy_course_category_rel (course_id, category_id) VALUES
        <foreach collection="categoryIds" item="categoryId" separator=",">
            (#{courseId}, #{categoryId})
        </foreach>
    </insert>

    <delete id="deleteRel">
        DELETE FROM psy_course_category_rel 
        WHERE course_id = #{courseId} AND category_id = #{categoryId}
    </delete>

    <delete id="deleteRelByCourseId">
        DELETE FROM psy_course_category_rel WHERE course_id = #{courseId}
    </delete>

    <delete id="deleteRelByCourseIds">
        DELETE FROM psy_course_category_rel
        WHERE course_id IN
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>

    <delete id="deleteRelByCategoryId">
        DELETE FROM psy_course_category_rel WHERE category_id = #{categoryId}
    </delete>

    <delete id="deleteRelByCategoryIds">
        DELETE FROM psy_course_category_rel
        WHERE category_id IN
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>

    <select id="checkRelExists" resultType="int">
        SELECT COUNT(*) FROM psy_course_category_rel 
        WHERE course_id = #{courseId} AND category_id = #{categoryId}
    </select>

</mapper>
