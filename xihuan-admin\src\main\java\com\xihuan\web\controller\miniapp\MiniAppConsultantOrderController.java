package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantOrder;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsyConsultantOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序咨询订单Controller（用户端）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/consultant/order")
public class MiniAppConsultantOrderController extends BaseController {
    
    @Autowired
    private IPsyConsultantOrderService orderService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 创建咨询订单
     */
    @PostMapping("/create")
    public AjaxResult createOrder(@RequestBody PsyConsultantOrder order, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            // 设置用户信息
            order.setUserId(loginUser.getUserId());
            order.setCreateBy(loginUser.getUsername());
            
            int result = orderService.insertOrder(order);
            if (result > 0) {
                Map<String, Object> data = new HashMap<>();
                data.put("orderNo", order.getOrderNo());
                data.put("orderId", order.getId());
                data.put("paymentAmount", order.getPaymentAmount());
                return success(data);
            } else {
                return error("创建订单失败");
            }
        } catch (Exception e) {
            logger.error("创建订单失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 获取我的订单列表
     */
    @GetMapping("/myOrders")
    public AjaxResult getMyOrders(@RequestParam(required = false) String status, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            List<PsyConsultantOrder> orders = orderService.selectOrdersByUserId(loginUser.getUserId());
            
            // 如果指定了状态，进行过滤
            if (status != null && !status.trim().isEmpty()) {
                orders = orders.stream()
                    .filter(order -> status.equals(order.getStatus()))
                    .collect(java.util.stream.Collectors.toList());
            }
            
            return success(orders);
        } catch (Exception e) {
            logger.error("获取我的订单失败", e);
            return error("获取订单列表失败");
        }
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/{orderId}")
    public AjaxResult getOrderDetails(@PathVariable Long orderId, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            PsyConsultantOrder order = orderService.selectOrderWithDetails(orderId);
            if (order == null) {
                return error("订单不存在");
            }
            
            // 验证订单归属
            if (!order.getUserId().equals(loginUser.getUserId())) {
                return error("无权限访问该订单");
            }
            
            return success(order);
        } catch (Exception e) {
            logger.error("获取订单详情失败", e);
            return error("获取订单详情失败");
        }
    }

    /**
     * 取消订单
     */
    @PostMapping("/{orderId}/cancel")
    public AjaxResult cancelOrder(@PathVariable Long orderId, 
                                @RequestParam String cancelReason, 
                                HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            // 验证订单归属
            PsyConsultantOrder order = orderService.selectOrderById(orderId);
            if (order == null || !order.getUserId().equals(loginUser.getUserId())) {
                return error("订单不存在或无权限操作");
            }
            
            int result = orderService.cancelOrder(orderId, cancelReason);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("取消订单失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 检查时间段是否可预约
     */
    @GetMapping("/checkAvailable")
    public AjaxResult checkTimeAvailable(@RequestParam Long consultantId,
                                       @RequestParam Date startTime,
                                       @RequestParam Integer duration) {
        try {
            Date endTime = new Date(startTime.getTime() + duration * 60 * 1000);
            boolean hasConflict = orderService.checkTimeConflict(consultantId, startTime, endTime, null);
            
            Map<String, Object> result = new HashMap<>();
            result.put("available", !hasConflict);
            result.put("message", hasConflict ? "该时间段已被预约" : "时间段可用");
            
            return success(result);
        } catch (Exception e) {
            logger.error("检查时间可用性失败", e);
            return error("检查时间可用性失败");
        }
    }

    /**
     * 获取订单统计
     */
    @GetMapping("/statistics")
    public AjaxResult getOrderStatistics(HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            Map<String, Object> statistics = orderService.getUserOrderStats(loginUser.getUserId());
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取订单统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取不同状态的订单数量
     */
    @GetMapping("/statusCount")
    public AjaxResult getOrderStatusCount(HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            List<PsyConsultantOrder> allOrders = orderService.selectOrdersByUserId(loginUser.getUserId());
            
            Map<String, Long> statusCount = new HashMap<>();
            statusCount.put("待支付", allOrders.stream().filter(o -> "待支付".equals(o.getStatus())).count());
            statusCount.put("已支付", allOrders.stream().filter(o -> "已支付".equals(o.getStatus())).count());
            statusCount.put("待咨询", allOrders.stream().filter(o -> "待咨询".equals(o.getStatus())).count());
            statusCount.put("咨询中", allOrders.stream().filter(o -> "咨询中".equals(o.getStatus())).count());
            statusCount.put("已完成", allOrders.stream().filter(o -> "已完成".equals(o.getStatus())).count());
            statusCount.put("已取消", allOrders.stream().filter(o -> "已取消".equals(o.getStatus())).count());
            
            return success(statusCount);
        } catch (Exception e) {
            logger.error("获取订单状态统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 申请退款
     */
    @PostMapping("/{orderId}/refund")
    public AjaxResult applyRefund(@PathVariable Long orderId,
                                @RequestParam BigDecimal refundAmount,
                                @RequestParam String refundReason,
                                HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            // 验证订单归属
            PsyConsultantOrder order = orderService.selectOrderById(orderId);
            if (order == null || !order.getUserId().equals(loginUser.getUserId())) {
                return error("订单不存在或无权限操作");
            }
            
            int result = orderService.refundOrder(orderId, refundAmount, refundReason);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("申请退款失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 检查订单是否可以取消
     */
    @GetMapping("/{orderId}/canCancel")
    public AjaxResult checkCanCancel(@PathVariable Long orderId, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            // 验证订单归属
            PsyConsultantOrder order = orderService.selectOrderById(orderId);
            if (order == null || !order.getUserId().equals(loginUser.getUserId())) {
                return error("订单不存在或无权限操作");
            }
            
            boolean canCancel = orderService.canCancel(orderId);
            return success(canCancel);
        } catch (Exception e) {
            logger.error("检查订单是否可取消失败", e);
            return error("检查失败");
        }
    }

    /**
     * 检查订单是否可以退款
     */
    @GetMapping("/{orderId}/canRefund")
    public AjaxResult checkCanRefund(@PathVariable Long orderId, HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            // 验证订单归属
            PsyConsultantOrder order = orderService.selectOrderById(orderId);
            if (order == null || !order.getUserId().equals(loginUser.getUserId())) {
                return error("订单不存在或无权限操作");
            }
            
            boolean canRefund = orderService.canRefund(orderId);
            return success(canRefund);
        } catch (Exception e) {
            logger.error("检查订单是否可退款失败", e);
            return error("检查失败");
        }
    }

    /**
     * 获取即将到期的订单
     */
    @GetMapping("/expiring")
    public AjaxResult getExpiringOrders(HttpServletRequest request) {
        try {
            // 获取当前用户
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            // 获取即将到期的订单（30分钟内）
            List<PsyConsultantOrder> expiringOrders = orderService.selectExpiringOrders(30);
            
            // 过滤出当前用户的订单
            List<PsyConsultantOrder> userExpiringOrders = expiringOrders.stream()
                .filter(order -> order.getUserId().equals(loginUser.getUserId()))
                .collect(java.util.stream.Collectors.toList());
            
            return success(userExpiringOrders);
        } catch (Exception e) {
            logger.error("获取即将到期订单失败", e);
            return error("获取即将到期订单失败");
        }
    }
}
