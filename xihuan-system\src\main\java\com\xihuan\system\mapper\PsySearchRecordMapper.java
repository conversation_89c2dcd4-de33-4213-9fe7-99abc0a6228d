package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsySearchRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 搜索记录Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsySearchRecordMapper {
    
    /**
     * 查询搜索记录
     *
     * @param id 搜索记录主键
     * @return 搜索记录
     */
    PsySearchRecord selectSearchRecordById(Long id);

    /**
     * 查询搜索记录列表
     *
     * @param searchRecord 搜索记录
     * @return 搜索记录集合
     */
    List<PsySearchRecord> selectSearchRecordList(PsySearchRecord searchRecord);

    /**
     * 新增搜索记录
     *
     * @param searchRecord 搜索记录
     * @return 结果
     */
    int insertSearchRecord(PsySearchRecord searchRecord);

    /**
     * 修改搜索记录
     *
     * @param searchRecord 搜索记录
     * @return 结果
     */
    int updateSearchRecord(PsySearchRecord searchRecord);

    /**
     * 删除搜索记录
     *
     * @param id 搜索记录主键
     * @return 结果
     */
    int deleteSearchRecordById(Long id);

    /**
     * 批量删除搜索记录
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSearchRecordByIds(Long[] ids);
    
    /**
     * 获取用户搜索历史
     *
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 搜索历史
     */
    List<String> selectUserSearchHistory(@Param("userId") Long userId, @Param("limit") Integer limit);
    
    /**
     * 获取热门搜索关键词
     *
     * @param searchType 搜索类型
     * @param limit 限制数量
     * @return 热门关键词
     */
    List<Map<String, Object>> selectHotKeywords(@Param("searchType") String searchType, @Param("limit") Integer limit);
    
    /**
     * 统计关键词搜索次数
     *
     * @param keyword 关键词
     * @param searchType 搜索类型
     * @return 搜索次数
     */
    Integer countKeywordSearch(@Param("keyword") String keyword, @Param("searchType") String searchType);
    
    /**
     * 清理过期搜索记录
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanExpiredRecords(@Param("days") Integer days);
}
