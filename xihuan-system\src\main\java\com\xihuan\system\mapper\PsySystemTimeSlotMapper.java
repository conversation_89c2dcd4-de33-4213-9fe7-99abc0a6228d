package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import org.apache.ibatis.annotations.Param;
import java.time.LocalDate;
import java.util.List;

/**
 * 系统公共时间槽Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsySystemTimeSlotMapper {
    
    /**
     * 查询系统时间槽列表
     * 
     * @param systemTimeSlot 系统时间槽
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectSystemTimeSlotList(PsySystemTimeSlot systemTimeSlot);
    
    /**
     * 根据ID查询系统时间槽
     * 
     * @param id 系统时间槽主键
     * @return 系统时间槽
     */
    PsySystemTimeSlot selectSystemTimeSlotById(Long id);
    
    /**
     * 查询指定日期范围的系统时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectSlotsByDateRange(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("centerId") Long centerId
    );
    
    /**
     * 查询指定日期的系统时间槽
     * 
     * @param dateKey 日期
     * @param centerId 咨询中心ID
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectSlotsByDate(
        @Param("dateKey") String dateKey,
        @Param("centerId") Long centerId
    );
    
    /**
     * 查询有可用咨询师的时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectAvailableSlots(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("centerId") Long centerId
    );
    
    /**
     * 新增系统时间槽
     * 
     * @param systemTimeSlot 系统时间槽
     * @return 结果
     */
    int insertSystemTimeSlot(PsySystemTimeSlot systemTimeSlot);
    
    /**
     * 批量新增系统时间槽
     * 
     * @param systemTimeSlots 系统时间槽列表
     * @return 结果
     */
    int batchInsertSystemTimeSlots(@Param("systemTimeSlots") List<PsySystemTimeSlot> systemTimeSlots);
    
    /**
     * 修改系统时间槽
     * 
     * @param systemTimeSlot 系统时间槽
     * @return 结果
     */
    int updateSystemTimeSlot(PsySystemTimeSlot systemTimeSlot);
    
    /**
     * 批量更新系统时间槽的可用性统计
     * 
     * @param dateKey 日期
     * @param centerId 咨询中心ID
     * @return 结果
     */
    int batchUpdateAvailabilityStats(@Param("dateKey") String dateKey, @Param("centerId") Long centerId);
    
    /**
     * 删除系统时间槽
     * 
     * @param id 系统时间槽主键
     * @return 结果
     */
    int deleteSystemTimeSlotById(Long id);
    
    /**
     * 批量删除系统时间槽
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSystemTimeSlotByIds(Long[] ids);
    
    /**
     * 删除指定日期范围的系统时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 结果
     */
    int deleteSlotsByDateRange(
        @Param("startDate") String startDate,
        @Param("endDate") String endDate,
        @Param("centerId") Long centerId
    );
    
    /**
     * 检查系统时间槽是否存在
     * 
     * @param centerId 咨询中心ID
     * @param dateKey 日期
     * @param startTime 开始时间
     * @return 结果
     */
    int checkSystemSlotExists(
        @Param("centerId") Long centerId,
        @Param("dateKey") String dateKey,
        @Param("startTime") String startTime
    );
    
    /**
     * 统计指定时间段的可用咨询师数量
     * 
     * @param centerId 咨询中心ID
     * @param dateKey 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 可用咨询师数量统计
     */
    java.util.Map<String, Object> countAvailableCounselors(
        @Param("centerId") Long centerId,
        @Param("dateKey") String dateKey,
        @Param("startTime") String startTime,
        @Param("endTime") String endTime
    );

    /**
     * 批量更新系统时间槽状态
     *
     * @param slotIds 时间槽ID列表
     * @param status 新状态
     * @return 更新的记录数
     */
    int batchUpdateSlotStatus(@Param("slotIds") java.util.List<Long> slotIds, @Param("status") Integer status);

    /**
     * 更新过期的系统时间槽状态
     *
     * @param centerId 咨询中心ID
     * @param delayHours 延后小时数（0表示不延后）
     * @return 更新的记录数
     */
    int updateExpiredSlotStatus(@Param("centerId") Long centerId, @Param("delayHours") Integer delayHours);

    /**
     * 清理过期的系统时间槽
     *
     * @param beforeDate 清理此日期之前的数据
     * @param centerId 咨询中心ID
     * @return 清理的记录数
     */
    int cleanExpiredSystemSlots(@Param("beforeDate") java.time.LocalDate beforeDate, @Param("centerId") Long centerId);
}
