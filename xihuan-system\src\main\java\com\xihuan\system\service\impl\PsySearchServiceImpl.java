package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.dto.SearchResultDTO;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.*;
import com.xihuan.system.service.IPsySearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 搜索服务实现
 * 
 * <AUTHOR>
 */
@Service
public class PsySearchServiceImpl implements IPsySearchService {
    
    @Autowired
    private PsySearchRecordMapper searchRecordMapper;
    
    @Autowired
    private PsyHotSearchMapper hotSearchMapper;
    
    @Autowired
    private PsySearchSuggestionMapper searchSuggestionMapper;
    
    // 各模块的Mapper
    @Autowired(required = false)
    private PsyConsultantMapper consultantMapper;

    @Autowired(required = false)
    private PsyCourseMapper courseMapper;

    @Autowired(required = false)
    private PsyMeditationMapper meditationMapper;



    @Override
    @Transactional
    public SearchResultDTO globalSearch(String keyword, String searchType, Integer pageNum, Integer pageSize, 
                                       Long userId, String ipAddress) {
        long startTime = System.currentTimeMillis();
        
        SearchResultDTO result = new SearchResultDTO();
        result.setKeyword(keyword);
        result.setSearchType(searchType);
        
        List<SearchResultDTO.CategoryResult> categories = new ArrayList<>();
        int totalCount = 0;
        
        try {
            // 根据搜索类型进行搜索
            if ("all".equals(searchType) || "consultant".equals(searchType)) {
                SearchResultDTO.CategoryResult consultantResult = searchConsultants(keyword, pageNum, pageSize);
                if (consultantResult.getCount() > 0) {
                    categories.add(consultantResult);
                    totalCount += consultantResult.getCount();
                }
            }

            if ("all".equals(searchType) || "course".equals(searchType)) {
                SearchResultDTO.CategoryResult courseResult = searchCourses(keyword, pageNum, pageSize);
                if (courseResult.getCount() > 0) {
                    categories.add(courseResult);
                    totalCount += courseResult.getCount();
                }
            }

            if ("all".equals(searchType) || "meditation".equals(searchType)) {
                SearchResultDTO.CategoryResult meditationResult = searchMeditations(keyword, pageNum, pageSize);
                if (meditationResult.getCount() > 0) {
                    categories.add(meditationResult);
                    totalCount += meditationResult.getCount();
                }
            }


            
            result.setCategories(categories);
            result.setTotalCount(totalCount);
            
            // 获取搜索建议
            result.setSuggestions(getSearchSuggestions(keyword, userId));
            
        } catch (Exception e) {
            e.printStackTrace();
            result.setTotalCount(0);
            result.setCategories(new ArrayList<>());
        }
        
        long endTime = System.currentTimeMillis();
        result.setSearchTime(endTime - startTime);
        
        // 记录搜索行为
        recordSearch(keyword, searchType, totalCount, userId, ipAddress, null);
        
        // 更新热门搜索
        updateHotSearch(keyword, searchType);
        
        return result;
    }
    
    /**
     * 搜索咨询师
     */
    private SearchResultDTO.CategoryResult searchConsultants(String keyword, Integer pageNum, Integer pageSize) {
        SearchResultDTO.CategoryResult result = new SearchResultDTO.CategoryResult();
        result.setType("consultant");
        result.setTypeName("咨询师");
        result.setItems(new ArrayList<>());
        result.setCount(0);

        if (consultantMapper == null) {
            return result;
        }

        try {
            // 获取所有可用咨询师
            List<PsyConsultant> consultants = consultantMapper.selectAllConsultantsWithDetails();

            // 过滤匹配关键词的咨询师
            List<PsyConsultant> filteredConsultants = consultants.stream()
                .filter(consultant -> matchesConsultantKeyword(consultant, keyword))
                .collect(Collectors.toList());

            result.setCount(filteredConsultants.size());

            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, filteredConsultants.size());

            if (start < filteredConsultants.size()) {
                List<PsyConsultant> pageConsultants = filteredConsultants.subList(start, end);

                for (PsyConsultant consultant : pageConsultants) {
                    SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
                    item.setId(consultant.getId());
                    item.setType("consultant");
                    item.setTitle(consultant.getName());
                    item.setDescription(consultant.getPersonalIntro());
                    item.setCoverImage(consultant.getImageUrl());
                    item.setCreateTime(consultant.getCreateTime());
                    item.setRelevanceScore(calculateRelevanceScore(consultant.getName(), keyword));
                    item.setPrice(consultant.getPrice() != null ? consultant.getPrice().toString() : "");

                    // 高亮处理
                    item.setHighlightTitle(highlightKeyword(consultant.getName(), keyword));
                    item.setHighlightDescription(highlightKeyword(consultant.getPersonalIntro(), keyword));

                    result.getItems().add(item);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }
    
    /**
     * 搜索课程
     */
    private SearchResultDTO.CategoryResult searchCourses(String keyword, Integer pageNum, Integer pageSize) {
        SearchResultDTO.CategoryResult result = new SearchResultDTO.CategoryResult();
        result.setType("course");
        result.setTypeName("课程");
        result.setItems(new ArrayList<>());
        result.setCount(0);

        if (courseMapper == null) {
            return result;
        }

        try {
            // 构建搜索条件
            PsyCourse queryCourse = new PsyCourse();
            queryCourse.setStatus(1); // 只搜索已发布的课程

            List<PsyCourse> courses = courseMapper.selectCourseList(queryCourse);

            // 过滤匹配关键词的课程
            List<PsyCourse> filteredCourses = courses.stream()
                .filter(course -> matchesCourseKeyword(course, keyword))
                .collect(Collectors.toList());

            result.setCount(filteredCourses.size());

            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, filteredCourses.size());

            if (start < filteredCourses.size()) {
                List<PsyCourse> pageCourses = filteredCourses.subList(start, end);

                for (PsyCourse course : pageCourses) {
                    SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
                    item.setId(course.getId());
                    item.setType("course");
                    item.setTitle(course.getTitle());
                    item.setDescription(course.getSummary());
                    item.setCoverImage(course.getCoverImage());
                    item.setCreateTime(course.getCreateTime());
                    item.setRelevanceScore(calculateRelevanceScore(course.getTitle(), keyword));
                    item.setPrice(course.getPrice() != null ? course.getPrice().toString() : "");
                    item.setRating(course.getRatingAvg() != null ? course.getRatingAvg().doubleValue() : null);
                    item.setViewCount(course.getViewCount());

                    // 高亮处理
                    item.setHighlightTitle(highlightKeyword(course.getTitle(), keyword));
                    item.setHighlightDescription(highlightKeyword(course.getSummary(), keyword));

                    result.getItems().add(item);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }

    /**
     * 搜索冥想
     */
    private SearchResultDTO.CategoryResult searchMeditations(String keyword, Integer pageNum, Integer pageSize) {
        SearchResultDTO.CategoryResult result = new SearchResultDTO.CategoryResult();
        result.setType("meditation");
        result.setTypeName("冥想");
        result.setItems(new ArrayList<>());
        result.setCount(0);

        if (meditationMapper == null) {
            return result;
        }

        try {
            // 构建搜索条件
            PsyMeditation queryMeditation = new PsyMeditation();
            queryMeditation.setStatus(1); // 只搜索已发布的冥想

            List<PsyMeditation> meditations = meditationMapper.selectMeditationList(queryMeditation);

            // 过滤匹配关键词的冥想
            List<PsyMeditation> filteredMeditations = meditations.stream()
                .filter(meditation -> matchesMeditationKeyword(meditation, keyword))
                .collect(Collectors.toList());

            result.setCount(filteredMeditations.size());

            // 分页处理
            int start = (pageNum - 1) * pageSize;
            int end = Math.min(start + pageSize, filteredMeditations.size());

            if (start < filteredMeditations.size()) {
                List<PsyMeditation> pageMeditations = filteredMeditations.subList(start, end);

                for (PsyMeditation meditation : pageMeditations) {
                    SearchResultDTO.SearchItem item = new SearchResultDTO.SearchItem();
                    item.setId(meditation.getId());
                    item.setType("meditation");
                    item.setTitle(meditation.getTitle());
                    item.setDescription(meditation.getDescription());
                    item.setCoverImage(meditation.getCoverImage());
                    item.setCreateTime(meditation.getCreateTime());
                    item.setRelevanceScore(calculateRelevanceScore(meditation.getTitle(), keyword));
                    item.setPrice(meditation.getPrice() != null ? meditation.getPrice().toString() : "");
                    item.setViewCount(meditation.getPlayCount());

                    // 高亮处理
                    item.setHighlightTitle(highlightKeyword(meditation.getTitle(), keyword));
                    item.setHighlightDescription(highlightKeyword(meditation.getDescription(), keyword));

                    result.getItems().add(item);
                }
            }

        } catch (Exception e) {
            e.printStackTrace();
        }

        return result;
    }



    /**
     * 检查咨询师是否匹配关键词
     */
    private boolean matchesConsultantKeyword(PsyConsultant consultant, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();

        return (StringUtils.isNotEmpty(consultant.getName()) &&
                consultant.getName().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(consultant.getPersonalIntro()) &&
                consultant.getPersonalIntro().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(consultant.getPersonalTitle()) &&
                consultant.getPersonalTitle().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(consultant.getSearchKeywords()) &&
                consultant.getSearchKeywords().toLowerCase().contains(lowerKeyword));
    }

    /**
     * 检查课程是否匹配关键词
     */
    private boolean matchesCourseKeyword(PsyCourse course, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();

        return (StringUtils.isNotEmpty(course.getTitle()) &&
                course.getTitle().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(course.getSummary()) &&
                course.getSummary().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(course.getTags()) &&
                course.getTags().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(course.getSearchKeywords()) &&
                course.getSearchKeywords().toLowerCase().contains(lowerKeyword));
    }

    /**
     * 检查冥想是否匹配关键词
     */
    private boolean matchesMeditationKeyword(PsyMeditation meditation, String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return true;
        }

        String lowerKeyword = keyword.toLowerCase();

        return (StringUtils.isNotEmpty(meditation.getTitle()) &&
                meditation.getTitle().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getDescription()) &&
                meditation.getDescription().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getNarrator()) &&
                meditation.getNarrator().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getTags()) &&
                meditation.getTags().toLowerCase().contains(lowerKeyword)) ||
               (StringUtils.isNotEmpty(meditation.getSearchKeywords()) &&
                meditation.getSearchKeywords().toLowerCase().contains(lowerKeyword));
    }


    
    /**
     * 计算相关度分数
     */
    private Double calculateRelevanceScore(String text, String keyword) {
        if (StringUtils.isEmpty(text) || StringUtils.isEmpty(keyword)) {
            return 0.0;
        }
        
        String lowerText = text.toLowerCase();
        String lowerKeyword = keyword.toLowerCase();
        
        // 简单的相关度计算：完全匹配得分最高，包含匹配次之
        if (lowerText.equals(lowerKeyword)) {
            return 100.0;
        } else if (lowerText.contains(lowerKeyword)) {
            // 根据匹配位置和长度计算分数
            int index = lowerText.indexOf(lowerKeyword);
            double positionScore = index == 0 ? 1.0 : 1.0 / (index + 1);
            double lengthScore = (double) lowerKeyword.length() / lowerText.length();
            return (positionScore + lengthScore) * 50;
        }
        
        return 0.0;
    }
    
    /**
     * 高亮关键词
     */
    private String highlightKeyword(String text, String keyword) {
        if (StringUtils.isEmpty(text) || StringUtils.isEmpty(keyword)) {
            return text;
        }
        
        // 简单的高亮处理，实际项目中可能需要更复杂的逻辑
        return text.replaceAll("(?i)" + keyword, "<em>" + keyword + "</em>");
    }

    @Override
    public List<String> getSearchSuggestions(String keyword, Long userId) {
        List<String> suggestions = new ArrayList<>();
        
        try {
            // 1. 基于关键词前缀的建议
            if (StringUtils.isNotEmpty(keyword)) {
                List<String> keywordSuggestions = searchSuggestionMapper.selectSuggestionsByKeyword(keyword, 5);
                suggestions.addAll(keywordSuggestions);
            }
            
            // 2. 基于用户历史搜索
            if (userId != null) {
                List<String> historySuggestions = getUserSearchHistory(userId, 3);
                suggestions.addAll(historySuggestions);
            }
            
            // 3. 基于热门搜索
            List<PsyHotSearch> hotSearches = hotSearchMapper.selectHotSearchByType("all", 5);
            for (PsyHotSearch hotSearch : hotSearches) {
                suggestions.add(hotSearch.getKeyword());
            }
            
            // 去重并限制数量
            suggestions = suggestions.stream()
                .distinct()
                .limit(10)
                .collect(Collectors.toList());
                
        } catch (Exception e) {
            e.printStackTrace();
        }
        
        return suggestions;
    }

    @Override
    public List<PsyHotSearch> getHotSearches(String searchType, Integer limit) {
        try {
            return hotSearchMapper.selectHotSearchByType(searchType, limit);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public void recordSearch(String keyword, String searchType, Integer resultCount, 
                           Long userId, String ipAddress, String userAgent) {
        try {
            PsySearchRecord record = new PsySearchRecord();
            record.setUserId(userId);
            record.setKeyword(keyword);
            record.setSearchType(searchType);
            record.setResultCount(resultCount);
            record.setSearchTime(DateUtils.getNowDate());
            record.setIpAddress(ipAddress);
            record.setUserAgent(userAgent);
            record.setDelFlag("0");
            
            searchRecordMapper.insertSearchRecord(record);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    @Transactional
    public void updateHotSearch(String keyword, String searchType) {
        try {
            PsyHotSearch existingHotSearch = hotSearchMapper.selectByKeywordAndType(keyword, searchType);
            
            if (existingHotSearch != null) {
                // 更新现有记录
                hotSearchMapper.incrementSearchCount(keyword, searchType);
                
                // 重新计算热度分数
                BigDecimal hotScore = calculateHotScore(existingHotSearch.getSearchCount() + 1);
                hotSearchMapper.updateHotScore(existingHotSearch.getId(), hotScore);
            } else {
                // 创建新记录
                PsyHotSearch newHotSearch = new PsyHotSearch();
                newHotSearch.setKeyword(keyword);
                newHotSearch.setSearchType(searchType);
                newHotSearch.setSearchCount(1);
                newHotSearch.setLastSearchTime(DateUtils.getNowDate());
                newHotSearch.setHotScore(calculateHotScore(1));
                newHotSearch.setStatus("0");
                newHotSearch.setCreateTime(DateUtils.getNowDate());
                
                hotSearchMapper.insertHotSearch(newHotSearch);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
    
    /**
     * 计算热度分数
     */
    private BigDecimal calculateHotScore(Integer searchCount) {
        // 简单的热度计算公式，可以根据需要调整
        return new BigDecimal(searchCount * 10);
    }

    @Override
    public List<String> getUserSearchHistory(Long userId, Integer limit) {
        try {
            return searchRecordMapper.selectUserSearchHistory(userId, limit);
        } catch (Exception e) {
            e.printStackTrace();
            return new ArrayList<>();
        }
    }

    @Override
    @Transactional
    public boolean clearUserSearchHistory(Long userId) {
        try {
            // 这里可以实现软删除或硬删除
            // 暂时返回true
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    // 搜索记录管理方法
    @Override
    public List<PsySearchRecord> selectSearchRecordList(PsySearchRecord searchRecord) {
        return searchRecordMapper.selectSearchRecordList(searchRecord);
    }

    @Override
    public int insertSearchRecord(PsySearchRecord searchRecord) {
        searchRecord.setCreateTime(DateUtils.getNowDate());
        return searchRecordMapper.insertSearchRecord(searchRecord);
    }

    @Override
    public int updateSearchRecord(PsySearchRecord searchRecord) {
        searchRecord.setUpdateTime(DateUtils.getNowDate());
        return searchRecordMapper.updateSearchRecord(searchRecord);
    }

    @Override
    public int deleteSearchRecordByIds(Long[] ids) {
        return searchRecordMapper.deleteSearchRecordByIds(ids);
    }

    // 热门搜索管理方法
    @Override
    public List<PsyHotSearch> selectHotSearchList(PsyHotSearch hotSearch) {
        return hotSearchMapper.selectHotSearchList(hotSearch);
    }

    @Override
    public int insertHotSearch(PsyHotSearch hotSearch) {
        hotSearch.setCreateTime(DateUtils.getNowDate());
        return hotSearchMapper.insertHotSearch(hotSearch);
    }

    @Override
    public int updateHotSearch(PsyHotSearch hotSearch) {
        hotSearch.setUpdateTime(DateUtils.getNowDate());
        return hotSearchMapper.updateHotSearch(hotSearch);
    }

    @Override
    public int deleteHotSearchByIds(Long[] ids) {
        return hotSearchMapper.deleteHotSearchByIds(ids);
    }

    // 搜索建议管理方法
    @Override
    public List<PsySearchSuggestion> selectSearchSuggestionList(PsySearchSuggestion searchSuggestion) {
        return searchSuggestionMapper.selectSearchSuggestionList(searchSuggestion);
    }

    @Override
    public int insertSearchSuggestion(PsySearchSuggestion searchSuggestion) {
        searchSuggestion.setCreateTime(DateUtils.getNowDate());
        return searchSuggestionMapper.insertSearchSuggestion(searchSuggestion);
    }

    @Override
    public int updateSearchSuggestion(PsySearchSuggestion searchSuggestion) {
        searchSuggestion.setUpdateTime(DateUtils.getNowDate());
        return searchSuggestionMapper.updateSearchSuggestion(searchSuggestion);
    }

    @Override
    public int deleteSearchSuggestionByIds(Long[] ids) {
        return searchSuggestionMapper.deleteSearchSuggestionByIds(ids);
    }
}
