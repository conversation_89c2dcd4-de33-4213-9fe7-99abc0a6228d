-- 系统公共时间槽表
CREATE TABLE `psy_system_time_slot`
(
    `id`                    bigint      NOT NULL AUTO_INCREMENT COMMENT '系统时间槽ID',
    `center_id`             bigint      NOT NULL COMMENT '咨询中心ID',
    `date_key`              varchar(10) NOT NULL COMMENT '日期KEY（YYYY-MM-DD）',
    `week_day`              varchar(5)  DEFAULT NULL COMMENT '星期几',
    `range_id`              bigint      NOT NULL COMMENT '所属时间段ID',
    `start_time`            time        NOT NULL COMMENT '开始时间',
    `end_time`              time        NOT NULL COMMENT '结束时间',
    `available_counselors`  int         DEFAULT '0' COMMENT '可用咨询师数量',
    `total_counselors`      int         DEFAULT '0' COMMENT '总咨询师数量',
    `has_available`         tinyint(1)  DEFAULT '0' COMMENT '是否有可用咨询师',
    `del_flag`              tinyint(1)  DEFAULT '0' COMMENT '删除标志',
    `create_time`           datetime    DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time`           datetime    DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uniq_system_slot` (`center_id`, `date_key`, `start_time`),
    KEY `idx_date_range` (`date_key`, `range_id`),
    KEY `idx_available` (`has_available`, `available_counselors`)
) ENGINE = InnoDB COMMENT ='系统公共时间槽表';

-- 插入示例数据（可选）
-- 这个表的数据主要通过程序自动生成，不需要手动插入
