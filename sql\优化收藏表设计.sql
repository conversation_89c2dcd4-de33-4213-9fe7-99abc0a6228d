-- 优化用户收藏表设计
-- 支持咨询师、测评、冥想、课程的收藏功能

-- 1. 删除原有收藏表
DROP TABLE IF EXISTS `psy_user_favorite`;

-- 2. 创建新的收藏表
CREATE TABLE `psy_user_favorite` (
  `favorite_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '收藏ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `target_type` TINYINT(1) NOT NULL COMMENT '收藏目标类型（1：咨询师，2：课程，3：冥想，4：测评）',
  `target_id` BIGINT(20) NOT NULL COMMENT '目标对象ID',
  `target_title` VARCHAR(200) COMMENT '目标对象标题（冗余字段，便于查询）',
  `target_image` VARCHAR(500) COMMENT '目标对象图片（冗余字段，便于查询）',
  `sort` INT(11) DEFAULT 0 COMMENT '排序（用户可自定义收藏顺序）',
  `tags` VARCHAR(500) COMMENT '用户自定义标签（多个标签用逗号分隔）',
  `notes` TEXT COMMENT '用户备注',
  `is_public` TINYINT(1) DEFAULT 0 COMMENT '是否公开收藏（0私有 1公开）',
  `favorite_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '收藏时间',
  `last_view_time` DATETIME COMMENT '最后查看时间',
  `view_count` INT(11) DEFAULT 0 COMMENT '查看次数',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志（0存在 2删除）',
  `create_by` VARCHAR(64) DEFAULT '' COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) DEFAULT '' COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`favorite_id`),
  UNIQUE KEY `uniq_user_target` (`user_id`, `target_type`, `target_id`),
  KEY `idx_user_type_time` (`user_id`, `target_type`, `favorite_time`),
  KEY `idx_target` (`target_type`, `target_id`),
  KEY `idx_public_time` (`is_public`, `favorite_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户收藏表';

-- 3. 创建收藏统计表（用于统计各类内容的收藏数）
CREATE TABLE `psy_favorite_statistics` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '统计ID',
  `target_type` TINYINT(1) NOT NULL COMMENT '目标类型',
  `target_id` BIGINT(20) NOT NULL COMMENT '目标ID',
  `favorite_count` INT(11) DEFAULT 0 COMMENT '收藏总数',
  `today_count` INT(11) DEFAULT 0 COMMENT '今日收藏数',
  `week_count` INT(11) DEFAULT 0 COMMENT '本周收藏数',
  `month_count` INT(11) DEFAULT 0 COMMENT '本月收藏数',
  `last_favorite_time` DATETIME COMMENT '最后收藏时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_target` (`target_type`, `target_id`),
  KEY `idx_favorite_count` (`target_type`, `favorite_count`),
  KEY `idx_last_time` (`last_favorite_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏统计表';

-- 4. 创建收藏分组表（用户可以创建收藏夹）
CREATE TABLE `psy_favorite_group` (
  `group_id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分组ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `group_name` VARCHAR(100) NOT NULL COMMENT '分组名称',
  `group_icon` VARCHAR(200) COMMENT '分组图标',
  `group_color` VARCHAR(20) COMMENT '分组颜色',
  `description` VARCHAR(500) COMMENT '分组描述',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `is_default` TINYINT(1) DEFAULT 0 COMMENT '是否默认分组',
  `is_public` TINYINT(1) DEFAULT 0 COMMENT '是否公开',
  `favorite_count` INT(11) DEFAULT 0 COMMENT '收藏数量',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态（0停用 1启用）',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`group_id`),
  KEY `idx_user_sort` (`user_id`, `sort`),
  KEY `idx_public_time` (`is_public`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏分组表';

-- 5. 创建收藏分组关系表
CREATE TABLE `psy_favorite_group_rel` (
  `favorite_id` BIGINT(20) NOT NULL COMMENT '收藏ID',
  `group_id` BIGINT(20) NOT NULL COMMENT '分组ID',
  `sort` INT(11) DEFAULT 0 COMMENT '在分组中的排序',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`favorite_id`, `group_id`),
  KEY `idx_group_sort` (`group_id`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='收藏分组关系表';

-- 6. 初始化默认数据
-- 为每个用户创建默认收藏分组（可以通过触发器或应用程序逻辑实现）

-- 7. 创建视图，便于查询收藏详情
CREATE VIEW `v_user_favorite_details` AS
SELECT 
    f.favorite_id,
    f.user_id,
    f.target_type,
    f.target_id,
    f.target_title,
    f.target_image,
    f.sort,
    f.tags,
    f.notes,
    f.is_public,
    f.favorite_time,
    f.last_view_time,
    f.view_count,
    CASE f.target_type
        WHEN 1 THEN '咨询师'
        WHEN 2 THEN '课程'
        WHEN 3 THEN '冥想'
        WHEN 4 THEN '测评'
        ELSE '未知'
    END as target_type_name,
    -- 根据类型关联不同表获取详细信息
    CASE f.target_type
        WHEN 1 THEN c.name
        WHEN 2 THEN course.title
        WHEN 3 THEN med.title
#         WHEN 4 THEN scale.name
    END as actual_title,
    CASE f.target_type
        WHEN 1 THEN c.image_url
        WHEN 2 THEN course.cover_image
        WHEN 3 THEN med.cover_image
#         WHEN 4 THEN scale.image_url
    END as actual_image,
    CASE f.target_type
        WHEN 1 THEN c.personal_intro
        WHEN 2 THEN course.summary
        WHEN 3 THEN med.description
#         WHEN 4 THEN scale.description
    END as description,
    CASE f.target_type
        WHEN 1 THEN c.price
        WHEN 2 THEN course.price
        WHEN 3 THEN med.price
#         WHEN 4 THEN scale.price
    END as price,
    CASE f.target_type
#         WHEN 1 THEN c.status
        WHEN 2 THEN course.status
        WHEN 3 THEN med.status
#         WHEN 4 THEN scale.status
    END as target_status
FROM psy_user_favorite f
LEFT JOIN psy_consultants c ON f.target_type = 1 AND f.target_id = c.id AND c.del_flag = '0'
LEFT JOIN psy_course course ON f.target_type = 2 AND f.target_id = course.id AND course.del_flag = '0'
LEFT JOIN psy_meditation med ON f.target_type = 3 AND f.target_id = med.id AND med.del_flag = '0'
# LEFT JOIN psy_scale scale ON f.target_type = 4 AND f.target_id = scale.id AND scale.del_flag = '0'
WHERE f.del_flag = '0';

-- 8. 创建触发器，自动更新收藏统计
DELIMITER $$

CREATE TRIGGER `tr_favorite_insert_stats` 
AFTER INSERT ON `psy_user_favorite`
FOR EACH ROW
BEGIN
    INSERT INTO psy_favorite_statistics (target_type, target_id, favorite_count, today_count, week_count, month_count, last_favorite_time)
    VALUES (NEW.target_type, NEW.target_id, 1, 1, 1, 1, NEW.favorite_time)
    ON DUPLICATE KEY UPDATE
        favorite_count = favorite_count + 1,
        today_count = CASE WHEN DATE(last_favorite_time) = CURDATE() THEN today_count + 1 ELSE 1 END,
        week_count = CASE WHEN YEARWEEK(last_favorite_time) = YEARWEEK(NOW()) THEN week_count + 1 ELSE 1 END,
        month_count = CASE WHEN DATE_FORMAT(last_favorite_time, '%Y-%m') = DATE_FORMAT(NOW(), '%Y-%m') THEN month_count + 1 ELSE 1 END,
        last_favorite_time = NEW.favorite_time;
END$$

CREATE TRIGGER `tr_favorite_delete_stats`
AFTER UPDATE ON `psy_user_favorite`
FOR EACH ROW
BEGIN
    IF NEW.del_flag = '2' AND OLD.del_flag = '0' THEN
        UPDATE psy_favorite_statistics 
        SET favorite_count = GREATEST(favorite_count - 1, 0)
        WHERE target_type = OLD.target_type AND target_id = OLD.target_id;
    END IF;
END$$

DELIMITER ;

-- 9. 创建定时清理任务（需要在应用程序中实现）
-- 定期清理统计表中的today_count, week_count, month_count字段
