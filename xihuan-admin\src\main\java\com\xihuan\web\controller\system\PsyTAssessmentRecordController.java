package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTAssessmentRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 测评记录Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/assessment/record")
public class PsyTAssessmentRecordController extends BaseController {
    
    @Autowired
    private IPsyTAssessmentRecordService recordService;

    /**
     * 查询测评记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTAssessmentRecord record) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectRecordList(record);
        return getDataTable(list);
    }

    /**
     * 导出测评记录列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:export')")
    @Log(title = "测评记录", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTAssessmentRecord record) {
        List<PsyTAssessmentRecord> list = recordService.selectRecordList(record);
        ExcelUtil<PsyTAssessmentRecord> util = new ExcelUtil<>(PsyTAssessmentRecord.class);
        util.exportExcel(response, list, "测评记录数据");
    }

    /**
     * 获取测评记录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(recordService.selectRecordWithDetails(id));
    }

    /**
     * 根据会话ID查询测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/session/{sessionId}")
    public AjaxResult getBySessionId(@PathVariable("sessionId") String sessionId) {
        return success(recordService.selectRecordBySessionId(sessionId));
    }

    /**
     * 新增测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:add')")
    @Log(title = "测评记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTAssessmentRecord record) {
        return toAjax(recordService.insertRecord(record));
    }

    /**
     * 修改测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "测评记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTAssessmentRecord record) {
        return toAjax(recordService.updateRecord(record));
    }

    /**
     * 删除测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:remove')")
    @Log(title = "测评记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(recordService.deleteRecordByIds(ids));
    }

    /**
     * 开始测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:add')")
    @Log(title = "测评记录", businessType = BusinessType.INSERT)
    @PostMapping("/start")
    public AjaxResult startAssessment(@RequestParam Long userId, @RequestParam Long scaleId) {
        PsyTAssessmentRecord record = recordService.startAssessment(userId, scaleId);
        return success(record);
    }

    /**
     * 完成测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "测评记录", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/complete")
    public AjaxResult completeAssessment(@PathVariable("id") Long id) {
        return toAjax(recordService.completeAssessment(id));
    }

    /**
     * 放弃测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "测评记录", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/abandon")
    public AjaxResult abandonAssessment(@PathVariable("id") Long id) {
        return toAjax(recordService.abandonAssessment(id));
    }

    /**
     * 计算测评结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @PostMapping("/{id}/calculate")
    public AjaxResult calculateResult(@PathVariable("id") Long id) {
        Map<String, Object> result = recordService.calculateAssessmentResult(id);
        return success(result);
    }

    /**
     * 生成测评报告
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @PostMapping("/{id}/report")
    public AjaxResult generateReport(@PathVariable("id") Long id) {
        Map<String, Object> report = recordService.generateAssessmentReport(id);
        return success(report);
    }

    /**
     * 查询用户测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/user/{userId}")
    public TableDataInfo getUserRecords(@PathVariable("userId") Long userId) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectRecordsByUserId(userId);
        return getDataTable(list);
    }

    /**
     * 查询量表测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/scale/{scaleId}")
    public TableDataInfo getScaleRecords(@PathVariable("scaleId") Long scaleId) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectRecordsByScaleId(scaleId);
        return getDataTable(list);
    }

    /**
     * 查询用户在指定量表的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/user/{userId}/scale/{scaleId}")
    public TableDataInfo getUserScaleRecords(
            @PathVariable("userId") Long userId,
            @PathVariable("scaleId") Long scaleId) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectUserRecordsByScaleId(userId, scaleId);
        return getDataTable(list);
    }

    /**
     * 查询进行中的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/in-progress")
    public TableDataInfo getInProgressRecords(@RequestParam(required = false) Long userId) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectInProgressRecords(userId);
        return getDataTable(list);
    }

    /**
     * 查询已完成的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/completed")
    public TableDataInfo getCompletedRecords(@RequestParam(required = false) Long userId) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectCompletedRecords(userId);
        return getDataTable(list);
    }

    /**
     * 查询已放弃的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/abandoned")
    public TableDataInfo getAbandonedRecords(@RequestParam(required = false) Long userId) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectAbandonedRecords(userId);
        return getDataTable(list);
    }

    /**
     * 搜索测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/search")
    public TableDataInfo searchRecords(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Long userId,
            @RequestParam(required = false) Long scaleId,
            @RequestParam(required = false) Integer status) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.searchRecords(keyword, userId, scaleId, status);
        return getDataTable(list);
    }

    /**
     * 更新测评记录状态
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "测评记录", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/status")
    public AjaxResult updateStatus(@PathVariable("id") Long id, @RequestParam Integer status) {
        return toAjax(recordService.updateRecordStatus(id, status));
    }

    /**
     * 批量更新测评记录状态
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "测评记录", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-status")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params) {
        Long[] ids = (Long[]) params.get("ids");
        Integer status = (Integer) params.get("status");
        return toAjax(recordService.batchUpdateRecordStatus(ids, status));
    }

    /**
     * 查询用户测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/user/{userId}/stats")
    public AjaxResult getUserStats(@PathVariable("userId") Long userId) {
        Map<String, Object> stats = recordService.selectUserRecordStats(userId);
        return success(stats);
    }

    /**
     * 查询量表测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/scale/{scaleId}/stats")
    public AjaxResult getScaleStats(@PathVariable("scaleId") Long scaleId) {
        Map<String, Object> stats = recordService.selectScaleRecordStats(scaleId);
        return success(stats);
    }

    /**
     * 查询测评趋势统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/trend-stats")
    public AjaxResult getTrendStats(@RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> stats = recordService.selectRecordTrendStats(days);
        return success(stats);
    }

    /**
     * 查询测评完成率统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/completion-rate")
    public AjaxResult getCompletionRateStats(
            @RequestParam(required = false) Long scaleId,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Map<String, Object> stats = recordService.selectCompletionRateStats(scaleId, startDate, endDate);
        return success(stats);
    }

    /**
     * 查询测评时长统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/duration-stats")
    public AjaxResult getDurationStats(@RequestParam(required = false) Long scaleId) {
        Map<String, Object> stats = recordService.selectDurationStats(scaleId);
        return success(stats);
    }

    /**
     * 查询测评分数分布
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/score-distribution")
    public AjaxResult getScoreDistribution(@RequestParam(required = false) Long scaleId) {
        List<Map<String, Object>> distribution = recordService.selectScoreDistribution(scaleId);
        return success(distribution);
    }

    /**
     * 查询测评记录排行榜
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/ranking")
    public AjaxResult getRanking(
            @RequestParam(required = false) Long scaleId,
            @RequestParam(defaultValue = "10") Integer limit) {
        List<Map<String, Object>> ranking = recordService.selectRecordRanking(scaleId, limit);
        return success(ranking);
    }

    /**
     * 查询用户测评进度
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/user/{userId}/scale/{scaleId}/progress")
    public AjaxResult getUserProgress(
            @PathVariable("userId") Long userId,
            @PathVariable("scaleId") Long scaleId) {
        Map<String, Object> progress = recordService.selectUserTestProgress(userId, scaleId);
        return success(progress);
    }

    /**
     * 清理过期的测评记录
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:remove')")
    @Log(title = "测评记录", businessType = BusinessType.CLEAN)
    @PostMapping("/clean-expired")
    public AjaxResult cleanExpiredRecords(@RequestParam(defaultValue = "30") Integer expireDays) {
        int count = recordService.cleanExpiredRecords(expireDays);
        return success("清理了 " + count + " 条过期记录");
    }
}
