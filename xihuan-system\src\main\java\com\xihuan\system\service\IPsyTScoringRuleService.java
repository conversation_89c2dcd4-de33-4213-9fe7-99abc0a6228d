package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTScoringRule;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 计分规则Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTScoringRuleService {
    
    /**
     * 查询计分规则列表
     * 
     * @param rule 计分规则信息
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectRuleList(PsyTScoringRule rule);

    /**
     * 根据ID查询计分规则
     * 
     * @param id 计分规则ID
     * @return 计分规则信息
     */
    PsyTScoringRule selectRuleById(Long id);

    /**
     * 根据量表ID查询计分规则列表
     * 
     * @param scaleId 量表ID
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectRulesByScaleId(Long scaleId);

    /**
     * 根据分量表ID查询计分规则列表
     * 
     * @param subscaleId 分量表ID
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectRulesBySubscaleId(Long subscaleId);

    /**
     * 查询量表总分计分规则
     * 
     * @param scaleId 量表ID
     * @return 计分规则集合
     */
    List<PsyTScoringRule> selectTotalScoreRules(Long scaleId);

    /**
     * 新增计分规则
     * 
     * @param rule 计分规则信息
     * @return 结果
     */
    int insertRule(PsyTScoringRule rule);

    /**
     * 批量新增计分规则
     * 
     * @param rules 计分规则列表
     * @return 结果
     */
    int batchInsertRules(List<PsyTScoringRule> rules);

    /**
     * 修改计分规则
     * 
     * @param rule 计分规则信息
     * @return 结果
     */
    int updateRule(PsyTScoringRule rule);

    /**
     * 删除计分规则
     * 
     * @param ids 需要删除的计分规则ID
     * @return 结果
     */
    int deleteRuleByIds(Long[] ids);

    /**
     * 根据量表ID删除计分规则
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteRulesByScaleId(Long scaleId);

    /**
     * 根据分量表ID删除计分规则
     * 
     * @param subscaleId 分量表ID
     * @return 结果
     */
    int deleteRulesBySubscaleId(Long subscaleId);

    /**
     * 根据分数查找匹配的计分规则
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID（可为空）
     * @param score 分数
     * @return 计分规则
     */
    PsyTScoringRule findMatchingRule(Long scaleId, Long subscaleId, BigDecimal score);

    /**
     * 查找分数对应的所有匹配规则
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID（可为空）
     * @param score 分数
     * @return 计分规则集合
     */
    List<PsyTScoringRule> findAllMatchingRules(Long scaleId, Long subscaleId, BigDecimal score);

    /**
     * 检查规则范围是否重叠
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @param minValue 最小值
     * @param maxValue 最大值
     * @param excludeId 排除的ID
     * @return 是否重叠
     */
    boolean checkRuleRangeOverlap(Long scaleId, Long subscaleId, BigDecimal minValue, BigDecimal maxValue, Long excludeId);

    /**
     * 统计量表计分规则数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countRulesByScaleId(Long scaleId);

    /**
     * 统计分量表计分规则数量
     * 
     * @param subscaleId 分量表ID
     * @return 数量
     */
    int countRulesBySubscaleId(Long subscaleId);

    /**
     * 查询规则类型统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    List<Map<String, Object>> selectRuleTypeStats(Long scaleId);

    /**
     * 查询规则使用统计
     * 
     * @param ruleId 规则ID
     * @return 统计信息
     */
    Map<String, Object> selectRuleUsageStats(Long ruleId);

    /**
     * 复制计分规则到新量表
     * 
     * @param sourceScaleId 源量表ID
     * @param targetScaleId 目标量表ID
     * @return 结果
     */
    int copyRulesToScale(Long sourceScaleId, Long targetScaleId);

    /**
     * 复制计分规则到新分量表
     * 
     * @param sourceSubscaleId 源分量表ID
     * @param targetSubscaleId 目标分量表ID
     * @return 结果
     */
    int copyRulesToSubscale(Long sourceSubscaleId, Long targetSubscaleId);

    /**
     * 查询规则覆盖范围分析
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 覆盖范围信息
     */
    Map<String, Object> selectRuleCoverageAnalysis(Long scaleId, Long subscaleId);

    /**
     * 查询规则分数分布
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 分布信息
     */
    List<Map<String, Object>> selectRuleScoreDistribution(Long scaleId, Long subscaleId);

    /**
     * 搜索计分规则
     * 
     * @param keyword 关键词
     * @param scaleId 量表ID
     * @param ruleType 规则类型
     * @return 计分规则集合
     */
    List<PsyTScoringRule> searchRules(String keyword, Long scaleId, String ruleType);

    /**
     * 验证规则完整性
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @return 验证结果
     */
    Map<String, Object> validateRuleCompleteness(Long scaleId, Long subscaleId);

    /**
     * 解释测评结果
     * 
     * @param scaleId 量表ID
     * @param totalScore 总分
     * @param subscaleScores 分量表得分
     * @return 结果解释
     */
    Map<String, Object> interpretAssessmentResult(Long scaleId, BigDecimal totalScore, Map<Long, BigDecimal> subscaleScores);

    /**
     * 生成结果报告
     * 
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    Map<String, Object> generateResultReport(Long recordId);

    /**
     * 自动生成计分规则
     * 
     * @param scaleId 量表ID
     * @param strategy 生成策略
     * @return 结果
     */
    int autoGenerateRules(Long scaleId, String strategy);

    /**
     * 优化计分规则
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int optimizeRules(Long scaleId);

    /**
     * 导出计分规则
     * 
     * @param scaleId 量表ID
     * @return 规则数据
     */
    Map<String, Object> exportRules(Long scaleId);

    /**
     * 导入计分规则
     * 
     * @param scaleId 量表ID
     * @param ruleData 规则数据
     * @return 结果
     */
    int importRules(Long scaleId, Map<String, Object> ruleData);

    /**
     * 计算常模分数
     * 
     * @param scaleId 量表ID
     * @param rawScore 原始分数
     * @return 常模分数
     */
    BigDecimal calculateNormScore(Long scaleId, BigDecimal rawScore);

    /**
     * 计算百分位数
     * 
     * @param scaleId 量表ID
     * @param score 分数
     * @return 百分位数
     */
    BigDecimal calculatePercentile(Long scaleId, BigDecimal score);

    /**
     * 获取分数等级
     * 
     * @param scaleId 量表ID
     * @param subscaleId 分量表ID
     * @param score 分数
     * @return 等级信息
     */
    Map<String, Object> getScoreLevel(Long scaleId, Long subscaleId, BigDecimal score);
}
