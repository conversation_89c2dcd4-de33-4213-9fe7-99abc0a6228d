package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyMeditation;
import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyMeditationOrderMapper;
import com.xihuan.system.service.IPsyMeditationOrderService;
import com.xihuan.system.service.IPsyMeditationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 冥想订单表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyMeditationOrderServiceImpl implements IPsyMeditationOrderService {
    
    @Autowired
    private PsyMeditationOrderMapper orderMapper;
    
    @Autowired
    private IPsyMeditationService meditationService;

    @Override
    public List<PsyMeditationOrder> selectOrderList(PsyMeditationOrder order) {
        return orderMapper.selectOrderList(order);
    }

    @Override
    public PsyMeditationOrder selectOrderById(Long id) {
        return orderMapper.selectOrderById(id);
    }

    @Override
    public PsyMeditationOrder selectOrderByOrderNo(String orderNo) {
        return orderMapper.selectOrderByOrderNo(orderNo);
    }

    @Override
    public PsyMeditationOrder selectOrderWithDetails(Long id) {
        return orderMapper.selectOrderWithDetails(id);
    }

    @Override
    @Transactional
    public int insertOrder(PsyMeditationOrder order) {
        // 生成订单号
        if (order.getOrderNo() == null || order.getOrderNo().isEmpty()) {
            order.setOrderNo(generateOrderNo());
        }
        
        // 设置默认值
        order.setCreateTime(DateUtils.getNowDate());
        order.setDelFlag(0);
        order.setStatus(0); // 待支付
        
        // 获取冥想信息设置订单价格
        if (order.getMeditationId() != null) {
            PsyMeditation meditation = meditationService.selectMeditationById(order.getMeditationId());
            if (meditation != null) {
                order.setOriginalPrice(meditation.getPrice());
                // 如果没有设置支付金额，默认为冥想价格
                if (order.getPaymentAmount() == null) {
                    order.setPaymentAmount(meditation.getPrice());
                }
            }
        }
        
        return orderMapper.insertOrder(order);
    }

    @Override
    public int updateOrder(PsyMeditationOrder order) {
        order.setUpdateTime(DateUtils.getNowDate());
        return orderMapper.updateOrder(order);
    }

    @Override
    public int deleteOrderById(Long id) {
        return orderMapper.deleteOrderById(id);
    }

    @Override
    public int deleteOrderByIds(Long[] ids) {
        return orderMapper.deleteOrderByIds(ids);
    }

    @Override
    public int updateOrderPaymentStatus(String orderNo, Integer status, String paymentMethod, String transactionId, Date paymentTime) {
        return orderMapper.updateOrderPaymentStatus(orderNo, status, paymentMethod, transactionId, paymentTime);
    }

    @Override
    public int updateOrderRefund(String orderNo, BigDecimal refundAmount, Date refundTime) {
        return orderMapper.updateOrderRefund(orderNo, refundAmount, refundTime);
    }

    @Override
    public boolean checkUserPurchased(Long userId, Long meditationId) {
        int count = orderMapper.checkUserPurchased(userId, meditationId);
        return count > 0;
    }

    @Override
    public List<PsyMeditation> selectUserPurchasedMeditations(Long userId) {
        // 查询用户已支付的订单
        PsyMeditationOrder queryOrder = new PsyMeditationOrder();
        queryOrder.setUserId(userId);
        queryOrder.setStatus(1); // 已支付
        List<PsyMeditationOrder> orders = orderMapper.selectOrderList(queryOrder);
        
        // 获取冥想信息
        List<PsyMeditation> meditations = new java.util.ArrayList<>();
        for (PsyMeditationOrder order : orders) {
            PsyMeditation meditation = meditationService.selectMeditationById(order.getMeditationId());
            if (meditation != null) {
                meditations.add(meditation);
            }
        }
        
        return meditations;
    }

    @Override
    public String generateOrderNo() {
        return orderMapper.generateOrderNo();
    }
}
