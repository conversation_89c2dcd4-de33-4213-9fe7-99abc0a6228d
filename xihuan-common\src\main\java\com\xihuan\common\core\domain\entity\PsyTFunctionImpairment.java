package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 功能损害评估条目表 psy_t_function_impairment
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTFunctionImpairment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @Excel(name = "记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 关联psy_t_scale.id */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 损害条目内容 */
    @Excel(name = "损害条目内容")
    @NotBlank(message = "损害条目内容不能为空")
    private String itemContent;

    /** 阳性得分值 */
    @Excel(name = "阳性得分值")
    @NotNull(message = "阳性得分值不能为空")
    @Min(value = 1, message = "阳性得分值不能小于1")
    private Integer positiveScore;

    /** 损害程度分级 */
    @Excel(name = "损害程度分级", readConverterExp = "LOW=轻度,MEDIUM=中度,HIGH=重度")
    private String impairmentLevel;

    // 关联对象
    /** 量表信息 */
    private PsyTScale scale;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 量表编码 */
    private String scaleCode;

    /** 条目序号 */
    private Integer itemNo;

    /** 用户评分 */
    private Integer userScore;

    /** 是否阳性 */
    private Boolean isPositive;

    /** 损害描述 */
    private String impairmentDesc;

    // 常量定义
    /** 损害程度：轻度 */
    public static final String IMPAIRMENT_LEVEL_LOW = "LOW";
    
    /** 损害程度：中度 */
    public static final String IMPAIRMENT_LEVEL_MEDIUM = "MEDIUM";
    
    /** 损害程度：重度 */
    public static final String IMPAIRMENT_LEVEL_HIGH = "HIGH";

    /** 默认阳性得分值 */
    public static final Integer DEFAULT_POSITIVE_SCORE = 1;

    /**
     * 获取损害程度描述
     */
    public String getImpairmentLevelDesc() {
        if (impairmentLevel == null) return "";
        switch (impairmentLevel) {
            case "LOW": return "轻度";
            case "MEDIUM": return "中度";
            case "HIGH": return "重度";
            default: return "未知";
        }
    }

    /**
     * 判断是否阳性
     */
    public boolean isPositiveResult(Integer score) {
        if (score == null || positiveScore == null) return false;
        return score >= positiveScore;
    }

    /**
     * 获取损害等级数值
     */
    public Integer getImpairmentLevelValue() {
        if (impairmentLevel == null) return 0;
        switch (impairmentLevel) {
            case "LOW": return 1;
            case "MEDIUM": return 2;
            case "HIGH": return 3;
            default: return 0;
        }
    }

    /**
     * 是否轻度损害
     */
    public boolean isLowImpairment() {
        return IMPAIRMENT_LEVEL_LOW.equals(impairmentLevel);
    }

    /**
     * 是否中度损害
     */
    public boolean isMediumImpairment() {
        return IMPAIRMENT_LEVEL_MEDIUM.equals(impairmentLevel);
    }

    /**
     * 是否重度损害
     */
    public boolean isHighImpairment() {
        return IMPAIRMENT_LEVEL_HIGH.equals(impairmentLevel);
    }

    /**
     * 获取阳性标准描述
     */
    public String getPositiveStandardDesc() {
        if (positiveScore == null) return "";
        return "≥" + positiveScore + "分为阳性";
    }
}
