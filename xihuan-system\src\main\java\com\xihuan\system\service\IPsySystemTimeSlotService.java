package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 系统公共时间槽Service接口
 * 
 * <AUTHOR>
 */
public interface IPsySystemTimeSlotService {
    
    /**
     * 查询系统时间槽列表
     * 
     * @param systemTimeSlot 系统时间槽
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectSystemTimeSlotList(PsySystemTimeSlot systemTimeSlot);
    
    /**
     * 根据ID查询系统时间槽
     * 
     * @param id 系统时间槽主键
     * @return 系统时间槽
     */
    PsySystemTimeSlot selectSystemTimeSlotById(Long id);
    
    /**
     * 查询指定日期范围的系统时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectSlotsByDateRange(LocalDate startDate, LocalDate endDate, Long centerId);
    
    /**
     * 查询指定日期的系统时间槽
     * 
     * @param date 日期
     * @param centerId 咨询中心ID
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectSlotsByDate(LocalDate date, Long centerId);
    
    /**
     * 查询有可用咨询师的时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 系统时间槽集合
     */
    List<PsySystemTimeSlot> selectAvailableSlots(LocalDate startDate, LocalDate endDate, Long centerId);
    
    /**
     * 获取格式化的系统时间槽数据（按日期和时间段分组）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 格式化的时间槽数据
     */
    Map<String, Object> getFormattedSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId);
    
    /**
     * 新增系统时间槽
     * 
     * @param systemTimeSlot 系统时间槽
     * @return 结果
     */
    int insertSystemTimeSlot(PsySystemTimeSlot systemTimeSlot);
    
    /**
     * 批量新增系统时间槽
     * 
     * @param systemTimeSlots 系统时间槽列表
     * @return 结果
     */
    int batchInsertSystemTimeSlots(List<PsySystemTimeSlot> systemTimeSlots);
    
    /**
     * 修改系统时间槽
     * 
     * @param systemTimeSlot 系统时间槽
     * @return 结果
     */
    int updateSystemTimeSlot(PsySystemTimeSlot systemTimeSlot);
    
    /**
     * 删除系统时间槽信息
     * 
     * @param id 系统时间槽主键
     * @return 结果
     */
    int deleteSystemTimeSlotById(Long id);
    
    /**
     * 批量删除系统时间槽
     * 
     * @param ids 需要删除的系统时间槽主键集合
     * @return 结果
     */
    int deleteSystemTimeSlotByIds(Long[] ids);
    
    /**
     * 生成指定日期范围的系统时间槽
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 生成的时间槽数量
     */
    int generateSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId);
    
    /**
     * 更新系统时间槽的可用性统计
     * 
     * @param date 日期
     * @param centerId 咨询中心ID
     * @return 更新的时间槽数量
     */
    int updateAvailabilityStats(LocalDate date, Long centerId);
    
    /**
     * 清理过期的系统时间槽
     * 
     * @param beforeDate 清理此日期之前的时间槽
     * @param centerId 咨询中心ID
     * @return 清理的时间槽数量
     */
    int cleanExpiredSystemSlots(LocalDate beforeDate, Long centerId);
    
    /**
     * 重新生成系统时间槽（先清理再生成）
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param centerId 咨询中心ID
     * @return 生成的时间槽数量
     */
    int regenerateSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId);

    /**
     * 批量更新系统时间槽状态
     *
     * @param slotIds 时间槽ID列表
     * @param status 新状态（0-可用，2-已过期）
     * @return 更新的记录数
     */
    int batchUpdateSlotStatus(List<Long> slotIds, Integer status);

    /**
     * 更新过期的系统时间槽状态
     *
     * @param centerId 咨询中心ID
     * @param delayHours 延后小时数（0表示不延后）
     * @return 更新的记录数
     */
    int updateExpiredSlotStatus(Long centerId, Integer delayHours);

    /**
     * 更新过期的系统时间槽状态（支持延后配置）
     *
     * @param centerId 咨询中心ID
     * @return 更新的记录数
     */
    int updateExpiredSlotStatusWithDelay(Long centerId);
}
