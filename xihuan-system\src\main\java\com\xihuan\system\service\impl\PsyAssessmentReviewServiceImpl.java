package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyAssessmentReview;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.SecurityUtils;
import com.xihuan.common.utils.bean.BeanUtils;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;
import com.xihuan.system.mapper.PsyAssessmentReviewMapper;
import com.xihuan.system.service.IPsyAssessmentReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 心理测评评价Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyAssessmentReviewServiceImpl implements IPsyAssessmentReviewService {
    
    @Autowired
    private PsyAssessmentReviewMapper reviewMapper;

    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    @Override
    public List<PsyAssessmentReview> selectReviewList(PsyAssessmentReview review) {
        return reviewMapper.selectReviewList(review);
    }

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    @Override
    public PsyAssessmentReview selectReviewById(Long id) {
        return reviewMapper.selectReviewById(id);
    }

    /**
     * 查询评价详情（包含用户、量表等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    @Override
    public PsyAssessmentReview selectReviewWithDetails(Long id) {
        return reviewMapper.selectReviewWithDetails(id);
    }

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    @Override
    public List<PsyAssessmentReview> selectReviewsByUserId(Long userId) {
        return reviewMapper.selectReviewsByUserId(userId);
    }

    /**
     * 根据量表ID查询评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    @Override
    public List<PsyAssessmentReview> selectReviewsByScaleId(Long scaleId) {
        return reviewMapper.selectReviewsByScaleId(scaleId);
    }

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertReview(PsyAssessmentReview review) {
        // 设置默认值
        if (review.getIsAnonymous() == null) {
            review.setIsAnonymous(PsyAssessmentReview.ANONYMOUS_NO);
        }
        if (review.getStatus() == null) {
            review.setStatus(PsyAssessmentReview.STATUS_PENDING);
        }
        if (review.getLikeCount() == null) {
            review.setLikeCount(0);
        }
        if (review.getReplyCount() == null) {
            review.setReplyCount(0);
        }
        if (review.getDelFlag() == null) {
            review.setDelFlag(PsyAssessmentReview.DEL_FLAG_NORMAL);
        }
        
        review.setCreateTime(DateUtils.getNowDate());
        return reviewMapper.insertReview(review);
    }

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateReview(PsyAssessmentReview review) {
        review.setUpdateTime(DateUtils.getNowDate());
        return reviewMapper.updateReview(review);
    }

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteReviewById(Long id) {
        return reviewMapper.deleteReviewById(id);
    }

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteReviewByIds(Long[] ids) {
        return reviewMapper.deleteReviewByIds(ids);
    }

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param status 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    @Override
    @Transactional
    public int auditReview(Long id, Integer status, String auditRemark) {
        String username = SecurityUtils.getUsername();
        return reviewMapper.auditReview(id, status, username, auditRemark);
    }

    /**
     * 点赞评价
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int likeReview(Long id, Long userId) {
        // 检查是否已点赞
        if (isLiked(id, userId)) {
            return 0;
        }
        
        // TODO: 保存点赞记录
        
        // 更新点赞数
        return reviewMapper.updateLikeCount(id, 1);
    }

    /**
     * 取消点赞
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    @Override
    @Transactional
    public int unlikeReview(Long id, Long userId) {
        // 检查是否已点赞
        if (!isLiked(id, userId)) {
            return 0;
        }
        
        // TODO: 删除点赞记录
        
        // 更新点赞数
        return reviewMapper.updateLikeCount(id, -1);
    }

    /**
     * 查询用户是否已点赞
     * 
     * @param reviewId 评价ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    @Override
    public boolean isLiked(Long reviewId, Long userId) {
        return reviewMapper.checkUserLiked(reviewId, userId) > 0;
    }

    /**
     * 查询量表的评价统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectReviewStats(Long scaleId) {
        return reviewMapper.selectReviewStats(scaleId);
    }

    /**
     * 查询用户的评价统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserReviewStats(Long userId) {
        return reviewMapper.selectUserReviewStats(userId);
    }

    /**
     * 获取评价DTO
     * 
     * @param id 评价ID
     * @param userId 当前用户ID
     * @return 评价DTO
     */
    @Override
    public PsyAssessmentDTO.ReviewDTO getReviewDTO(Long id, Long userId) {
        PsyAssessmentReview review = selectReviewWithDetails(id);
        if (review == null) {
            return null;
        }
        
        PsyAssessmentDTO.ReviewDTO dto = new PsyAssessmentDTO.ReviewDTO();
        BeanUtils.copyBeanProp(dto, review);
        
        // 设置扩展字段
        dto.setStatusDesc(review.getStatusDesc());
        dto.setRatingStars(review.getRatingStars());
        dto.setDisplayName(review.getDisplayName());
        dto.setCreateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, review.getCreateTime()));
        
        // 设置是否已点赞
        if (userId != null) {
            dto.setLiked(isLiked(id, userId));
        }
        
        return dto;
    }

    /**
     * 获取评价列表DTO
     * 
     * @param review 查询条件
     * @param userId 当前用户ID
     * @return 评价DTO列表
     */
    @Override
    public List<PsyAssessmentDTO.ReviewDTO> getReviewListDTO(PsyAssessmentReview review, Long userId) {
        List<PsyAssessmentReview> reviewList = selectReviewList(review);
        List<PsyAssessmentDTO.ReviewDTO> dtoList = new ArrayList<>();
        
        for (PsyAssessmentReview item : reviewList) {
            PsyAssessmentDTO.ReviewDTO dto = getReviewDTO(item.getId(), userId);
            if (dto != null) {
                dtoList.add(dto);
            }
        }
        
        return dtoList;
    }
}
