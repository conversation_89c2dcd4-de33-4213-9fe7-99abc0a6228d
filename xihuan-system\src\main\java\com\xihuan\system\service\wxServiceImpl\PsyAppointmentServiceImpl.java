package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.entity.PsyAppointment;
import com.xihuan.system.domain.dto.PsyAppointmentTimeDTO;
import com.xihuan.system.mapper.PsyAppointmentMapper;
import com.xihuan.system.mapper.PsyConsultantMapper;
import com.xihuan.system.service.wxService.PsyAppointmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Service("PsyAppointmentService")
public class PsyAppointmentServiceImpl implements PsyAppointmentService {

    @Autowired
    private PsyAppointmentMapper psyAppointmentMapper;

    @Autowired
    private PsyConsultantMapper consultantMapper;

    /**
     * 核心时段生成方法（定时任务入口）
     */
    public void genSlots() {
        try {
            // 1. 清理所有过期数据（7天前）
            LocalDate cleanupDate = LocalDate.now().minusDays(7);
            psyAppointmentMapper.deleteBeforeDate(java.sql.Date.valueOf(cleanupDate));

            // 2. 清理所有未来的公共时段（防止重复）
            psyAppointmentMapper.deletePublicSlotsAfterDate(java.sql.Date.valueOf(LocalDate.now()));

            // 3. 生成新的公共时段
            generatePublicSlots();

            // 4. 获取所有有效咨询师
            List<PsyConsultant> consultants = consultantMapper.selectAllConsultantsWithDetails();
            
            // 5. 生成咨询师专属时段
            consultants.forEach(counselor -> {
                // 先清理这个咨询师未来的时段（防止重复）
                psyAppointmentMapper.deleteCounselorSlotsAfterDate(
                    counselor.getId(), 
                    java.sql.Date.valueOf(LocalDate.now())
                );
                // 重新生成时段
                generateCounselorSlots(counselor.getId());
            });
        } catch (Exception e) {
            // 记录错误日志
            e.printStackTrace();
            throw new RuntimeException("生成预约时段失败: " + e.getMessage());
        }
    }

    /**
     * 生成公共预约时段
     */
    private void generatePublicSlots() {
        LocalDate today = LocalDate.now();
        // 生成今天+未来6天的公共时段
        IntStream.range(0, 7).forEach(offset -> {
            LocalDate date = today.plusDays(offset);
            // 检查当天是否已经生成过时段
            if (!psyAppointmentMapper.existsDaySlots(null, date.toString())) {
                insertDailyPublicSlots(date);
            }
        });
    }

    /**
     * 插入单日所有公共时段
     */
    private void insertDailyPublicSlots(LocalDate date) {
        // 上午时段（3个）
        insertPublicSlot(date, "09:00-10:00");
        insertPublicSlot(date, "10:00-11:00");
        insertPublicSlot(date, "11:00-12:00");

        // 下午时段（6个）
        insertPublicSlot(date, "12:00-13:00");
        insertPublicSlot(date, "13:00-14:00");
        insertPublicSlot(date, "14:00-15:00");
        insertPublicSlot(date, "15:00-16:00");
        insertPublicSlot(date, "16:00-17:00");
        insertPublicSlot(date, "17:00-18:00");

        // 晚上时段（2个）
        insertPublicSlot(date, "20:00-21:00");
        insertPublicSlot(date, "21:00-22:00");
    }

    /**
     * 插入单个公共时段（带防重校验）
     */
    private void insertPublicSlot(LocalDate date, String timeSlot) {
        // 检查是否已存在相同的公共时段
        if (!psyAppointmentMapper.existsSlot(null, date.toString(), timeSlot)) {
            PsyAppointment slot = new PsyAppointment();
            slot.setCounselorId(null);  // 公共时段的counselorId为null
            slot.setAppointmentDate(java.sql.Date.valueOf(date));
            slot.setTimeSlot(timeSlot);
            slot.setWeekDay(getChineseWeekday(date));
            slot.setStatus("0"); // 默认可预约
            slot.setCreateTime(new Date());
            slot.setDelFlag(0);
            psyAppointmentMapper.insert(slot);
        }
    }

    /**
     * 为单个咨询师生成7天时段
     */
    private void generateCounselorSlots(Long counselorId) {
        LocalDate today = LocalDate.now();
        // 生成今天+未来6天数据
        IntStream.range(0, 7).forEach(offset -> {
            LocalDate date = today.plusDays(offset);
            // 检查当天是否已经生成过时段
            if (!psyAppointmentMapper.existsDaySlots(counselorId, date.toString())) {
                insertDailySlots(counselorId, date);
            }
        });
    }

    /**
     * 插入单日所有时段
     */
    private void insertDailySlots(Long counselorId, LocalDate date) {
        // 上午时段（3个）
        insertSlot(counselorId, date, "09:00-10:00");
        insertSlot(counselorId, date, "10:00-11:00");
        insertSlot(counselorId, date, "11:00-12:00");

        // 下午时段（6个）
        insertSlot(counselorId, date, "12:00-13:00");
        insertSlot(counselorId, date, "13:00-14:00");
        insertSlot(counselorId, date, "14:00-15:00");
        insertSlot(counselorId, date, "15:00-16:00");
        insertSlot(counselorId, date, "16:00-17:00");
        insertSlot(counselorId, date, "17:00-18:00");

        // 晚上时段（2个）
        insertSlot(counselorId, date, "20:00-21:00");
        insertSlot(counselorId, date, "21:00-22:00");

        // 设置当天第一个时段为已满（演示用）
        if (date.isEqual(LocalDate.now())) {
            psyAppointmentMapper.updateSlotStatus(
                    counselorId,
                    date.toString(),
                    "09:00-10:00",
                    "1" // 已满状态
            );
        }
    }

    /**
     * 插入单个时段（带防重校验）
     */
    private void insertSlot(Long counselorId, LocalDate date, String timeSlot) {
        // 检查是否已存在相同时段
        if (!psyAppointmentMapper.existsSlot(counselorId, date.toString(), timeSlot)) {
            PsyAppointment slot = new PsyAppointment();
            slot.setCounselorId(counselorId);
            slot.setAppointmentDate(java.sql.Date.valueOf(date));
            slot.setTimeSlot(timeSlot);
            slot.setWeekDay(getChineseWeekday(date));
            slot.setStatus("0"); // 默认可预约
            slot.setCreateTime(new Date());
            slot.setDelFlag(0);
            psyAppointmentMapper.insert(slot);
        }
    }

    public static String getChineseWeekday(LocalDate date) {
        if (date.isEqual(LocalDate.now())) {
            return "今天";
        }
        switch (date.getDayOfWeek()) {
            case MONDAY:
                return "周一";
            case TUESDAY:
                return "周二";
            case WEDNESDAY:
                return "周三";
            case THURSDAY:
                return "周四";
            case FRIDAY:
                return "周五";
            case SATURDAY:
                return "周六";
            case SUNDAY:
                return "周日";
            default:
                throw new IllegalArgumentException("无效日期");
        }
    }


    @Override
    public List<PsyAppointmentTimeDTO> getAvailableTimes(Long counselorId, int days) {
        // 合并重复方法
        LocalDate startDate = LocalDate.now();
        LocalDate endDate = startDate.plusDays(days > 0 ? days : 6);

        List<PsyAppointment> rawSlots = psyAppointmentMapper.selectByCounselorAndDate(
                counselorId,
                java.sql.Date.valueOf(startDate),
                java.sql.Date.valueOf(endDate)
        );

        return rawSlots.stream()
                .collect(Collectors.groupingBy(slot ->
                        new java.sql.Date(slot.getAppointmentDate().getTime()).toLocalDate()
                ))
                .entrySet().stream()
                .map(entry -> buildDateDTO(entry.getKey(), entry.getValue()))
                .sorted(Comparator.comparing(dto ->
                        LocalDate.parse(dto.getDate(), DateTimeFormatter.ofPattern("yyyy年M月d日"))
                ))
                .collect(Collectors.toList());
    }

    @Override
    public List<PsyAppointment> selectPublicSlots(Date startDate, Date endDate) {
        return psyAppointmentMapper.selectPublicSlots(startDate, endDate);
    }

    @Override
    public List<PsyAppointmentTimeDTO> getPublicTimesFormatted(Date startDate, Date endDate) {
        // 1. 获取原始数据
        List<PsyAppointment> rawSlots = psyAppointmentMapper.selectPublicSlots(startDate, endDate);

        // 2. 按日期分组并转换格式
        return rawSlots.stream()
                .collect(Collectors.groupingBy(slot ->
                        new java.sql.Date(slot.getAppointmentDate().getTime()).toLocalDate()
                ))
                .entrySet().stream()
                .map(entry -> buildDateDTO(entry.getKey(), entry.getValue()))
                .sorted(Comparator.comparing(dto ->
                        LocalDate.parse(dto.getDate(), DateTimeFormatter.ofPattern("yyyy年M月d日"))
                ))
                .collect(Collectors.toList());
    }

    private PsyAppointmentTimeDTO buildDateDTO(LocalDate date, List<PsyAppointment> slots) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy年M月d日");
        PsyAppointmentTimeDTO dto = new PsyAppointmentTimeDTO();
        dto.setDate(date.format(formatter));
        dto.setWeekDay(getChineseWeekday(date));
        dto.setShortDate(date.format(DateTimeFormatter.ofPattern("M月d日")));
        
        // 使用TreeMap保持时间段顺序
        Map<String, List<PsyAppointment>> rangeGroups = slots.stream()
                .collect(Collectors.groupingBy(
                        this::getTimeRange,
                        () -> new TreeMap<>(Comparator.comparingInt(r -> {
                            switch (r) {
                                case "上午":
                                    return 1;
                                case "下午":
                                    return 2;
                                default:
                                    return 3;
                            }
                        })),
                        Collectors.toList()
                ));

        dto.setTimeRanges(rangeGroups.entrySet().stream()
                .map(entry -> new PsyAppointmentTimeDTO.TimeRange(
                        entry.getKey(),
                        buildTimeSlots(date, entry.getValue())
                ))
                .collect(Collectors.toList()));

        // 设置是否是今天
        dto.setIsToday(date.equals(LocalDate.now()));

        return dto;
    }

    private String getTimeRange(PsyAppointment slot) {
        String time = slot.getTimeSlot().split("-")[0];
        int hour = Integer.parseInt(time.split(":")[0]);

        if (hour < 12) return "上午";
        if (hour < 18) return "下午";
        return "晚上";
    }

    private List<PsyAppointmentTimeDTO.TimeSlot> buildTimeSlots(LocalDate date, List<PsyAppointment> slots) {
        return slots.stream()
                .map(slot -> {
                    LocalDate slotDate = toLocalDate(slot.getAppointmentDate());
                    boolean isExpired = isTimeExpired(slotDate, slot.getTimeSlot());
                    
                    PsyAppointmentTimeDTO.TimeSlot timeSlot = new PsyAppointmentTimeDTO.TimeSlot();
                    timeSlot.setTime(slot.getTimeSlot());
                    timeSlot.setStatus(slot.getStatus());
                    timeSlot.setTimeStatus(isExpired ? "已过期" : "可预约");
                    timeSlot.setFullDate(date.toString());
                    
                    return timeSlot;
                })
                .sorted(Comparator.comparing(PsyAppointmentTimeDTO.TimeSlot::getTime))
                .collect(Collectors.toList());
    }

    private boolean isTimeExpired(LocalDate date, String timeSlot) {
        LocalDateTime slotStart = LocalDateTime.of(
                date,
                LocalTime.parse(timeSlot.split("-")[0])
        );
        return LocalDateTime.now().isAfter(slotStart);
    }


    public static LocalDate toLocalDate(Date date) {
        return date.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
    }

}
