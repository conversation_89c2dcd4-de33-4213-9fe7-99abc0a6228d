package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.consultant.PsyDictExpertise;

import java.util.List;

/**
 * 咨询领域Service接口
 */
public interface IPsyDictExpertiseService {
    /**
     * 查询咨询领域
     */
    public PsyDictExpertise selectPsyDictExpertiseById(Long id);

    /**
     * 查询咨询领域列表
     */
    public List<PsyDictExpertise> selectPsyDictExpertiseList(PsyDictExpertise psyDictExpertise);

    /**
     * 新增咨询领域
     */
    public int insertPsyDictExpertise(PsyDictExpertise psyDictExpertise);

    /**
     * 修改咨询领域
     */
    public int updatePsyDictExpertise(PsyDictExpertise psyDictExpertise);

    /**
     * 批量删除咨询领域
     */
    public int deletePsyDictExpertiseByIds(Long[] ids);

    /**
     * 删除咨询领域信息
     */
    public int deletePsyDictExpertiseById(Long id);

    /**
     * 获取树形结构的咨询领域列表
     */
    public List<PsyDictExpertise> buildExpertiseTree();

    /**
     * 只查询父节点列表
     */
    List<PsyDictExpertise> selectParentExpertiseList();

    /**
     * 只查询子节点列表
     */
    List<PsyDictExpertise> selectChildExpertiseList();
}