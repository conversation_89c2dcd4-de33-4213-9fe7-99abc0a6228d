-- 新心理测评系统数据库表结构
-- 表名前缀：psy_t_

-- 1. 量表基础信息表
DROP TABLE IF EXISTS `psy_t_scale`;
CREATE TABLE `psy_t_scale` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '量表唯一ID',
  `name` VARCHAR(100) NOT NULL COMMENT '量表名称',
  `code` VARCHAR(50) NOT NULL COMMENT '量表编码',
  `description` TEXT COMMENT '量表描述',
  `introduction` TEXT COMMENT '量表介绍',
  `question_count` INT(11) NOT NULL COMMENT '总题数',
  `scoring_type` ENUM('LIKERT','BINARY','COMPOSITE') NOT NULL COMMENT '计分类型',
  `duration` VARCHAR(20) COMMENT '预估时长',
  `norm_mean` DECIMAL(5,2) COMMENT '常模均值',
  `norm_sd` DECIMAL(5,2) COMMENT '常模标准差',
  `applicable_age` VARCHAR(50) COMMENT '适用年龄',
  `image_url` VARCHAR(500) COMMENT '量表封面图片',
  `price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '价格',
  `pay_mode` TINYINT(1) DEFAULT 0 COMMENT '付费模式(0免费 1付费)',
  `pay_phase` TINYINT(1) DEFAULT 0 COMMENT '付费阶段(0测试 1报告)',
  `free_vip_level` INT(11) DEFAULT 0 COMMENT '免费VIP等级',
  `free_report_level` INT(11) DEFAULT 1 COMMENT '免费报告层级',
  `paid_report_level` INT(11) DEFAULT 3 COMMENT '付费报告层级',
  `enterprise_id` BIGINT(20) COMMENT '企业ID',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态(0停用 1启用)',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `search_keywords` TEXT COMMENT '搜索关键词',
  `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数',
  `view_count` INT(11) DEFAULT 0 COMMENT '查看次数',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_code` (`code`),
  KEY `idx_status_sort` (`status`, `sort`),
  KEY `idx_enterprise` (`enterprise_id`),
  KEY `idx_search` (`search_count`, `view_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量表基础信息表';

-- 2. 题目表
DROP TABLE IF EXISTS `psy_t_question`;
CREATE TABLE `psy_t_question` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '题目唯一ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_t_scale.id',
  `question_no` INT(11) NOT NULL COMMENT '题号(从1开始)',
  `content` TEXT NOT NULL COMMENT '题目内容',
  `question_type` ENUM('SINGLE','MULTIPLE','TEXT','COMPOSITE') DEFAULT 'SINGLE' COMMENT '题目类型',
  `is_reverse` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否反向计分',
  `reverse_value` INT(11) COMMENT '反向计分值',
  `options` JSON COMMENT '选项配置',
  `subscale_ref` VARCHAR(50) COMMENT '分量表关联标识',
  `is_required` TINYINT(1) DEFAULT 1 COMMENT '是否必答',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale_sort` (`scale_id`, `sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量表题目表';

-- 3. 分量表定义表
DROP TABLE IF EXISTS `psy_t_subscale`;
CREATE TABLE `psy_t_subscale` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分量表ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_t_scale.id',
  `name` VARCHAR(50) NOT NULL COMMENT '分量表名称',
  `alias` VARCHAR(20) COMMENT '缩写',
  `min_score` INT(11) NOT NULL COMMENT '最小分数',
  `max_score` INT(11) NOT NULL COMMENT '最大分数',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale` (`scale_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量表分量表定义表';

-- 4. 分量表题目关系表
DROP TABLE IF EXISTS `psy_t_subscale_question_rel`;
CREATE TABLE `psy_t_subscale_question_rel` (
  `subscale_id` BIGINT(20) NOT NULL COMMENT '分量表ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '题目ID',
  `weight` DECIMAL(5,2) DEFAULT 1.00 COMMENT '权重',
  PRIMARY KEY (`subscale_id`, `question_id`),
  KEY `idx_question` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分量表题目关系表';

-- 5. 计分规则表
DROP TABLE IF EXISTS `psy_t_scoring_rule`;
CREATE TABLE `psy_t_scoring_rule` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_t_scale.id',
  `subscale_id` BIGINT(20) COMMENT '关联psy_t_subscale.id(为空时适用总分)',
  `rule_type` ENUM('RANGE','CUTOFF') NOT NULL COMMENT '规则类型',
  `min_value` DECIMAL(5,2) COMMENT '最小值',
  `max_value` DECIMAL(5,2) COMMENT '最大值',
  `cutoff_value` DECIMAL(5,2) COMMENT '临界值',
  `label` VARCHAR(50) NOT NULL COMMENT '结果标签',
  `description` TEXT NOT NULL COMMENT '解释说明',
  `suggestion` TEXT COMMENT '建议措施',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale` (`scale_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计分规则表';

-- 6. 复合题特殊计分表
DROP TABLE IF EXISTS `psy_t_composite_question`;
CREATE TABLE `psy_t_composite_question` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '复合题ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '关联psy_t_question.id',
  `subitem_no` VARCHAR(20) NOT NULL COMMENT '子项编号',
  `content` VARCHAR(200) NOT NULL COMMENT '子项内容',
  `score_weight` INT(11) NOT NULL DEFAULT 1 COMMENT '计分权重',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_item` (`question_id`,`subitem_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='复合题特殊计分表';

-- 7. 题目选项表
DROP TABLE IF EXISTS `psy_t_question_option`;
CREATE TABLE `psy_t_question_option` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '关联psy_t_question.id',
  `option_text` VARCHAR(200) NOT NULL COMMENT '选项文本',
  `option_value` INT(11) NOT NULL COMMENT '选项分值',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_question_sort` (`question_id`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目选项表';

-- 8. 功能损害评估表
DROP TABLE IF EXISTS `psy_t_function_impairment`;
CREATE TABLE `psy_t_function_impairment` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_t_scale.id',
  `item_content` TEXT NOT NULL COMMENT '损害条目内容',
  `positive_score` INT(11) NOT NULL DEFAULT 1 COMMENT '阳性得分值',
  `impairment_level` ENUM('LOW','MEDIUM','HIGH') COMMENT '损害程度分级',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale` (`scale_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='功能损害评估条目表';

-- 9. 测评记录表
DROP TABLE IF EXISTS `psy_t_assessment_record`;
CREATE TABLE `psy_t_assessment_record` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '量表ID',
  `user_id` BIGINT(20) COMMENT '用户ID',
  `session_id` VARCHAR(64) NOT NULL COMMENT '会话ID',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `completion_time` DATETIME COMMENT '完成时间',
  `total_score` DECIMAL(8,2) COMMENT '总分',
  `result_level` VARCHAR(50) COMMENT '结果等级',
  `result_description` TEXT COMMENT '结果描述',
  `suggestions` TEXT COMMENT '建议',
  `status` TINYINT(1) DEFAULT 0 COMMENT '状态(0进行中 1已完成 2已放弃)',
  `ip_address` VARCHAR(50) COMMENT 'IP地址',
  `user_agent` VARCHAR(500) COMMENT '用户代理',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_session` (`session_id`),
  KEY `idx_scale_user` (`scale_id`, `user_id`),
  KEY `idx_completion_time` (`completion_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测评记录表';

-- 10. 答题记录表
DROP TABLE IF EXISTS `psy_t_answer_record`;
CREATE TABLE `psy_t_answer_record` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `record_id` BIGINT(20) NOT NULL COMMENT '测评记录ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '题目ID',
  `answer_content` TEXT COMMENT '答案内容',
  `answer_score` INT(11) COMMENT '得分',
  `response_time` INT(11) COMMENT '答题耗时(秒)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record` (`record_id`),
  KEY `idx_question` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='答题记录表';

-- 11. 测评订单表
DROP TABLE IF EXISTS `psy_t_assessment_order`;
CREATE TABLE `psy_t_assessment_order` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单编号',
  `scale_id` BIGINT(20) NOT NULL COMMENT '量表ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `payment_amount` DECIMAL(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` VARCHAR(20) COMMENT '支付方式',
  `payment_time` DATETIME COMMENT '支付时间',
  `transaction_id` VARCHAR(100) COMMENT '交易流水号',
  `refund_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `refund_time` DATETIME COMMENT '退款时间',
  `cancel_time` DATETIME COMMENT '取消时间',
  `coupon_id` BIGINT(20) COMMENT '优惠券ID',
  `coupon_discount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠券折扣',
  `status` TINYINT(1) DEFAULT 0 COMMENT '订单状态(0待支付 1已支付 2已取消 3已退款)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_order_no` (`order_no`),
  KEY `idx_scale_user` (`scale_id`, `user_id`),
  KEY `idx_payment_time` (`payment_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测评订单表';

-- 12. 企业信息表
DROP TABLE IF EXISTS `psy_t_enterprise`;
CREATE TABLE `psy_t_enterprise` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '企业ID',
  `enterprise_code` VARCHAR(50) NOT NULL COMMENT '企业编码',
  `enterprise_name` VARCHAR(200) NOT NULL COMMENT '企业名称',
  `enterprise_type` TINYINT(1) DEFAULT 1 COMMENT '企业类型(1普通企业 2政府机构 3学校 4医院)',
  `industry` VARCHAR(100) COMMENT '所属行业',
  `scale` ENUM('SMALL','MEDIUM','LARGE') DEFAULT 'MEDIUM' COMMENT '企业规模',
  `employee_count` INT(11) DEFAULT 0 COMMENT '员工数量',
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `address` VARCHAR(500) COMMENT '企业地址',
  `license_number` VARCHAR(100) COMMENT '营业执照号',
  `contract_start_date` DATE COMMENT '合同开始日期',
  `contract_end_date` DATE COMMENT '合同结束日期',
  `service_package` VARCHAR(50) COMMENT '服务套餐',
  `max_assessment_count` INT(11) DEFAULT 0 COMMENT '最大测评次数',
  `used_assessment_count` INT(11) DEFAULT 0 COMMENT '已使用测评次数',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态(0停用 1启用 2过期)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_enterprise_code` (`enterprise_code`),
  KEY `idx_status_type` (`status`, `enterprise_type`),
  KEY `idx_contract_date` (`contract_end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业信息表';

-- 13. 企业部门表
DROP TABLE IF EXISTS `psy_t_enterprise_department`;
CREATE TABLE `psy_t_enterprise_department` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `enterprise_id` BIGINT(20) NOT NULL COMMENT '企业ID',
  `parent_id` BIGINT(20) DEFAULT 0 COMMENT '父部门ID',
  `department_code` VARCHAR(50) NOT NULL COMMENT '部门编码',
  `department_name` VARCHAR(100) NOT NULL COMMENT '部门名称',
  `department_level` INT(11) DEFAULT 1 COMMENT '部门层级',
  `manager_id` BIGINT(20) COMMENT '部门负责人ID',
  `employee_count` INT(11) DEFAULT 0 COMMENT '部门人数',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_parent` (`enterprise_id`, `parent_id`),
  KEY `idx_status_sort` (`status`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业部门表';

-- 14. 企业测评计划表
DROP TABLE IF EXISTS `psy_t_enterprise_assessment_plan`;
CREATE TABLE `psy_t_enterprise_assessment_plan` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `enterprise_id` BIGINT(20) NOT NULL COMMENT '企业ID',
  `plan_name` VARCHAR(200) NOT NULL COMMENT '计划名称',
  `plan_type` TINYINT(1) NOT NULL COMMENT '计划类型(1招聘测评 2定期筛查 3培训评估 4专项调研)',
  `scale_id` BIGINT(20) NOT NULL COMMENT '量表ID',
  `target_type` TINYINT(1) NOT NULL COMMENT '目标类型(1全员 2指定部门 3指定员工)',
  `target_departments` JSON COMMENT '目标部门ID列表',
  `target_employees` JSON COMMENT '目标员工ID列表',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME NOT NULL COMMENT '结束时间',
  `is_anonymous` TINYINT(1) DEFAULT 0 COMMENT '是否匿名测评',
  `is_mandatory` TINYINT(1) DEFAULT 1 COMMENT '是否强制参与',
  `reminder_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用提醒',
  `reminder_frequency` INT(11) DEFAULT 24 COMMENT '提醒频率(小时)',
  `description` TEXT COMMENT '计划描述',
  `completion_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
  `total_participants` INT(11) DEFAULT 0 COMMENT '总参与人数',
  `completed_participants` INT(11) DEFAULT 0 COMMENT '已完成人数',
  `status` TINYINT(1) DEFAULT 0 COMMENT '状态(0待开始 1进行中 2已结束 3已取消)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_time` (`enterprise_id`, `start_time`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业测评计划表';

-- 15. 企业测评参与记录表
DROP TABLE IF EXISTS `psy_t_enterprise_assessment_participant`;
CREATE TABLE `psy_t_enterprise_assessment_participant` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '参与记录ID',
  `plan_id` BIGINT(20) NOT NULL COMMENT '计划ID',
  `enterprise_id` BIGINT(20) NOT NULL COMMENT '企业ID',
  `employee_id` BIGINT(20) NOT NULL COMMENT '员工ID',
  `user_id` BIGINT(20) COMMENT '用户ID',
  `record_id` BIGINT(20) COMMENT '测评记录ID',
  `invitation_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '邀请时间',
  `start_time` DATETIME COMMENT '开始时间',
  `completion_time` DATETIME COMMENT '完成时间',
  `total_score` DECIMAL(8,2) COMMENT '总分',
  `result_level` VARCHAR(50) COMMENT '结果等级',
  `participation_status` TINYINT(1) DEFAULT 0 COMMENT '参与状态(0未开始 1进行中 2已完成 3已放弃)',
  `reminder_count` INT(11) DEFAULT 0 COMMENT '提醒次数',
  `last_reminder_time` DATETIME COMMENT '最后提醒时间',
  `completion_source` TINYINT(1) DEFAULT 1 COMMENT '完成方式(1主动完成 2提醒后完成)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_plan_employee` (`plan_id`, `employee_id`),
  KEY `idx_enterprise_plan` (`enterprise_id`, `plan_id`),
  KEY `idx_employee` (`employee_id`),
  KEY `idx_status_time` (`participation_status`, `completion_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业测评参与记录表';