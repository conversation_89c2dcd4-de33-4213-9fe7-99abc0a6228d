package com.xihuan.common.core.domain.entity.store;

import com.xihuan.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.xihuan.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心理咨询门店对象 psy_stores
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyStores extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 门店名称 */
    @Excel(name = "门店名称")
    private String name;

    /** 分店详细名称 */
    @Excel(name = "分店详细名称")
    private String branchName;

    /** 地图展示用名称 */
    @Excel(name = "地图展示用名称")
    private String mapName;

    /** 地图展示用完整地址 */
    @Excel(name = "地图展示用完整地址")
    private String mapAddress;

    /** 门店地址 */
    @Excel(name = "门店地址")
    private String address;

    /** 经度 */
    @Excel(name = "经度")
    private Double longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private Double latitude;

    /** 补充说明 */
    @Excel(name = "补充说明")
    private String remark;

    /** 状态：1-正常 0-关闭 */
    @Excel(name = "状态", readConverterExp = "1=正常,0=关闭")
    private String status;
} 