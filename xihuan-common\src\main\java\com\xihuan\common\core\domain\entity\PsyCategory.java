package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCategory extends BaseEntity {
    /** 分类正常状态 */
    public static final String CATEGORY_NORMAL = "0";

    /** 分类停用状态 */
    public static final String CATEGORY_DISABLED = "1";

    @Excel(name = "分类ID", cellType = Excel.ColumnType.NUMERIC)
    private Long categoryId;

    @Excel(name = "父分类ID", cellType = Excel.ColumnType.NUMERIC)
    private Long parentId;

    @Excel(name = "分类名称")
    private String categoryName;

    @Excel(name = "显示顺序")
    private Integer orderNum;

    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 子分类 */
    private List<PsyCategory> children = new ArrayList<>(); // 子节点列表必须初始化

    /** 关联产品列表 */
    private List<PsyProduct> products = new ArrayList<>();
}