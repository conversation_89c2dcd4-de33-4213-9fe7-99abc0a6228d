<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeRangeMapper">
    
    <resultMap type="PsyTimeRange" id="PsyTimeRangeResult">
        <result property="id"           column="id"           />
        <result property="name"         column="name"         />
        <result property="iconUrl"      column="icon_url"     />
        <result property="startHour"    column="start_hour"   />
        <result property="endHour"      column="end_hour"     />
        <result property="delFlag"      column="del_flag"     />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>

    <sql id="selectPsyTimeRangeVo">
        select id, name, icon_url, start_hour, end_hour, del_flag, create_by, create_time, update_by, update_time, remark
        from psy_time_range
    </sql>

    <select id="selectTimeRangeList" parameterType="PsyTimeRange" resultMap="PsyTimeRangeResult">
        <include refid="selectPsyTimeRangeVo"/>
        <where>  
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="startHour != null"> and start_hour = #{startHour}</if>
            <if test="endHour != null"> and end_hour = #{endHour}</if>
            <if test="delFlag != null"> and del_flag = #{delFlag}</if>
        </where>
        order by start_hour
    </select>
    
    <select id="selectAllActiveTimeRanges" resultMap="PsyTimeRangeResult">
        <include refid="selectPsyTimeRangeVo"/>
        where del_flag = 0
        order by start_hour
    </select>
    
    <select id="selectTimeRangeById" parameterType="Long" resultMap="PsyTimeRangeResult">
        <include refid="selectPsyTimeRangeVo"/>
        where id = #{id}
    </select>
    
    <select id="selectTimeRangeByHour" parameterType="Integer" resultMap="PsyTimeRangeResult">
        <include refid="selectPsyTimeRangeVo"/>
        where #{hour} &gt;= start_hour and #{hour} &lt; end_hour and del_flag = 0
        limit 1
    </select>
        
    <insert id="insertTimeRange" parameterType="PsyTimeRange" useGeneratedKeys="true" keyProperty="id">
        insert into psy_time_range
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">name,</if>
            <if test="iconUrl != null">icon_url,</if>
            <if test="startHour != null">start_hour,</if>
            <if test="endHour != null">end_hour,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null and name != ''">#{name},</if>
            <if test="iconUrl != null">#{iconUrl},</if>
            <if test="startHour != null">#{startHour},</if>
            <if test="endHour != null">#{endHour},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTimeRange" parameterType="PsyTimeRange">
        update psy_time_range
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="iconUrl != null">icon_url = #{iconUrl},</if>
            <if test="startHour != null">start_hour = #{startHour},</if>
            <if test="endHour != null">end_hour = #{endHour},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTimeRangeById" parameterType="Long">
        update psy_time_range set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteTimeRangeByIds" parameterType="String">
        update psy_time_range set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="checkTimeRangeNameUnique" resultType="int">
        select count(1) from psy_time_range 
        where name = #{name} and del_flag = 0
        <if test="id != null">and id != #{id}</if>
    </select>
</mapper>
