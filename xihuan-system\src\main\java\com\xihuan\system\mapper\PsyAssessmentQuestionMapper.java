package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 心理测评题目Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentQuestionMapper {
    
    /**
     * 查询题目列表
     * 
     * @param question 题目信息
     * @return 题目集合
     */
    List<PsyAssessmentQuestion> selectQuestionList(PsyAssessmentQuestion question);

    /**
     * 根据ID查询题目
     * 
     * @param id 题目ID
     * @return 题目信息
     */
    PsyAssessmentQuestion selectQuestionById(Long id);

    /**
     * 查询题目详情（包含选项信息）
     * 
     * @param id 题目ID
     * @return 题目详情
     */
    PsyAssessmentQuestion selectQuestionWithOptions(Long id);

    /**
     * 根据量表ID查询题目列表
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyAssessmentQuestion> selectQuestionsByScaleId(Long scaleId);

    /**
     * 根据量表ID查询题目列表（包含选项信息）
     * 
     * @param scaleId 量表ID
     * @return 题目集合
     */
    List<PsyAssessmentQuestion> selectQuestionsWithOptionsByScaleId(Long scaleId);

    /**
     * 新增题目
     * 
     * @param question 题目信息
     * @return 结果
     */
    int insertQuestion(PsyAssessmentQuestion question);

    /**
     * 修改题目
     * 
     * @param question 题目信息
     * @return 结果
     */
    int updateQuestion(PsyAssessmentQuestion question);

    /**
     * 删除题目
     * 
     * @param id 题目ID
     * @return 结果
     */
    int deleteQuestionById(Long id);

    /**
     * 批量删除题目
     * 
     * @param ids 需要删除的题目ID
     * @return 结果
     */
    int deleteQuestionByIds(Long[] ids);

    /**
     * 根据量表ID删除题目
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteQuestionsByScaleId(Long scaleId);

    /**
     * 查询量表的题目数量
     * 
     * @param scaleId 量表ID
     * @return 题目数量
     */
    int countQuestionsByScaleId(Long scaleId);

    /**
     * 查询量表的维度列表
     * 
     * @param scaleId 量表ID
     * @return 维度列表
     */
    List<String> selectDimensionsByScaleId(Long scaleId);

    /**
     * 根据维度查询题目列表
     * 
     * @param scaleId 量表ID
     * @param dimension 维度
     * @return 题目集合
     */
    List<PsyAssessmentQuestion> selectQuestionsByDimension(@Param("scaleId") Long scaleId, @Param("dimension") String dimension);

    /**
     * 查询下一题
     * 
     * @param scaleId 量表ID
     * @param currentQuestionNo 当前题目序号
     * @return 下一题信息
     */
    PsyAssessmentQuestion selectNextQuestion(@Param("scaleId") Long scaleId, @Param("currentQuestionNo") Integer currentQuestionNo);

    /**
     * 查询上一题
     * 
     * @param scaleId 量表ID
     * @param currentQuestionNo 当前题目序号
     * @return 上一题信息
     */
    PsyAssessmentQuestion selectPreviousQuestion(@Param("scaleId") Long scaleId, @Param("currentQuestionNo") Integer currentQuestionNo);

    /**
     * 批量插入题目
     * 
     * @param questions 题目列表
     * @return 结果
     */
    int batchInsertQuestions(List<PsyAssessmentQuestion> questions);

    /**
     * 更新题目序号
     * 
     * @param id 题目ID
     * @param questionNo 题目序号
     * @return 结果
     */
    int updateQuestionNo(@Param("id") Long id, @Param("questionNo") Integer questionNo);

    /**
     * 检查题目是否被使用
     * 
     * @param id 题目ID
     * @return 数量
     */
    int checkQuestionInUse(Long id);

    /**
     * 查询题目统计信息
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectQuestionStats(Long scaleId);
}
