package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyCourse;
import com.xihuan.common.core.domain.entity.PsyCourseOrder;
import com.xihuan.common.core.domain.entity.PsyMeditation;
import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.domain.dto.WxPayDTO;
import com.xihuan.system.service.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 订单支付服务实现类
 */
@Service
public class OrderPaymentServiceImpl implements IOrderPaymentService {
    private static final Logger log = LoggerFactory.getLogger(OrderPaymentServiceImpl.class);

    @Autowired
    private IWxPayService wxPayService;

    @Autowired
    private IPsyCourseService courseService;

    @Autowired
    private IPsyCourseOrderService courseOrderService;

    @Autowired
    private IPsyMeditationService meditationService;

    @Autowired
    private IPsyMeditationOrderService meditationOrderService;

    // 订单类型映射
    private static final Map<String, String> ORDER_TYPE_PREFIX = new HashMap<>();
    static {
        ORDER_TYPE_PREFIX.put("course", "COURSE");
        ORDER_TYPE_PREFIX.put("meditation", "MEDITATION");
        ORDER_TYPE_PREFIX.put("consultant", "CONSULTANT");
        ORDER_TYPE_PREFIX.put("assessment", "ASSESSMENT");
    }

    @Override
    public WxPayDTO.PayOrderResponse createPayOrder(String orderType, String orderNo, String openid, String clientIp) {
        try {
            log.info("创建支付订单，订单类型: {}, 订单号: {}", orderType, orderNo);

            // 获取订单信息
            OrderInfo orderInfo = getOrderInfo(orderType, orderNo);
            if (orderInfo == null) {
                throw new ServiceException("订单不存在或已失效");
            }

            // 检查订单状态
            if (orderInfo.getStatus() != 0) {
                throw new ServiceException("订单状态异常，无法支付");
            }

            // 构建支付请求
            WxPayDTO.CreatePayOrderRequest request = new WxPayDTO.CreatePayOrderRequest();
            request.setOrderNo(orderNo);
            request.setDescription(orderInfo.getDescription());
            request.setAmount(orderInfo.getAmount());
            request.setOpenid(openid);
            request.setClientIp(clientIp);

            // 调用微信支付服务
            return wxPayService.createPayOrder(request);

        } catch (Exception e) {
            log.error("创建支付订单失败，订单类型: {}, 订单号: {}", orderType, orderNo, e);
            throw new ServiceException("创建支付订单失败: " + e.getMessage());
        }
    }

    @Override
    public WxPayDTO.QueryOrderResponse queryPayOrder(String orderType, String orderNo) {
        try {
            log.info("查询支付订单，订单类型: {}, 订单号: {}", orderType, orderNo);

            WxPayDTO.QueryOrderRequest request = new WxPayDTO.QueryOrderRequest();
            request.setOutTradeNo(orderNo);

            return wxPayService.queryOrder(request);

        } catch (Exception e) {
            log.error("查询支付订单失败，订单类型: {}, 订单号: {}", orderType, orderNo, e);
            throw new ServiceException("查询支付订单失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional
    public boolean handlePaymentSuccess(String orderType, String orderNo, String transactionId, String paymentMethod) {
        try {
            log.info("处理支付成功，订单类型: {}, 订单号: {}, 交易号: {}", orderType, orderNo, transactionId);

            Date paymentTime = new Date();

            switch (orderType) {
                case "course":
                    return courseOrderService.updateOrderPaymentStatus(orderNo, 1, paymentMethod, transactionId, paymentTime) > 0;
                case "meditation":
                    return meditationOrderService.updateOrderPaymentStatus(orderNo, 1, paymentMethod, transactionId, paymentTime) > 0;
                case "consultant":
                    // TODO: 实现咨询订单支付成功处理
                    return true;
                case "assessment":
                    // TODO: 实现测评订单支付成功处理
                    return true;
                default:
                    log.error("未知的订单类型: {}", orderType);
                    return false;
            }

        } catch (Exception e) {
            log.error("处理支付成功失败，订单类型: {}, 订单号: {}", orderType, orderNo, e);
            return false;
        }
    }

    @Override
    public WxPayDTO.RefundResponse refundOrder(String orderType, String orderNo, BigDecimal refundAmount, String reason) {
        try {
            log.info("申请订单退款，订单类型: {}, 订单号: {}, 退款金额: {}", orderType, orderNo, refundAmount);

            // 获取订单信息
            OrderInfo orderInfo = getOrderInfo(orderType, orderNo);
            if (orderInfo == null) {
                throw new ServiceException("订单不存在");
            }

            // 检查订单状态
            if (orderInfo.getStatus() != 1) {
                throw new ServiceException("订单状态异常，无法退款");
            }

            // 生成退款单号
            String refundNo = generateRefundNo(orderType);

            // 构建退款请求
            WxPayDTO.RefundRequest request = new WxPayDTO.RefundRequest();
            request.setOutTradeNo(orderNo);
            request.setOutRefundNo(refundNo);
            request.setReason(reason);
            request.setRefundAmount(refundAmount);
            request.setTotalAmount(orderInfo.getAmount());

            // 调用微信支付退款
            WxPayDTO.RefundResponse response = wxPayService.refund(request);

            // 更新订单退款状态
            updateOrderRefundStatus(orderType, orderNo, refundAmount, reason);

            return response;

        } catch (Exception e) {
            log.error("申请订单退款失败，订单类型: {}, 订单号: {}", orderType, orderNo, e);
            throw new ServiceException("申请退款失败: " + e.getMessage());
        }
    }

    @Override
    public boolean closeOrder(String orderType, String orderNo) {
        try {
            log.info("关闭订单，订单类型: {}, 订单号: {}", orderType, orderNo);

            // 调用微信支付关闭订单
            boolean result = wxPayService.closeOrder(orderNo);

            if (result) {
                // 更新本地订单状态为已取消
                updateOrderStatus(orderType, orderNo, 3);
            }

            return result;

        } catch (Exception e) {
            log.error("关闭订单失败，订单类型: {}, 订单号: {}", orderType, orderNo, e);
            return false;
        }
    }

    @Override
    public String generateOrderNo(String orderType) {
        String prefix = ORDER_TYPE_PREFIX.get(orderType);
        if (StringUtils.isEmpty(prefix)) {
            throw new ServiceException("不支持的订单类型: " + orderType);
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String random = String.format("%04d", new Random().nextInt(10000));

        return prefix + "_" + timestamp + random;
    }

    @Override
    public String parseOrderType(String orderNo) {
        if (StringUtils.isEmpty(orderNo)) {
            return null;
        }

        if (orderNo.startsWith("COURSE_")) {
            return "course";
        } else if (orderNo.startsWith("MEDITATION_")) {
            return "meditation";
        } else if (orderNo.startsWith("CONSULTANT_")) {
            return "consultant";
        } else if (orderNo.startsWith("ASSESSMENT_")) {
            return "assessment";
        }

        return null;
    }

    /**
     * 获取订单信息
     */
    private OrderInfo getOrderInfo(String orderType, String orderNo) {
        switch (orderType) {
            case "course":
                PsyCourseOrder courseOrder = courseOrderService.selectOrderByOrderNo(orderNo);
                if (courseOrder != null) {
                    PsyCourse course = courseService.selectCourseById(courseOrder.getCourseId());
                    OrderInfo orderInfo = new OrderInfo();
                    orderInfo.setOrderNo(orderNo);
                    orderInfo.setAmount(courseOrder.getPaymentAmount());
                    orderInfo.setDescription(course != null ? course.getTitle() : "课程订单");
                    orderInfo.setStatus(courseOrder.getStatus());
                    return orderInfo;
                }
                break;
            case "meditation":
                PsyMeditationOrder meditationOrder = meditationOrderService.selectOrderByOrderNo(orderNo);
                if (meditationOrder != null) {
                    PsyMeditation meditation = meditationService.selectMeditationById(meditationOrder.getMeditationId());
                    OrderInfo orderInfo = new OrderInfo();
                    orderInfo.setOrderNo(orderNo);
                    orderInfo.setAmount(meditationOrder.getPaymentAmount());
                    orderInfo.setDescription(meditation != null ? meditation.getTitle() : "冥想订单");
                    orderInfo.setStatus(meditationOrder.getStatus());
                    return orderInfo;
                }
                break;
            // TODO: 添加其他订单类型的处理
        }
        return null;
    }

    /**
     * 更新订单退款状态
     */
    private void updateOrderRefundStatus(String orderType, String orderNo, BigDecimal refundAmount, String reason) {
        Date refundTime = new Date();
        switch (orderType) {
            case "course":
                courseOrderService.updateOrderRefund(orderNo, refundAmount, refundTime);
                break;
            case "meditation":
                meditationOrderService.updateOrderRefund(orderNo, refundAmount, refundTime);
                break;
            // TODO: 添加其他订单类型的处理
        }
    }

    /**
     * 更新订单状态
     */
    private void updateOrderStatus(String orderType, String orderNo, Integer status) {
        // TODO: 实现各种订单类型的状态更新
    }

    /**
     * 生成退款单号
     */
    private String generateRefundNo(String orderType) {
        String prefix = ORDER_TYPE_PREFIX.get(orderType);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String random = String.format("%04d", new Random().nextInt(10000));
        return "REFUND_" + prefix + "_" + timestamp + random;
    }

    /**
     * 订单信息内部类
     */
    private static class OrderInfo {
        private String orderNo;
        private BigDecimal amount;
        private String description;
        private Integer status;

        public String getOrderNo() {
            return orderNo;
        }

        public void setOrderNo(String orderNo) {
            this.orderNo = orderNo;
        }

        public BigDecimal getAmount() {
            return amount;
        }

        public void setAmount(BigDecimal amount) {
            this.amount = amount;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        public Integer getStatus() {
            return status;
        }

        public void setStatus(Integer status) {
            this.status = status;
        }
    }
}
