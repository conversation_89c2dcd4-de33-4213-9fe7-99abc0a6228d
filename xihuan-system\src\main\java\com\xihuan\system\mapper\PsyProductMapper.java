package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyProduct;
import com.xihuan.common.core.domain.entity.PsyServiceItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

// PsyProductMapper.java
public interface PsyProductMapper {
    List<PsyProduct> selectProductList(PsyProduct product);
    PsyProduct selectProductWithDetails(Long productId);
    int insertProduct(PsyProduct product);
    int updateProduct(PsyProduct product);
    int deleteProductByIds(Long[] productIds);

    int batchInsert(List<PsyServiceItem> list);
    int deleteByProductId(Long productId);
    int deleteByProductIds(Long[] productIds);

    /**
     * 批量插入产品分类关联
     * @param productId 产品ID
     * @param categoryIds 分类ID列表
     * @return 影响行数
     */
    int batchInsertProductCategories(@Param("productId") Long productId, @Param("categoryIds") List<Long> categoryIds);

    /**
     * 删除产品的分类关联
     * @param productId 产品ID
     * @return 影响行数
     */
    int deleteProductCategories(Long productId);

    /**
     * 批量删除产品的分类关联
     * @param productIds 产品ID数组
     * @return 影响行数
     */
    int deleteProductCategoriesByIds(Long[] productIds);
}

