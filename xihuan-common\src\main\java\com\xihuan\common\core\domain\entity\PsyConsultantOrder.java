package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 心理咨询订单表 psy_consultant_order
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyConsultantOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 咨询师ID */
    @Excel(name = "咨询师ID")
    private Long consultantId;

    /** 咨询类型 */
    @Excel(name = "咨询类型")
    private String consultType;

    /** 预约咨询时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "预约咨询时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date scheduledTime;

    /** 预约时长(分钟) */
    @Excel(name = "预约时长(分钟)")
    private Integer duration;

    /** 咨询单价(元/分钟) */
    @Excel(name = "咨询单价")
    private BigDecimal unitPrice;

    /** 原始金额 */
    @Excel(name = "原始金额")
    private BigDecimal originalAmount;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    private BigDecimal discountAmount;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal paymentAmount;

    /** 支付方式 */
    @Excel(name = "支付方式")
    private String paymentMethod;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    /** 订单状态 */
    @Excel(name = "订单状态")
    private String status;

    /** 使用的优惠券ID */
    @Excel(name = "优惠券ID")
    private Long couponId;

    /** 取消时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "取消时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /** 取消原因(用户或咨询师取消原因记录) */
    @Excel(name = "取消原因")
    private String cancelReason;

    /** 关联咨询记录ID */
    @Excel(name = "关联咨询记录ID")
    private Long consultRecordId;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;

    /** 退款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /** 退款原因 */
    @Excel(name = "退款原因")
    private String refundReason;

    /** 删除标志(0正常 2删除) */
    private String delFlag;

    // 关联对象
    /** 用户信息 */
    private SysUser user;

    /** 咨询师信息 */
    private PsyConsultant consultant;

    /** 咨询记录信息 */
    private PsyConsultationRecord consultationRecord;

    /** 优惠券信息 */
    private Object coupon; // TODO 待定义优惠券实体

    // 扩展字段
    /** 是否可以取消 */
    private Boolean canCancel;

    /** 是否可以退款 */
    private Boolean canRefund;

    /** 距离咨询时间(分钟) */
    private Long timeToConsult;

    /** 订单状态描述 */
    private String statusDesc;

    /** 支付状态描述 */
    private String paymentStatusDesc;
}
