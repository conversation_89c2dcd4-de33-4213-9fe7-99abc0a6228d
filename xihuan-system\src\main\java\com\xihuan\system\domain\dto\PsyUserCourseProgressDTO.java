package com.xihuan.system.domain.dto;

import com.xihuan.common.core.domain.entity.PsyUserCourseProgress;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用户课程学习进度数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyUserCourseProgressDTO extends PsyUserCourseProgress {
    
    /** 课程标题 */
    private String courseTitle;
    
    /** 章节标题 */
    private String chapterTitle;
    
    /** 内容类型 */
    private Integer contentType;
    
    /** 章节总时长 */
    private Integer chapterDuration;
    
    /** 课程总进度 */
    private BigDecimal totalProgress;
    
    /** 已完成章节数 */
    private Integer completedChapters;
    
    /** 总章节数 */
    private Integer totalChapters;
    
    /** 章节进度列表 */
    private List<ChapterProgress> chapterProgressList;
    
    /**
     * 章节进度
     */
    @Data
    public static class ChapterProgress {
        
        /** 章节ID */
        private Long chapterId;
        
        /** 章节标题 */
        private String chapterTitle;
        
        /** 章节层级 */
        private Integer level;
        
        /** 内容类型 */
        private Integer contentType;
        
        /** 章节时长 */
        private Integer duration;
        
        /** 学习进度百分比 */
        private BigDecimal progressPercent;
        
        /** 最后播放位置 */
        private Integer lastPosition;
        
        /** 是否完成 */
        private Integer isCompleted;
        
        /** 学习时长 */
        private Integer studyTime;
        
        /** 更新时间 */
        private String updateTime;
    }
    
    /**
     * 更新进度请求DTO
     */
    @Data
    public static class UpdateProgressRequest {
        
        /** 课程ID */
        @NotNull(message = "课程ID不能为空")
        private Long courseId;
        
        /** 章节ID */
        @NotNull(message = "章节ID不能为空")
        private Long chapterId;
        
        /** 学习进度百分比 */
        @NotNull(message = "学习进度不能为空")
        @Min(value = 0, message = "学习进度不能小于0")
        @Max(value = 100, message = "学习进度不能大于100")
        private BigDecimal progressPercent;
        
        /** 最后播放位置 */
        @NotNull(message = "播放位置不能为空")
        @Min(value = 0, message = "播放位置不能小于0")
        private Integer lastPosition;
        
        /** 是否完成 */
        private Integer isCompleted;
        
        /** 本次学习时长（秒） */
        @Min(value = 0, message = "学习时长不能小于0")
        private Integer studyTime;
    }
    
    /**
     * 课程学习统计DTO
     */
    @Data
    public static class CourseStudyStatistics {
        
        /** 课程ID */
        private Long courseId;
        
        /** 课程标题 */
        private String courseTitle;
        
        /** 总学习时长（秒） */
        private Integer totalStudyTime;
        
        /** 总进度百分比 */
        private BigDecimal totalProgress;
        
        /** 已完成章节数 */
        private Integer completedChapters;
        
        /** 总章节数 */
        private Integer totalChapters;
        
        /** 最近学习时间 */
        private String lastStudyTime;
        
        /** 最近学习章节 */
        private String lastChapterTitle;
    }
}
