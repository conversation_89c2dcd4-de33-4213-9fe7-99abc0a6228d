package com.xihuan.web.controller.kangnili;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.kangnili.KangniliQuestion;
import com.xihuan.common.core.domain.kangnili.KangniliSession;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IKangniliQuestionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/system/question")
public class KangniliQuestionController extends BaseController {

    @Autowired
    private IKangniliQuestionService questionService;



    // 查询列表
    @PreAuthorize("@ss.hasPermi('system:question:list')")
    @GetMapping("/list")
    public TableDataInfo list(KangniliQuestion question) {
        startPage();
        List<KangniliQuestion> list = questionService.selectQuestionList(question);
        return getDataTable(list);
    }

    // 新增
    @PreAuthorize("@ss.hasPermi('system:question:add')")
    @Log(title = "抗逆力量表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody KangniliQuestion question) {
        return toAjax(questionService.insertQuestion(question));
    }

    // 修改
    @PreAuthorize("@ss.hasPermi('system:question:edit')")
    @Log(title = "抗逆力量表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody KangniliQuestion question) {
        return toAjax(questionService.updateQuestion(question));
    }

    // 删除
    @PreAuthorize("@ss.hasPermi('system:question:remove')")
    @Log(title = "抗逆力量表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(questionService.deleteQuestionByIds(ids));
    }

    @PreAuthorize("@ss.hasPermi('system:question:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable Long id) {
        return AjaxResult.success(questionService.selectQuestionById(id));
    }

    @GetMapping("/listWithOptions")
    public AjaxResult listWithOptions() {
        List<KangniliQuestion> list = questionService.getQuestionListWithOptions();
        return success(list);
    }

}