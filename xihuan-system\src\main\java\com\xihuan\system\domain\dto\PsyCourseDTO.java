package com.xihuan.system.domain.dto;

import com.xihuan.common.core.domain.entity.PsyCourse;
import com.xihuan.common.core.domain.entity.PsyCourseChapter;
import com.xihuan.common.core.domain.entity.PsyCourseInstructor;
import com.xihuan.common.core.domain.entity.PsyCategory;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

/**
 * 课程数据传输对象
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCourseDTO extends PsyCourse {
    
    /** 课程章节列表 */
    @Valid
    private List<ChapterDTO> chapterList;
    
    /** 讲师信息 */
    private InstructorDTO instructorInfo;
    
    /** 分类信息列表 */
    private List<CategoryDTO> categoryList;
    
    /** 用户是否已购买 */
    private Boolean purchased;
    
    /** 用户学习进度 */
    private BigDecimal userProgress;
    
    /** 用户评价信息 */
    private ReviewDTO userReview;
    
    /**
     * 章节DTO
     */
    @Data
    public static class ChapterDTO {
        
        /** 章节ID */
        private Long id;
        
        /** 章节标题 */
        @NotBlank(message = "章节标题不能为空")
        @Size(max = 100, message = "章节标题长度不能超过100个字符")
        private String chapterTitle;
        
        /** 章节内容 */
        private String chapterContent;
        
        /** 内容类型（0:视频 1:音频 2:文档） */
        @NotNull(message = "内容类型不能为空")
        private Integer contentType;
        
        /** 时长（秒） */
        private Integer duration;
        
        /** 章节排序 */
        private Integer chapterOrder;
        
        /** 是否试听章节 */
        private Integer isTrial;

        /** 媒体文件URL */
        private String mediaUrl;

        /** 媒体文件名称 */
        private String mediaFileName;

        /** 媒体文件大小 */
        private Long mediaFileSize;

        /** 章节层级 */
        private Integer level;

        /** 父章节ID */
        private Long parentId;
        
        /** 子章节列表 */
        private List<ChapterDTO> children;
        
        /** 用户学习进度 */
        private ProgressDTO progress;
    }
    
    /**
     * 讲师DTO
     */
    @Data
    public static class InstructorDTO {
        
        /** 讲师ID */
        private Long id;
        
        /** 讲师姓名 */
        private String name;
        
        /** 讲师头衔 */
        private String title;
        
        /** 讲师资质 */
        private String qualifications;
        
        /** 讲师头像 */
        private String avatar;
    }
    
    /**
     * 分类DTO
     */
    @Data
    public static class CategoryDTO {
        
        /** 分类ID */
        private Long categoryId;
        
        /** 分类名称 */
        private String categoryName;
        
        /** 父分类ID */
        private Long parentId;
        
        /** 显示顺序 */
        private Integer orderNum;
    }
    
    /**
     * 评价DTO
     */
    @Data
    public static class ReviewDTO {
        
        /** 评价ID */
        private Long id;
        
        /** 评价内容 */
        private String content;
        
        /** 评分 */
        private Integer rating;
        
        /** 评价时间 */
        private String createTime;
        
        /** 用户昵称 */
        private String nickName;
    }
    
    /**
     * 学习进度DTO
     */
    @Data
    public static class ProgressDTO {
        
        /** 进度ID */
        private Long id;
        
        /** 学习进度百分比 */
        private BigDecimal progressPercent;
        
        /** 最后播放位置（秒） */
        private Integer lastPosition;
        
        /** 是否完成 */
        private Integer isCompleted;
        
        /** 学习时长（秒） */
        private Integer studyTime;
        
        /** 更新时间 */
        private String updateTime;
    }
}
