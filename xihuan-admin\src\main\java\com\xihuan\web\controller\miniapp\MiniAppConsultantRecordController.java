package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultationRecord;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.IPsyConsultationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序咨询师咨询记录Controller（咨询师端）
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/consultant/record")
public class MiniAppConsultantRecordController extends BaseController {
    
    @Autowired
    private IPsyConsultationRecordService recordService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取咨询师的咨询记录列表
     */
    @GetMapping("/myRecords")
    public AjaxResult getMyRecords(HttpServletRequest request) {
        try {
            // 获取当前咨询师
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            // 验证是否为咨询师
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultationRecord> records = recordService.selectRecordsByConsultantId(consultantId);
            return success(records);
        } catch (Exception e) {
            logger.error("获取咨询师咨询记录失败", e);
            return error("获取咨询记录失败");
        }
    }

    /**
     * 获取咨询记录详情
     */
    @GetMapping("/{recordId}")
    public AjaxResult getRecordDetails(@PathVariable Long recordId, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            PsyConsultationRecord record = recordService.selectRecordWithDetails(recordId);
            if (record == null) {
                return error("咨询记录不存在");
            }
            
            // 验证记录归属
            if (!record.getConsultantId().equals(loginUser.getUserId())) {
                return error("无权限访问该咨询记录");
            }
            
            return success(record);
        } catch (Exception e) {
            logger.error("获取咨询记录详情失败", e);
            return error("获取咨询记录详情失败");
        }
    }

    /**
     * 开始咨询
     */
    @PostMapping("/start/{orderId}")
    public AjaxResult startConsultation(@PathVariable Long orderId, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            PsyConsultationRecord record = recordService.startConsultation(orderId);
            return success(record);
        } catch (Exception e) {
            logger.error("开始咨询失败", e);
            return error(e.getMessage());
        }
    }

    /**
     * 结束咨询
     */
    @PostMapping("/end/{recordId}")
    public AjaxResult endConsultation(@PathVariable Long recordId, 
                                    @RequestParam(required = false) String consultContent, 
                                    HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 验证记录归属
            PsyConsultationRecord record = recordService.selectRecordById(recordId);
            if (record == null || !record.getConsultantId().equals(loginUser.getUserId())) {
                return error("咨询记录不存在或无权限操作");
            }
            
            int result = recordService.endConsultation(recordId, consultContent);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("结束咨询失败", e);
            return error("结束咨询失败");
        }
    }

    /**
     * 中断咨询
     */
    @PostMapping("/interrupt/{recordId}")
    public AjaxResult interruptConsultation(@PathVariable Long recordId,
                                          @RequestParam String interruptType,
                                          @RequestParam String reason,
                                          HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 验证记录归属
            PsyConsultationRecord record = recordService.selectRecordById(recordId);
            if (record == null || !record.getConsultantId().equals(loginUser.getUserId())) {
                return error("咨询记录不存在或无权限操作");
            }
            
            int result = recordService.interruptConsultation(recordId, interruptType, reason);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("中断咨询失败", e);
            return error("中断咨询失败");
        }
    }

    /**
     * 恢复咨询
     */
    @PostMapping("/resume/{recordId}")
    public AjaxResult resumeConsultation(@PathVariable Long recordId, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 验证记录归属
            PsyConsultationRecord record = recordService.selectRecordById(recordId);
            if (record == null || !record.getConsultantId().equals(loginUser.getUserId())) {
                return error("咨询记录不存在或无权限操作");
            }
            
            int result = recordService.resumeConsultation(recordId);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("恢复咨询失败", e);
            return error("恢复咨询失败");
        }
    }

    /**
     * 获取今日咨询记录
     */
    @GetMapping("/today")
    public AjaxResult getTodayRecords(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultationRecord> allRecords = recordService.selectRecordsByConsultantId(consultantId);
            
            // 过滤今日记录
            java.time.LocalDate today = java.time.LocalDate.now();
            List<PsyConsultationRecord> todayRecords = allRecords.stream()
                .filter(record -> {
                    if (record.getStartTime() != null) {
                        java.time.LocalDate recordDate = record.getStartTime().toInstant()
                            .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
                        return recordDate.equals(today);
                    }
                    return false;
                })
                .collect(java.util.stream.Collectors.toList());
            
            return success(todayRecords);
        } catch (Exception e) {
            logger.error("获取今日咨询记录失败", e);
            return error("获取今日咨询记录失败");
        }
    }

    /**
     * 获取咨询师咨询统计
     */
    @GetMapping("/statistics")
    public AjaxResult getConsultationStatistics(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            Map<String, Object> statistics = recordService.getConsultantConsultationStats(consultantId);
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取咨询统计失败", e);
            return error("获取统计信息失败");
        }
    }

    /**
     * 获取正在进行的咨询
     */
    @GetMapping("/ongoing")
    public AjaxResult getOngoingConsultations(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            Long consultantId = loginUser.getUserId();
            List<PsyConsultationRecord> allRecords = recordService.selectRecordsByConsultantId(consultantId);
            
            // 过滤正在进行的咨询
            List<PsyConsultationRecord> ongoingRecords = allRecords.stream()
                .filter(record -> record.getActualStartTime() != null && record.getActualEndTime() == null)
                .collect(java.util.stream.Collectors.toList());
            
            return success(ongoingRecords);
        } catch (Exception e) {
            logger.error("获取正在进行的咨询失败", e);
            return error("获取正在进行的咨询失败");
        }
    }

    /**
     * 更新咨询内容
     */
    @PostMapping("/updateContent/{recordId}")
    public AjaxResult updateConsultContent(@PathVariable Long recordId,
                                         @RequestParam String consultContent,
                                         HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限操作");
            }
            
            // 验证记录归属
            PsyConsultationRecord record = recordService.selectRecordById(recordId);
            if (record == null || !record.getConsultantId().equals(loginUser.getUserId())) {
                return error("咨询记录不存在或无权限操作");
            }
            
            // 更新咨询内容
            record.setConsultContent(consultContent);
            int result = recordService.updateRecord(record);
            return toAjax(result);
        } catch (Exception e) {
            logger.error("更新咨询内容失败", e);
            return error("更新咨询内容失败");
        }
    }

    /**
     * 获取咨询记录的用户信息
     */
    @GetMapping("/{recordId}/userInfo")
    public AjaxResult getUserInfo(@PathVariable Long recordId, HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("请先登录");
            }
            
            if (!isConsultant(loginUser)) {
                return error("您不是咨询师，无权限访问");
            }
            
            PsyConsultationRecord record = recordService.selectRecordWithDetails(recordId);
            if (record == null || !record.getConsultantId().equals(loginUser.getUserId())) {
                return error("咨询记录不存在或无权限访问");
            }
            
            // 只返回必要的用户信息，保护隐私
            Map<String, Object> userInfo = new HashMap<>();
            if (record.getUser() != null) {
                userInfo.put("nickName", record.getUser().getNickName());
                userInfo.put("consultCount", record.getConsultCount());
                // 不返回敏感信息如手机号等
            }
            
            return success(userInfo);
        } catch (Exception e) {
            logger.error("获取用户信息失败", e);
            return error("获取用户信息失败");
        }
    }

    /**
     * 检查是否为咨询师
     */
    private boolean isConsultant(LoginUser loginUser) {
        return loginUser.getUser().getDeptId() != null && 
               loginUser.getUser().getDeptId().equals(201L);
    }
}
