# 分类产品树接口说明

## 接口概述

修改后的 `/treeWithProducts` 接口现在返回所有分类下的对应产品类型：
- **团单(11)** - 包含产品
- **咨询师(12)** - 包含咨询师
- **课程(13)** - 包含课程
- **冥想(14)** - 包含冥想
- **测评(16)** - 包含测评及其子分类

## 🔧 接口信息

### 请求信息
```bash
GET /system/category/treeWithProducts
```

### 响应格式
```json
{
  "code": 200,
  "data": {
    "categories": [
      {
        "categoryId": 11,
        "categoryName": "团单",
        "parentId": 0,
        "orderNum": 1,
        "status": "0",
        "products": [
          {
            "id": 1,
            "name": "心理咨询团购套餐",
            "price": 299.00,
            "description": "专业心理咨询服务团购"
          }
        ],
        "children": []
      },
      {
        "categoryId": 12,
        "categoryName": "咨询师",
        "parentId": 0,
        "orderNum": 2,
        "status": "0",
        "products": [
          {
            "id": 1,
            "name": "张医生",
            "specialty": "焦虑症治疗",
            "experience": "10年经验",
            "rating": 4.8
          }
        ],
        "children": []
      },
      {
        "categoryId": 13,
        "categoryName": "课程",
        "parentId": 0,
        "orderNum": 3,
        "status": "0",
        "products": [
          {
            "id": 1,
            "title": "情绪管理课程",
            "duration": "8课时",
            "price": 199.00,
            "instructor": "李老师"
          }
        ],
        "children": []
      },
      {
        "categoryId": 14,
        "categoryName": "冥想",
        "parentId": 0,
        "orderNum": 4,
        "status": "0",
        "products": [
          {
            "id": 1,
            "title": "正念冥想入门",
            "duration": "15分钟",
            "type": "引导冥想",
            "difficulty": "初级"
          }
        ],
        "children": []
      },
      {
        "categoryId": 16,
        "categoryName": "测评",
        "parentId": 0,
        "orderNum": 5,
        "status": "0",
        "products": [
          {
            "id": 1,
            "title": "心理健康综合测评",
            "description": "全面的心理健康评估",
            "questionCount": 50,
            "price": 29.90,
            "payMode": 1
          }
        ],
        "children": [
          {
            "categoryId": 17,
            "categoryName": "儿童类",
            "parentId": 16,
            "orderNum": 1,
            "status": "0",
            "products": [
              {
                "id": 2,
                "title": "儿童注意力测评",
                "description": "专业的儿童注意力评估工具",
                "questionCount": 30,
                "price": 19.90,
                "payMode": 1
              }
            ]
          },
          {
            "categoryId": 18,
            "categoryName": "健康类",
            "parentId": 16,
            "orderNum": 2,
            "status": "0",
            "products": [...]
          }
        ]
      }
    ],
    "totalCount": 5
  }
}
```

## 🎯 分类产品映射

### 1. 团单(11) - 产品
```json
{
  "categoryId": 11,
  "categoryName": "团单",
  "products": [
    {
      "id": 1,
      "name": "产品名称",
      "price": 299.00,
      "description": "产品描述",
      "originalPrice": 399.00,
      "discount": 0.75,
      "validUntil": "2025-12-31"
    }
  ]
}
```

### 2. 咨询师(12) - 咨询师
```json
{
  "categoryId": 12,
  "categoryName": "咨询师",
  "products": [
    {
      "id": 1,
      "name": "咨询师姓名",
      "title": "主任医师",
      "specialty": "专业领域",
      "experience": "工作经验",
      "rating": 4.8,
      "consultationFee": 200.00,
      "avatar": "/images/counselor1.jpg"
    }
  ]
}
```

### 3. 课程(13) - 课程
```json
{
  "categoryId": 13,
  "categoryName": "课程",
  "products": [
    {
      "id": 1,
      "title": "课程标题",
      "description": "课程描述",
      "instructor": "讲师姓名",
      "duration": "课程时长",
      "price": 199.00,
      "studentCount": 1250,
      "rating": 4.7,
      "coverImage": "/images/course1.jpg"
    }
  ]
}
```

### 4. 冥想(14) - 冥想
```json
{
  "categoryId": 14,
  "categoryName": "冥想",
  "products": [
    {
      "id": 1,
      "title": "冥想标题",
      "description": "冥想描述",
      "duration": "15分钟",
      "type": "引导冥想",
      "difficulty": "初级",
      "narrator": "引导师",
      "audioUrl": "/audio/meditation1.mp3",
      "coverImage": "/images/meditation1.jpg"
    }
  ]
}
```

### 5. 测评(16) - 测评及子分类
```json
{
  "categoryId": 16,
  "categoryName": "测评",
  "products": [
    {
      "id": 1,
      "title": "测评标题",
      "description": "测评描述",
      "imageUrl": "/images/assessment1.jpg",
      "price": 29.90,
      "questionCount": 50,
      "payMode": 1,
      "status": 1
    }
  ],
  "children": [
    {
      "categoryId": 17,
      "categoryName": "儿童类",
      "products": [...]
    }
  ]
}
```

## 🚀 使用示例

### 1. 前端调用
```javascript
// 获取分类产品树
async function getCategoryTreeWithProducts() {
  try {
    const response = await fetch('/system/category/treeWithProducts');
    const result = await response.json();
    
    if (result.code === 200) {
      const categories = result.data.categories;
      renderCategoryTree(categories);
    }
  } catch (error) {
    console.error('获取分类产品树失败:', error);
  }
}

// 渲染分类树
function renderCategoryTree(categories) {
  categories.forEach(category => {
    console.log(`分类: ${category.categoryName}`);
    console.log(`产品数量: ${category.products.length}`);
    
    // 渲染产品
    category.products.forEach(product => {
      renderProduct(category.categoryId, product);
    });
    
    // 渲染子分类
    if (category.children && category.children.length > 0) {
      category.children.forEach(child => {
        console.log(`  子分类: ${child.categoryName}`);
        console.log(`  产品数量: ${child.products.length}`);
      });
    }
  });
}

// 根据分类类型渲染不同的产品
function renderProduct(categoryId, product) {
  switch (categoryId) {
    case 11: // 团单
      renderGroupProduct(product);
      break;
    case 12: // 咨询师
      renderCounselor(product);
      break;
    case 13: // 课程
      renderCourse(product);
      break;
    case 14: // 冥想
      renderMeditation(product);
      break;
    case 16: // 测评
    case 17: case 18: case 19: case 20: case 21: case 22:
      renderAssessment(product);
      break;
  }
}
```

### 2. 小程序使用
```javascript
// 小程序页面
Page({
  data: {
    categoryTree: []
  },
  
  onLoad() {
    this.loadCategoryTree();
  },
  
  async loadCategoryTree() {
    wx.showLoading({ title: '加载中...' });
    
    try {
      const result = await this.request('/system/category/treeWithProducts');
      
      this.setData({
        categoryTree: result.data.categories
      });
      
    } catch (error) {
      wx.showToast({ title: '加载失败', icon: 'error' });
    } finally {
      wx.hideLoading();
    }
  },
  
  // 点击分类
  onCategoryTap(e) {
    const { categoryId, categoryName } = e.currentTarget.dataset;
    
    wx.navigateTo({
      url: `/pages/category/index?categoryId=${categoryId}&categoryName=${categoryName}`
    });
  }
});
```

## ⚠️ 注意事项

### 1. 数据完整性
- 确保各个产品服务都已实现对应的查询方法
- 处理服务调用失败的情况，返回空列表而不是报错

### 2. 性能考虑
- 大量产品时考虑分页或限制返回数量
- 可以添加缓存机制提高响应速度
- 考虑异步加载子分类产品

### 3. 扩展性
- 新增产品类型时只需在 `getProductsByCategoryId` 方法中添加对应的case
- 支持动态配置分类与产品类型的映射关系

### 4. 错误处理
- 各个产品查询方法都有异常处理
- 查询失败时返回空列表，不影响其他分类的数据

现在接口支持返回所有分类下的对应产品类型，提供了完整的分类产品树结构！🎉
