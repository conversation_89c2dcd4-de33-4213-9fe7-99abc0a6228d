<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTScaleMapper">

    <!-- 结果映射 -->
    <resultMap id="ScaleResultMap" type="PsyTScale">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="name" column="name"/>
        <result property="alias" column="alias"/>
        <result property="description" column="description"/>
        <result property="instruction" column="instruction"/>
        <result property="author" column="author"/>
        <result property="version" column="version"/>
        <result property="scoringType" column="scoring_type"/>
        <result property="payMode" column="pay_mode"/>
        <result property="payPhase" column="pay_phase"/>
        <result property="originalPrice" column="original_price"/>
        <result property="currentPrice" column="current_price"/>
        <result property="freeReportLevel" column="free_report_level"/>
        <result property="paidReportLevel" column="paid_report_level"/>
        <result property="timeLimit" column="time_limit"/>
        <result property="questionCount" column="question_count"/>
        <result property="coverImage" column="cover_image"/>
        <result property="tags" column="tags"/>
        <result property="searchKeywords" column="search_keywords"/>
        <result property="searchCount" column="search_count"/>
        <result property="viewCount" column="view_count"/>
        <result property="testCount" column="test_count"/>
        <result property="ratingAvg" column="rating_avg"/>
        <result property="ratingCount" column="rating_count"/>
        <result property="sort" column="sort"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 详细结果映射 -->
    <resultMap id="ScaleDetailMap" type="PsyTScale" extends="ScaleResultMap">
        <collection property="questions" ofType="PsyTQuestion" column="id" select="com.xihuan.system.mapper.PsyTQuestionMapper.selectQuestionsByScaleId"/>
        <collection property="subscales" ofType="PsyTSubscale" column="id" select="com.xihuan.system.mapper.PsyTSubscaleMapper.selectSubscalesByScaleId"/>
        <collection property="scoringRules" ofType="PsyTScoringRule" column="id" select="com.xihuan.system.mapper.PsyTScoringRuleMapper.selectRulesByScaleId"/>
    </resultMap>

    <!-- 查询量表列表 -->
    <select id="selectScaleList" parameterType="PsyTScale" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0'
        <if test="name != null and name != ''">
            AND name LIKE CONCAT('%', #{name}, '%')
        </if>
        <if test="code != null and code != ''">
            AND code LIKE CONCAT('%', #{code}, '%')
        </if>
        <if test="scoringType != null and scoringType != ''">
            AND scoring_type = #{scoringType}
        </if>
        <if test="payMode != null">
            AND pay_mode = #{payMode}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="searchKeywords != null and searchKeywords != ''">
            AND (
                name LIKE CONCAT('%', #{searchKeywords}, '%')
                OR description LIKE CONCAT('%', #{searchKeywords}, '%')
                OR search_keywords LIKE CONCAT('%', #{searchKeywords}, '%')
            )
        </if>
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 根据ID查询量表 -->
    <select id="selectScaleById" parameterType="Long" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 查询量表详情 -->
    <select id="selectScaleWithDetails" parameterType="Long" resultMap="ScaleDetailMap">
        SELECT * FROM psy_t_scale WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 根据编码查询量表 -->
    <select id="selectScaleByCode" parameterType="String" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale WHERE code = #{code} AND del_flag = '0'
    </select>

    <!-- 新增量表 -->
    <insert id="insertScale" parameterType="PsyTScale" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_scale
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="alias != null">alias,</if>
            <if test="description != null">description,</if>
            <if test="instruction != null">instruction,</if>
            <if test="author != null">author,</if>
            <if test="version != null">version,</if>
            <if test="scoringType != null">scoring_type,</if>
            <if test="payMode != null">pay_mode,</if>
            <if test="payPhase != null">pay_phase,</if>
            <if test="originalPrice != null">original_price,</if>
            <if test="currentPrice != null">current_price,</if>
            <if test="freeReportLevel != null">free_report_level,</if>
            <if test="paidReportLevel != null">paid_report_level,</if>
            <if test="timeLimit != null">time_limit,</if>
            <if test="questionCount != null">question_count,</if>
            <if test="coverImage != null">cover_image,</if>
            <if test="tags != null">tags,</if>
            <if test="searchKeywords != null">search_keywords,</if>
            <if test="searchCount != null">search_count,</if>
            <if test="viewCount != null">view_count,</if>
            <if test="testCount != null">test_count,</if>
            <if test="ratingAvg != null">rating_avg,</if>
            <if test="ratingCount != null">rating_count,</if>
            <if test="sort != null">sort,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="code != null and code != ''">#{code},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="alias != null">#{alias},</if>
            <if test="description != null">#{description},</if>
            <if test="instruction != null">#{instruction},</if>
            <if test="author != null">#{author},</if>
            <if test="version != null">#{version},</if>
            <if test="scoringType != null">#{scoringType},</if>
            <if test="payMode != null">#{payMode},</if>
            <if test="payPhase != null">#{payPhase},</if>
            <if test="originalPrice != null">#{originalPrice},</if>
            <if test="currentPrice != null">#{currentPrice},</if>
            <if test="freeReportLevel != null">#{freeReportLevel},</if>
            <if test="paidReportLevel != null">#{paidReportLevel},</if>
            <if test="timeLimit != null">#{timeLimit},</if>
            <if test="questionCount != null">#{questionCount},</if>
            <if test="coverImage != null">#{coverImage},</if>
            <if test="tags != null">#{tags},</if>
            <if test="searchKeywords != null">#{searchKeywords},</if>
            <if test="searchCount != null">#{searchCount},</if>
            <if test="viewCount != null">#{viewCount},</if>
            <if test="testCount != null">#{testCount},</if>
            <if test="ratingAvg != null">#{ratingAvg},</if>
            <if test="ratingCount != null">#{ratingCount},</if>
            <if test="sort != null">#{sort},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <!-- 修改量表 -->
    <update id="updateScale" parameterType="PsyTScale">
        UPDATE psy_t_scale
        <trim prefix="SET" suffixOverrides=",">
            <if test="code != null and code != ''">code = #{code},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="alias != null">alias = #{alias},</if>
            <if test="description != null">description = #{description},</if>
            <if test="instruction != null">instruction = #{instruction},</if>
            <if test="author != null">author = #{author},</if>
            <if test="version != null">version = #{version},</if>
            <if test="scoringType != null">scoring_type = #{scoringType},</if>
            <if test="payMode != null">pay_mode = #{payMode},</if>
            <if test="payPhase != null">pay_phase = #{payPhase},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="currentPrice != null">current_price = #{currentPrice},</if>
            <if test="freeReportLevel != null">free_report_level = #{freeReportLevel},</if>
            <if test="paidReportLevel != null">paid_report_level = #{paidReportLevel},</if>
            <if test="timeLimit != null">time_limit = #{timeLimit},</if>
            <if test="questionCount != null">question_count = #{questionCount},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="searchKeywords != null">search_keywords = #{searchKeywords},</if>
            <if test="searchCount != null">search_count = #{searchCount},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="testCount != null">test_count = #{testCount},</if>
            <if test="ratingAvg != null">rating_avg = #{ratingAvg},</if>
            <if test="ratingCount != null">rating_count = #{ratingCount},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除量表 -->
    <delete id="deleteScaleById" parameterType="Long">
        DELETE FROM psy_t_scale WHERE id = #{id}
    </delete>

    <!-- 批量删除量表 -->
    <delete id="deleteScaleByIds" parameterType="String">
        DELETE FROM psy_t_scale WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询热门量表 -->
    <select id="selectHotScales" parameterType="Integer" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale 
        WHERE del_flag = '0' AND status = 1
        ORDER BY test_count DESC, view_count DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 查询免费量表 -->
    <select id="selectFreeScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale 
        WHERE del_flag = '0' AND status = 1 AND pay_mode = 0
        ORDER BY sort ASC, create_time DESC
    </select>

    <!-- 查询最新量表 -->
    <select id="selectLatestScales" parameterType="Integer" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale 
        WHERE del_flag = '0' AND status = 1
        ORDER BY create_time DESC
        <if test="limit != null">
            LIMIT #{limit}
        </if>
    </select>

    <!-- 更新查看次数 -->
    <update id="updateViewCount" parameterType="Long">
        UPDATE psy_t_scale SET view_count = view_count + 1 WHERE id = #{id}
    </update>

    <!-- 更新搜索次数 -->
    <update id="updateSearchCount" parameterType="Long">
        UPDATE psy_t_scale SET search_count = search_count + 1 WHERE id = #{id}
    </update>

    <!-- 检查量表编码唯一性 -->
    <select id="checkScaleCodeUnique" resultType="int">
        SELECT COUNT(1) FROM psy_t_scale WHERE code = #{code} AND del_flag = '0'
        <if test="excludeId != null and excludeId != 0">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查量表是否被使用 -->
    <select id="checkScaleInUse" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM (
            SELECT 1 FROM psy_t_assessment_record WHERE scale_id = #{id} LIMIT 1
            UNION ALL
            SELECT 1 FROM psy_t_assessment_order WHERE scale_id = #{id} LIMIT 1
        ) t
    </select>

    <!-- 搜索量表 -->
    <select id="searchScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = '0' AND status = 1
        <if test="keyword != null and keyword != ''">
            AND (
                name LIKE CONCAT('%', #{keyword}, '%')
                OR description LIKE CONCAT('%', #{keyword}, '%')
                OR search_keywords LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="scoringType != null and scoringType != ''">
            AND scoring_type = #{scoringType}
        </if>
        <if test="payMode != null">
            AND pay_mode = #{payMode}
        </if>
        ORDER BY sort ASC, test_count DESC, create_time DESC
    </select>

    <!-- 统计量表数量 -->
    <select id="countScales" parameterType="PsyTScale" resultType="int">
        SELECT COUNT(1) FROM psy_t_scale
        WHERE del_flag = '0'
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="payMode != null">
            AND pay_mode = #{payMode}
        </if>
    </select>

    <!-- 批量更新量表状态 -->
    <update id="batchUpdateScaleStatus">
        UPDATE psy_t_scale SET status = #{status}, update_time = sysdate()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
