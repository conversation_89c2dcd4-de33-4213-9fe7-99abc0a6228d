<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMatchQuestionMapper">

    <resultMap id="QuestionResult" type="PsyMatchQuestion">
        <id property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="type" column="type"/>
        <result property="sort" column="sort"/>
        <result property="parentId" column="parent_id"/>
        <result property="isRequired" column="is_required"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <collection property="options" ofType="PsyMatchQuestionOption">
            <id property="id" column="option_id"/>
            <result property="questionId" column="question_id"/>
            <result property="optionText" column="option_text"/>
            <result property="valueCode" column="value_code"/>
            <result property="tagType" column="tag_type"/>
            <result property="recommendTag" column="recommend_tag"/>
            <result property="warningText" column="warning_text"/>
            <result property="sort" column="option_sort"/>
            <collection property="consultantIds" ofType="java.lang.Long">
                <result column="consultant_id"/>
            </collection>
        </collection>
    </resultMap>

    <select id="selectQuestionList" resultMap="QuestionResult">
        SELECT q.*,
               o.id as option_id,
               o.question_id,
               o.option_text,
               o.value_code,
               o.tag_type,
               o.recommend_tag,
               o.warning_text,
               o.sort as option_sort,
               co.consultant_id
        FROM psy_match_question q
        LEFT JOIN psy_match_question_option o ON q.id = o.question_id
        LEFT JOIN psy_match_consultant_option co ON o.id = co.option_id
        WHERE q.del_flag = '0'
        <if test="title != null and title != ''">
            AND q.title LIKE CONCAT('%', #{title}, '%')
        </if>
        ORDER BY q.sort ASC, o.sort ASC
    </select>

    <select id="selectById" parameterType="Long" resultMap="QuestionResult">
        SELECT q.*, 
               o.id as option_id,
               o.question_id,
               o.option_text,
               o.value_code,
               o.tag_type,
               o.recommend_tag,
               o.warning_text,
               o.sort as option_sort,
               co.consultant_id
        FROM psy_match_question q
        LEFT JOIN psy_match_question_option o ON q.id = o.question_id
        LEFT JOIN psy_match_consultant_option co ON o.id = co.option_id
        WHERE q.id = #{id}
    </select>

    <insert id="insert" parameterType="PsyMatchQuestion" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_match_question (
            title, type, sort, parent_id, is_required, status
        ) VALUES (
            #{title}, #{type}, #{sort}, #{parentId}, #{isRequired}, #{status}
        )
    </insert>

    <update id="update" parameterType="PsyMatchQuestion">
        UPDATE psy_match_question
        SET title = #{title},
            type = #{type},
            sort = #{sort},
            parent_id = #{parentId},
            is_required = #{isRequired},
            status = #{status},
            update_by = #{updateBy},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="deleteQuestionOptionsByQuestionId" parameterType="Long">
        DELETE FROM psy_match_question_option WHERE question_id = #{questionId}
    </delete>

    <insert id="insertOption" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_match_question_option
        (question_id, option_text, value_code, tag_type, recommend_tag, warning_text, sort)
        VALUES
        (#{questionId}, #{optionText}, #{valueCode}, #{tagType}, #{recommendTag}, #{warningText}, #{sort})
    </insert>

    <insert id="batchInsertOptions">
        INSERT INTO psy_match_question_option 
        (question_id, option_text, value_code, tag_type, recommend_tag, warning_text, sort) 
        VALUES
        <foreach collection="options" item="option" separator=",">
            (#{questionId}, #{option.optionText}, #{option.valueCode}, #{option.tagType}, 
            #{option.recommendTag}, #{option.warningText}, #{option.sort})
        </foreach>
    </insert>

    <update id="deleteById" parameterType="Long">
        UPDATE psy_match_question
        SET del_flag = '2'
        WHERE id = #{id}
    </update>

    <insert id="batchInsertOptionConsultants">
        INSERT INTO psy_match_consultant_option (option_id, consultant_id)
        VALUES
        <foreach collection="consultantIds" item="consultantId" separator=",">
            (#{optionId}, #{consultantId})
        </foreach>
    </insert>

    <delete id="deleteOptionConsultants" parameterType="Long">
        DELETE FROM psy_match_consultant_option WHERE option_id = #{optionId}
    </delete>

    <select id="selectConsultantIdsByOptionId" parameterType="Long" resultType="Long">
        SELECT consultant_id 
        FROM psy_match_consultant_option 
        WHERE option_id = #{optionId}
    </select>

    <!-- 根据选项ID和咨询领域ID筛选咨询师 -->
    <select id="selectByOptionIds" resultType="PsyConsultant">
        SELECT DISTINCT c.* 
        FROM psy_consultants c
        LEFT JOIN psy_match_consultant_option co ON c.id = co.consultant_id
        LEFT JOIN psy_consultant_expertise ce ON c.id = ce.consultant_id
        WHERE 
        (
            co.option_id IN
            <foreach collection="optionIds" item="optionId" open="(" separator="," close=")">
                #{optionId}
            </foreach>
            <if test="expertiseIds != null and expertiseIds.size() > 0">
                OR ce.expertise_id IN
                <foreach collection="expertiseIds" item="expertiseId" open="(" separator="," close=")">
                    #{expertiseId}
                </foreach>
            </if>
        )
        AND c.audit_status = '1'
        AND c.work_status = '0'
        AND c.del_flag = '0'
        GROUP BY c.id
        HAVING 
            COUNT(DISTINCT co.option_id) = #{optionCount}
            <if test="expertiseIds != null and expertiseIds.size() > 0">
                AND COUNT(DISTINCT CASE WHEN ce.expertise_id IN
                <foreach collection="expertiseIds" item="expertiseId" open="(" separator="," close=")">
                    #{expertiseId}
                </foreach>
                THEN ce.expertise_id END) = #{expertiseCount}
            </if>
    </select>

</mapper> 