package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.entity.PsyImageResource;
import com.xihuan.system.mapper.PsyImageResourceMapper;
import com.xihuan.system.service.wxService.IPsyImageResourceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 心理咨询平台图片资源Service实现
 *
 * <AUTHOR>
 */
@Service
public class PsyImageResourceServiceImpl implements IPsyImageResourceService {
    @Autowired
    private PsyImageResourceMapper psyImageResourceMapper;

    /**
     * 查询图片资源
     */
    @Override
    public PsyImageResource selectPsyImageResourceById(Long id) {
        return psyImageResourceMapper.selectPsyImageResourceById(id);
    }

    /**
     * 查询图片资源列表
     */
    @Override
    public List<PsyImageResource> selectPsyImageResourceList(PsyImageResource psyImageResource) {
        return psyImageResourceMapper.selectPsyImageResourceList(psyImageResource);
    }

    /**
     * 新增图片资源
     */
    @Override
    public int insertPsyImageResource(PsyImageResource psyImageResource) {
        return psyImageResourceMapper.insertPsyImageResource(psyImageResource);
    }

    /**
     * 修改图片资源
     */
    @Override
    public int updatePsyImageResource(PsyImageResource psyImageResource) {
        return psyImageResourceMapper.updatePsyImageResource(psyImageResource);
    }

    /**
     * 批量删除图片资源
     */
    @Override
    public int deletePsyImageResourceByIds(Long[] ids) {
        return psyImageResourceMapper.deletePsyImageResourceByIds(ids);
    }

    /**
     * 删除图片资源
     */
    @Override
    public int deletePsyImageResourceById(Long id) {
        return psyImageResourceMapper.deletePsyImageResourceById(id);
    }
} 