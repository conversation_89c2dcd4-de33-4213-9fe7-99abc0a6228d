package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyAssessmentAnswer;
import com.xihuan.common.core.domain.entity.PsyAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyAssessmentScale;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.common.utils.bean.BeanUtils;
import com.xihuan.common.utils.uuid.IdUtils;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;
import com.xihuan.system.mapper.PsyAssessmentAnswerMapper;
import com.xihuan.system.mapper.PsyAssessmentRecordMapper;
import com.xihuan.system.service.IPsyAssessmentRecordService;
import com.xihuan.system.service.IPsyAssessmentScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 心理测评记录Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyAssessmentRecordServiceImpl implements IPsyAssessmentRecordService {
    
    @Autowired
    private PsyAssessmentRecordMapper recordMapper;
    
    @Autowired
    private PsyAssessmentAnswerMapper answerMapper;
    
    @Autowired
    private IPsyAssessmentScaleService scaleService;

    /**
     * 查询测评记录列表
     * 
     * @param record 测评记录信息
     * @return 测评记录集合
     */
    @Override
    public List<PsyAssessmentRecord> selectRecordList(PsyAssessmentRecord record) {
        return recordMapper.selectRecordList(record);
    }

    /**
     * 根据ID查询测评记录
     * 
     * @param id 测评记录ID
     * @return 测评记录信息
     */
    @Override
    public PsyAssessmentRecord selectRecordById(Long id) {
        return recordMapper.selectRecordById(id);
    }

    /**
     * 查询测评记录详情（包含用户、量表等信息）
     * 
     * @param id 测评记录ID
     * @return 测评记录详情
     */
    @Override
    public PsyAssessmentRecord selectRecordWithDetails(Long id) {
        return recordMapper.selectRecordWithDetails(id);
    }

    /**
     * 根据会话ID查询测评记录
     * 
     * @param sessionId 会话ID
     * @return 测评记录信息
     */
    @Override
    public PsyAssessmentRecord selectRecordBySessionId(String sessionId) {
        return recordMapper.selectRecordBySessionId(sessionId);
    }

    /**
     * 根据用户ID查询测评记录列表
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyAssessmentRecord> selectRecordsByUserId(Long userId) {
        return recordMapper.selectRecordsByUserId(userId);
    }

    /**
     * 根据量表ID查询测评记录列表
     * 
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyAssessmentRecord> selectRecordsByScaleId(Long scaleId) {
        return recordMapper.selectRecordsByScaleId(scaleId);
    }

    /**
     * 新增测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRecord(PsyAssessmentRecord record) {
        // 设置默认值
        if (record.getStatus() == null) {
            record.setStatus(PsyAssessmentRecord.STATUS_IN_PROGRESS);
        }
        if (record.getIsAnonymous() == null) {
            record.setIsAnonymous(PsyAssessmentRecord.ANONYMOUS_NO);
        }
        if (record.getDuration() == null) {
            record.setDuration(0);
        }
        if (record.getDelFlag() == null) {
            record.setDelFlag(PsyAssessmentRecord.DEL_FLAG_NORMAL);
        }
        
        record.setCreateTime(DateUtils.getNowDate());
        return recordMapper.insertRecord(record);
    }

    /**
     * 修改测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRecord(PsyAssessmentRecord record) {
        record.setUpdateTime(DateUtils.getNowDate());
        return recordMapper.updateRecord(record);
    }

    /**
     * 删除测评记录
     * 
     * @param ids 需要删除的测评记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRecordByIds(Long[] ids) {
        return recordMapper.deleteRecordByIds(ids);
    }

    /**
     * 查询用户在指定量表的最新测评记录
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录信息
     */
    @Override
    public PsyAssessmentRecord selectLatestRecordByUserAndScale(Long userId, Long scaleId) {
        return recordMapper.selectLatestRecordByUserAndScale(userId, scaleId);
    }

    /**
     * 查询用户在指定量表的测评次数
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评次数
     */
    @Override
    public int countRecordsByUserAndScale(Long userId, Long scaleId) {
        return recordMapper.countRecordsByUserAndScale(userId, scaleId);
    }

    /**
     * 查询用户的测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserRecordStats(Long userId) {
        return recordMapper.selectUserRecordStats(userId);
    }

    /**
     * 查询量表的测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleRecordStats(Long scaleId) {
        return recordMapper.selectScaleRecordStats(scaleId);
    }

    /**
     * 查询进行中的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyAssessmentRecord> selectInProgressRecords(Long userId) {
        return recordMapper.selectInProgressRecords(userId);
    }

    /**
     * 查询已完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyAssessmentRecord> selectCompletedRecords(Long userId) {
        return recordMapper.selectCompletedRecords(userId);
    }

    /**
     * 开始测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param isAnonymous 是否匿名
     * @return 测评记录信息
     */
    @Override
    @Transactional
    public PsyAssessmentRecord startTest(Long userId, Long scaleId, Integer isAnonymous) {
        // 检查量表是否存在且已发布
        PsyAssessmentScale scale = scaleService.selectScaleById(scaleId);
        if (scale == null || !scale.isPublished()) {
            throw new ServiceException("量表不存在或未发布");
        }
        
        // 生成会话ID
        String sessionId = "session_" + userId + "_" + DateUtils.dateTimeNow() + "_" + IdUtils.fastSimpleUUID().substring(0, 6);
        
        // 创建测评记录
        PsyAssessmentRecord record = new PsyAssessmentRecord();
        record.setUserId(userId);
        record.setScaleId(scaleId);
        record.setSessionId(sessionId);
        record.setStartTime(new Date());
        record.setStatus(PsyAssessmentRecord.STATUS_IN_PROGRESS);
        record.setIsAnonymous(isAnonymous != null ? isAnonymous : PsyAssessmentRecord.ANONYMOUS_NO);
        record.setMaxScore(new BigDecimal(scale.getQuestionCount() * 4)); // 假设每题最高4分
        
        insertRecord(record);
        
        return record;
    }

    /**
     * 提交答案
     * 
     * @param sessionId 会话ID
     * @param questionId 题目ID
     * @param optionId 选项ID
     * @param answerText 答案内容
     * @param timeSpent 答题耗时
     * @return 结果
     */
    @Override
    @Transactional
    public int submitAnswer(String sessionId, Long questionId, Long optionId, String answerText, Integer timeSpent) {
        // 查询测评记录
        PsyAssessmentRecord record = selectRecordBySessionId(sessionId);
        if (record == null || !record.isInProgress()) {
            throw new ServiceException("测评记录不存在或已结束");
        }
        
        // 检查是否已有答案
        PsyAssessmentAnswer existAnswer = answerMapper.selectAnswerByRecordAndQuestion(record.getId(), questionId);
        
        if (existAnswer != null) {
            // 更新答案
            existAnswer.setOptionId(optionId);
            existAnswer.setAnswerText(answerText);
            existAnswer.setTimeSpent(timeSpent);
            existAnswer.setAnswerTime(new Date());
            return answerMapper.updateAnswer(existAnswer);
        } else {
            // 新增答案
            PsyAssessmentAnswer answer = new PsyAssessmentAnswer();
            answer.setRecordId(record.getId());
            answer.setQuestionId(questionId);
            answer.setOptionId(optionId);
            answer.setAnswerText(answerText);
            answer.setTimeSpent(timeSpent);
            answer.setAnswerTime(new Date());
            // TODO: 计算得分
            answer.setScore(new BigDecimal(0));
            
            return answerMapper.insertAnswer(answer);
        }
    }

    /**
     * 完成测评
     * 
     * @param sessionId 会话ID
     * @return 测评记录信息
     */
    @Override
    @Transactional
    public PsyAssessmentRecord completeTest(String sessionId) {
        // 查询测评记录
        PsyAssessmentRecord record = selectRecordBySessionId(sessionId);
        if (record == null || !record.isInProgress()) {
            throw new ServiceException("测评记录不存在或已结束");
        }
        
        // 计算得分和结果
        Map<String, Object> scoreResult = calculateScore(record.getId());
        
        // 更新测评记录
        Date endTime = new Date();
        int duration = (int) ((endTime.getTime() - record.getStartTime().getTime()) / 1000);
        
        recordMapper.completeRecord(
            sessionId,
            endTime,
            duration,
            (BigDecimal) scoreResult.get("totalScore"),
            (BigDecimal) scoreResult.get("percentage"),
            (String) scoreResult.get("resultLevel"),
            (String) scoreResult.get("resultDescription"),
            (String) scoreResult.get("suggestions"),
            (String) scoreResult.get("dimensionScores")
        );
        
        return selectRecordBySessionId(sessionId);
    }

    /**
     * 中断测评
     * 
     * @param sessionId 会话ID
     * @return 结果
     */
    @Override
    @Transactional
    public int interruptTest(String sessionId) {
        return recordMapper.interruptRecord(sessionId);
    }

    /**
     * 查询测评排行榜
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 测评记录集合
     */
    @Override
    public List<PsyAssessmentRecord> selectRecordRanking(Long scaleId, Integer limit) {
        return recordMapper.selectRecordRanking(scaleId, limit);
    }

    /**
     * 查询用户测评历史
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    @Override
    public List<PsyAssessmentRecord> selectUserTestHistory(Long userId, Long scaleId) {
        return recordMapper.selectUserTestHistory(userId, scaleId);
    }

    /**
     * 统计测评记录数量
     * 
     * @param record 查询条件
     * @return 数量
     */
    @Override
    public int countRecords(PsyAssessmentRecord record) {
        return recordMapper.countRecords(record);
    }

    /**
     * 查询测评趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectRecordTrend(Integer days) {
        return recordMapper.selectRecordTrend(days);
    }

    /**
     * 获取测评记录DTO
     * 
     * @param id 测评记录ID
     * @return 测评记录DTO
     */
    @Override
    public PsyAssessmentDTO.RecordDTO getRecordDTO(Long id) {
        PsyAssessmentRecord record = selectRecordWithDetails(id);
        if (record == null) {
            return null;
        }
        
        PsyAssessmentDTO.RecordDTO dto = new PsyAssessmentDTO.RecordDTO();
        BeanUtils.copyBeanProp(dto, record);
        
        // 设置扩展字段
        dto.setStatusDesc(record.getStatusDesc());
        dto.setFormattedDuration(record.getFormattedDuration());
        dto.setFormattedPercentage(record.getFormattedPercentage());
        dto.setStartTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, record.getStartTime()));
        if (record.getEndTime() != null) {
            dto.setEndTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, record.getEndTime()));
        }
        
        return dto;
    }

    /**
     * 获取测评记录列表DTO
     * 
     * @param record 查询条件
     * @return 测评记录DTO列表
     */
    @Override
    public List<PsyAssessmentDTO.RecordDTO> getRecordListDTO(PsyAssessmentRecord record) {
        List<PsyAssessmentRecord> recordList = selectRecordList(record);
        List<PsyAssessmentDTO.RecordDTO> dtoList = new ArrayList<>();
        
        for (PsyAssessmentRecord item : recordList) {
            PsyAssessmentDTO.RecordDTO dto = getRecordDTO(item.getId());
            if (dto != null) {
                dtoList.add(dto);
            }
        }
        
        return dtoList;
    }

    /**
     * 获取测评结果DTO
     * 
     * @param sessionId 会话ID
     * @return 测评结果DTO
     */
    @Override
    public PsyAssessmentDTO.RecordDTO getTestResultDTO(String sessionId) {
        PsyAssessmentRecord record = selectRecordBySessionId(sessionId);
        if (record == null || !record.isCompleted()) {
            return null;
        }
        
        return getRecordDTO(record.getId());
    }

    /**
     * 获取测评答案列表
     * 
     * @param recordId 测评记录ID
     * @return 答案列表
     */
    @Override
    public List<PsyAssessmentAnswer> getAnswerList(Long recordId) {
        return answerMapper.selectAnswersWithDetailsByRecordId(recordId);
    }

    /**
     * 获取测评答案DTO列表
     * 
     * @param recordId 测评记录ID
     * @return 答案DTO列表
     */
    @Override
    public List<PsyAssessmentDTO.AnswerDTO> getAnswerListDTO(Long recordId) {
        List<PsyAssessmentAnswer> answerList = getAnswerList(recordId);
        List<PsyAssessmentDTO.AnswerDTO> dtoList = new ArrayList<>();
        
        for (PsyAssessmentAnswer answer : answerList) {
            PsyAssessmentDTO.AnswerDTO dto = new PsyAssessmentDTO.AnswerDTO();
            BeanUtils.copyBeanProp(dto, answer);
            
            // 设置扩展字段
            dto.setFormattedTimeSpent(answer.getFormattedTimeSpent());
            dto.setAnswerDisplay(answer.getAnswerDisplay());
            dto.setAnswerTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, answer.getAnswerTime()));
            
            dtoList.add(dto);
        }
        
        return dtoList;
    }

    /**
     * 计算测评得分
     * 
     * @param recordId 测评记录ID
     * @return 得分信息
     */
    @Override
    public Map<String, Object> calculateScore(Long recordId) {
        // TODO: 实现具体的计分逻辑
        // 这里需要根据量表的计分规则和结果解释规则来计算
        return null;
    }

    /**
     * 生成测评报告
     * 
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    @Override
    public String generateReport(Long recordId) {
        // TODO: 实现测评报告生成逻辑
        return null;
    }
}
