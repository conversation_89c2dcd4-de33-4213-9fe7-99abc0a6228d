package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;

import com.xihuan.common.core.domain.consultant.PsyConsultant;

/**
 * 心理咨询预约表实体类
 */
@Data
public class PsyAppointment extends BaseEntity {
    private static final long serialVersionUID = 2L;

    /**
     * 预约记录唯一标识
     */
    @Id
    private Long appointmentId;

    /**
     * 关联咨询师ID（对应psy_counselor.counselor_id）
     */
    private Long counselorId;

    /**
     * 预约日期（格式：yyyy-MM-dd）
     */
    private Date appointmentDate;

    /**
     * 时间段（示例：09:00-10:00）
     */
    private String timeSlot;

    /**
     * 星期显示（示例：周一/今天）
     */
    private String weekDay;

    /**
     * 预约状态（0可预约 1已满 2已过期）
     */
    private String status = "0";

    // 以下字段继承自BaseEntity
    // create_time 创建时间
    // update_time 更新时间
    // create_by 创建人
    // update_by 更新人
    // del_flag 删除标记

    /**
     * 关联咨询师信息（非数据库字段）
     */
    private PsyConsultant consultantInfo;

    /**
     * 关联用户信息（非数据库字段）
     */
    private SysUser userInfo;

    // 新增逻辑删除字段
    private Integer delFlag;

    private Integer isPublic;

}