package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyAssessmentOrder;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 心理测评订单Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyAssessmentOrderService {
    
    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectOrderList(PsyAssessmentOrder order);

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    PsyAssessmentOrder selectOrderById(Long id);

    /**
     * 查询订单详情（包含用户、量表等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyAssessmentOrder selectOrderWithDetails(Long id);

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单信息
     */
    PsyAssessmentOrder selectOrderByOrderNo(String orderNo);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectOrdersByUserId(Long userId);

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int insertOrder(PsyAssessmentOrder order);

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int updateOrder(PsyAssessmentOrder order);

    /**
     * 删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param couponId 优惠券ID
     * @return 订单信息
     */
    PsyAssessmentOrder createOrder(Long userId, Long scaleId, Long couponId);

    /**
     * 支付订单
     * 
     * @param orderNo 订单编号
     * @param paymentMethod 支付方式
     * @return 结果
     */
    int payOrder(String orderNo, String paymentMethod);

    /**
     * 完成订单
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    int completeOrder(String orderNo);

    /**
     * 取消订单
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    int cancelOrder(String orderNo);

    /**
     * 申请退款
     * 
     * @param orderNo 订单编号
     * @param refundReason 退款原因
     * @return 结果
     */
    int applyRefund(String orderNo, String refundReason);

    /**
     * 处理退款
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    int processRefund(String orderNo);

    /**
     * 查询用户在指定量表的有效订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 订单信息
     */
    PsyAssessmentOrder selectValidOrderByUserAndScale(Long userId, Long scaleId);

    /**
     * 检查用户是否已购买量表
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 是否已购买
     */
    boolean checkUserPurchased(Long userId, Long scaleId);

    /**
     * 查询待支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectPendingOrders(Long userId);

    /**
     * 查询已支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectPaidOrders(Long userId);

    /**
     * 查询已完成订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectCompletedOrders(Long userId);

    /**
     * 查询已取消订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectCancelledOrders(Long userId);

    /**
     * 处理过期订单
     * 
     * @return 处理数量
     */
    int handleExpiredOrders();

    /**
     * 查询订单统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectOrderStats();

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserOrderStats(Long userId);

    /**
     * 查询量表订单统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleOrderStats(Long scaleId);

    /**
     * 查询订单收入统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<Map<String, Object>> selectOrderIncomeStats(Integer days);

    /**
     * 查询热销量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<Map<String, Object>> selectHotSalesScales(Integer limit);

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    String generateOrderNo();

    /**
     * 计算订单金额
     * 
     * @param scaleId 量表ID
     * @param couponId 优惠券ID
     * @return 订单金额信息
     */
    Map<String, BigDecimal> calculateOrderAmount(Long scaleId, Long couponId);

    /**
     * 获取订单DTO
     * 
     * @param id 订单ID
     * @return 订单DTO
     */
    PsyAssessmentDTO.OrderDTO getOrderDTO(Long id);

    /**
     * 获取订单列表DTO
     * 
     * @param order 查询条件
     * @return 订单DTO列表
     */
    List<PsyAssessmentDTO.OrderDTO> getOrderListDTO(PsyAssessmentOrder order);

    /**
     * 统计订单数量
     * 
     * @param order 查询条件
     * @return 数量
     */
    int countOrders(PsyAssessmentOrder order);

    /**
     * 查询订单趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<Map<String, Object>> selectOrderTrend(Integer days);
}
