<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeAppointmentSlotMapper">
    
    <resultMap type="PsyTimeAppointmentSlot" id="PsyTimeAppointmentSlotResult">
        <result property="appointmentId"    column="appointment_id"   />
        <result property="slotId"           column="slot_id"          />
    </resultMap>

    <select id="selectSlotsByAppointmentId" parameterType="String" resultMap="PsyTimeAppointmentSlotResult">
        select appointment_id, slot_id
        from psy_time_appointment_slot
        where appointment_id = #{appointmentId}
    </select>
    
    <select id="selectAppointmentBySlotId" parameterType="Long" resultMap="PsyTimeAppointmentSlotResult">
        select appointment_id, slot_id
        from psy_time_appointment_slot
        where slot_id = #{slotId}
        limit 1
    </select>
        
    <insert id="insertAppointmentSlot" parameterType="PsyTimeAppointmentSlot">
        insert into psy_time_appointment_slot (appointment_id, slot_id)
        values (#{appointmentId}, #{slotId})
    </insert>

    <insert id="batchInsertAppointmentSlots">
        insert into psy_time_appointment_slot (appointment_id, slot_id)
        values
        <foreach collection="slotIds" item="slotId" separator=",">
            (#{appointmentId}, #{slotId})
        </foreach>
    </insert>

    <delete id="deleteAppointmentSlot">
        delete from psy_time_appointment_slot 
        where appointment_id = #{appointmentId} and slot_id = #{slotId}
    </delete>

    <delete id="deleteSlotsByAppointmentId" parameterType="String">
        delete from psy_time_appointment_slot where appointment_id = #{appointmentId}
    </delete>

    <delete id="deleteAppointmentBySlotId" parameterType="Long">
        delete from psy_time_appointment_slot where slot_id = #{slotId}
    </delete>
</mapper>
