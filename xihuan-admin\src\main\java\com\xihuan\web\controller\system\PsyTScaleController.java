package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 量表基础信息Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/scale")
public class PsyTScaleController extends BaseController {
    
    @Autowired
    private IPsyTScaleService scaleService;

    /**
     * 查询量表列表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTScale scale) {
        startPage();
        List<PsyTScale> list = scaleService.selectScaleList(scale);
        return getDataTable(list);
    }

    /**
     * 导出量表列表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:export')")
    @Log(title = "量表基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTScale scale) {
        List<PsyTScale> list = scaleService.selectScaleList(scale);
        ExcelUtil<PsyTScale> util = new ExcelUtil<>(PsyTScale.class);
        util.exportExcel(response, list, "量表基础信息数据");
    }

    /**
     * 获取量表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:scale:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(scaleService.selectScaleWithDetails(id));
    }

    /**
     * 新增量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:add')")
    @Log(title = "量表基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTScale scale) {
        return toAjax(scaleService.insertScale(scale));
    }

    /**
     * 修改量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @Log(title = "量表基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTScale scale) {
        return toAjax(scaleService.updateScale(scale));
    }

    /**
     * 删除量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:remove')")
    @Log(title = "量表基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scaleService.deleteScaleByIds(ids));
    }

    /**
     * 根据编码查询量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:query')")
    @GetMapping("/code/{code}")
    public AjaxResult getByCode(@PathVariable("code") String code) {
        return success(scaleService.selectScaleByCode(code));
    }

    /**
     * 查询热门量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/hot")
    public AjaxResult getHotScales(@RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectHotScales(limit);
        return success(list);
    }

    /**
     * 查询免费量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/free")
    public AjaxResult getFreeScales() {
        List<PsyTScale> list = scaleService.selectFreeScales();
        return success(list);
    }

    /**
     * 查询最新量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/latest")
    public AjaxResult getLatestScales(@RequestParam(defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectLatestScales(limit);
        return success(list);
    }

    /**
     * 搜索量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/search")
    public TableDataInfo searchScales(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String scoringType,
            @RequestParam(required = false) Integer payMode,
            @RequestParam(required = false) Long enterpriseId) {
        startPage();
        List<PsyTScale> list = scaleService.searchScales(keyword, scoringType, payMode, enterpriseId);
        return getDataTable(list);
    }

    /**
     * 更新查看次数
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @PostMapping("/{id}/view")
    public AjaxResult updateViewCount(@PathVariable("id") Long id) {
        return toAjax(scaleService.updateViewCount(id));
    }

    /**
     * 更新搜索次数
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @PostMapping("/{id}/search")
    public AjaxResult updateSearchCount(@PathVariable("id") Long id) {
        return toAjax(scaleService.updateSearchCount(id));
    }

    /**
     * 批量更新状态
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @Log(title = "量表基础信息", businessType = BusinessType.UPDATE)
    @PutMapping("/status")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params) {
        Long[] ids = (Long[]) params.get("ids");
        Integer status = (Integer) params.get("status");
        return toAjax(scaleService.batchUpdateScaleStatus(ids, status));
    }

    /**
     * 发布量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @Log(title = "量表基础信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/publish")
    public AjaxResult publish(@PathVariable("id") Long id) {
        return toAjax(scaleService.publishScale(id));
    }

    /**
     * 下架量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:edit')")
    @Log(title = "量表基础信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/unpublish")
    public AjaxResult unpublish(@PathVariable("id") Long id) {
        return toAjax(scaleService.unpublishScale(id));
    }

    /**
     * 复制量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:add')")
    @Log(title = "量表基础信息", businessType = BusinessType.INSERT)
    @PostMapping("/{id}/copy")
    public AjaxResult copy(@PathVariable("id") Long id, @RequestBody PsyTScale targetScale) {
        return toAjax(scaleService.copyScale(id, targetScale));
    }

    /**
     * 验证量表完整性
     */
    @PreAuthorize("@ss.hasPermi('system:scale:query')")
    @GetMapping("/{id}/validate")
    public AjaxResult validate(@PathVariable("id") Long id) {
        Map<String, Object> result = scaleService.validateScaleCompleteness(id);
        return success(result);
    }

    /**
     * 查询量表统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        List<Map<String, Object>> stats = scaleService.selectScaleStats();
        return success(stats);
    }

    /**
     * 查询量表测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/{id}/test-stats")
    public AjaxResult getTestStats(@PathVariable("id") Long id) {
        Map<String, Object> stats = scaleService.selectScaleTestStats(id);
        return success(stats);
    }

    /**
     * 查询量表使用统计
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/{id}/usage-stats")
    public AjaxResult getUsageStats(
            @PathVariable("id") Long id,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate) {
        Map<String, Object> stats = scaleService.selectScaleUsageStats(id, startDate, endDate);
        return success(stats);
    }

    /**
     * 查询量表趋势统计
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/trend-stats")
    public AjaxResult getTrendStats(@RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> stats = scaleService.selectScaleTrendStats(days);
        return success(stats);
    }

    /**
     * 查询量表分类统计
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/category-stats")
    public AjaxResult getCategoryStats() {
        List<Map<String, Object>> stats = scaleService.selectScaleCategoryStats();
        return success(stats);
    }

    /**
     * 查询量表评分分布
     */
    @PreAuthorize("@ss.hasPermi('system:scale:list')")
    @GetMapping("/{id}/score-distribution")
    public AjaxResult getScoreDistribution(@PathVariable("id") Long id) {
        List<Map<String, Object>> distribution = scaleService.selectScaleScoreDistribution(id);
        return success(distribution);
    }

    /**
     * 导入量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:import')")
    @Log(title = "量表基础信息", businessType = BusinessType.IMPORT)
    @PostMapping("/import")
    public AjaxResult importScale(@RequestBody Map<String, Object> scaleData) {
        return toAjax(scaleService.importScale(scaleData));
    }

    /**
     * 导出量表
     */
    @PreAuthorize("@ss.hasPermi('system:scale:export')")
    @GetMapping("/{id}/export")
    public AjaxResult exportScale(@PathVariable("id") Long id) {
        Map<String, Object> data = scaleService.exportScale(id);
        return success(data);
    }

    /**
     * 检查编码唯一性
     */
    @GetMapping("/checkCodeUnique")
    public AjaxResult checkCodeUnique(
            @RequestParam String code,
            @RequestParam(required = false) Long excludeId) {
        boolean unique = scaleService.checkScaleCodeUnique(code, excludeId);
        return success(unique);
    }

    /**
     * 检查量表是否被使用
     */
    @PreAuthorize("@ss.hasPermi('system:scale:query')")
    @GetMapping("/{id}/check-in-use")
    public AjaxResult checkInUse(@PathVariable("id") Long id) {
        boolean inUse = scaleService.checkScaleInUse(id);
        return success(inUse);
    }
}
