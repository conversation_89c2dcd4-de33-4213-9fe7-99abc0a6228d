package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 题目选项表 psy_t_question_option
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTQuestionOption extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 选项ID */
    @Excel(name = "选项ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 关联psy_t_question.id */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /** 选项文本 */
    @Excel(name = "选项文本")
    @NotBlank(message = "选项文本不能为空")
    @Size(max = 200, message = "选项文本不能超过200个字符")
    private String optionText;

    /** 选项分值 */
    @Excel(name = "选项分值")
    @NotNull(message = "选项分值不能为空")
    private Integer optionValue;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 状态 */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    private Integer status;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 题目信息 */
    private PsyTQuestion question;

    // 扩展字段
    /** 题目内容 */
    private String questionContent;

    /** 题目序号 */
    private Integer questionNo;

    /** 量表ID */
    private Long scaleId;

    /** 量表名称 */
    private String scaleName;

    /** 选项标识(A、B、C、D等) */
    private String optionLabel;

    /** 是否被选中 */
    private Boolean selected;

    /** 是否正确答案 */
    private Boolean isCorrect;

    // 常量定义
    /** 状态：停用 */
    public static final Integer STATUS_DISABLED = 0;
    
    /** 状态：启用 */
    public static final Integer STATUS_ENABLED = 1;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "停用";
            case 1: return "启用";
            default: return "未知";
        }
    }

    /**
     * 获取选项标识
     */
    public String getOptionLabelBySort() {
        if (sort == null) return "";
        if (sort <= 0) return "";
        
        // 生成A、B、C、D...标识
        char label = (char) ('A' + sort - 1);
        return String.valueOf(label);
    }

    /**
     * 获取选项完整描述
     */
    public String getFullOptionDesc() {
        String label = getOptionLabelBySort();
        if (label.isEmpty()) {
            return optionText;
        }
        return label + ". " + optionText;
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return STATUS_ENABLED.equals(status);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 获取分值描述
     */
    public String getValueDesc() {
        if (optionValue == null) return "0";
        return optionValue.toString() + "分";
    }
}
