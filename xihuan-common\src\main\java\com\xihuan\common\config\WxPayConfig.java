package com.xihuan.common.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 微信支付配置类
 */
@Configuration
@ConfigurationProperties(prefix = "wx.pay")
public class WxPayConfig {

    /**
     * 微信支付商户号
     */
    private String mchId;

    /**
     * 微信支付商户API密钥
     */
    private String mchKey;

    /**
     * 微信支付商户证书路径
     */
    private String mchCertPath;

    /**
     * 微信支付商户证书密码
     */
    private String mchCertPassword;

    /**
     * 微信支付API V3密钥
     */
    private String apiV3Key;

    /**
     * 微信支付回调地址
     */
    private String notifyUrl;

    /**
     * 微信小程序APPID
     */
    private String appId;

    /**
     * 微信支付服务商模式下的子商户公众账号ID
     */
    private String subAppId;

    /**
     * 微信支付服务商模式下的子商户号
     */
    private String subMchId;

    /**
     * 是否使用沙箱环境
     */
    private Boolean useSandbox = false;

    /**
     * 是否使用V3版本接口
     */
    private Boolean useV3 = true;

    /**
     * 私钥路径
     */
    private String privateKeyPath;

    /**
     * 私钥内容，当无法通过privateKeyPath读取时使用
     */
    private String privateKey;

    /**
     * 平台证书序列号
     */
    private String certSerialNo;

    /**
     * API V3平台证书路径
     */
    private String apiV3CertPath;

    /**
     * 超时时间，单位分钟
     */
    private Integer timeoutMinutes = 5;

    public String getMchId() {
        return mchId;
    }

    public void setMchId(String mchId) {
        this.mchId = mchId;
    }

    public String getMchKey() {
        return mchKey;
    }

    public void setMchKey(String mchKey) {
        this.mchKey = mchKey;
    }

    public String getMchCertPath() {
        return mchCertPath;
    }

    public void setMchCertPath(String mchCertPath) {
        this.mchCertPath = mchCertPath;
    }

    public String getMchCertPassword() {
        return mchCertPassword;
    }

    public void setMchCertPassword(String mchCertPassword) {
        this.mchCertPassword = mchCertPassword;
    }

    public String getApiV3Key() {
        return apiV3Key;
    }

    public void setApiV3Key(String apiV3Key) {
        this.apiV3Key = apiV3Key;
    }

    public String getNotifyUrl() {
        return notifyUrl;
    }

    public void setNotifyUrl(String notifyUrl) {
        this.notifyUrl = notifyUrl;
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getSubAppId() {
        return subAppId;
    }

    public void setSubAppId(String subAppId) {
        this.subAppId = subAppId;
    }

    public String getSubMchId() {
        return subMchId;
    }

    public void setSubMchId(String subMchId) {
        this.subMchId = subMchId;
    }

    public Boolean getUseSandbox() {
        return useSandbox;
    }

    public void setUseSandbox(Boolean useSandbox) {
        this.useSandbox = useSandbox;
    }

    public Boolean getUseV3() {
        return useV3;
    }

    public void setUseV3(Boolean useV3) {
        this.useV3 = useV3;
    }

    public String getPrivateKeyPath() {
        return privateKeyPath;
    }

    public void setPrivateKeyPath(String privateKeyPath) {
        this.privateKeyPath = privateKeyPath;
    }

    public String getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(String privateKey) {
        this.privateKey = privateKey;
    }

    public String getCertSerialNo() {
        return certSerialNo;
    }

    public void setCertSerialNo(String certSerialNo) {
        this.certSerialNo = certSerialNo;
    }

    public String getApiV3CertPath() {
        return apiV3CertPath;
    }

    public void setApiV3CertPath(String apiV3CertPath) {
        this.apiV3CertPath = apiV3CertPath;
    }

    public Integer getTimeoutMinutes() {
        return timeoutMinutes;
    }

    public void setTimeoutMinutes(Integer timeoutMinutes) {
        this.timeoutMinutes = timeoutMinutes;
    }
}
