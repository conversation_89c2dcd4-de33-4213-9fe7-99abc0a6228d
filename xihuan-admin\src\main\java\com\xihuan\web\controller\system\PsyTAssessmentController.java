package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTAnswerRecord;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsyTAssessmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 测评流程Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/assessment")
public class PsyTAssessmentController extends BaseController {
    
    @Autowired
    private IPsyTAssessmentService assessmentService;

    /**
     * 开始测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:start')")
    @Log(title = "测评流程", businessType = BusinessType.INSERT)
    @PostMapping("/start")
    public AjaxResult startAssessment(
            @RequestParam Long userId,
            @RequestParam Long scaleId,
            @RequestParam(defaultValue = "personal") String source) {
        Map<String, Object> result = assessmentService.startAssessment(userId, scaleId, source);
        return success(result);
    }

    /**
     * 获取测评题目
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/question/{questionNo}")
    public AjaxResult getQuestion(
            @PathVariable("sessionId") String sessionId,
            @PathVariable("questionNo") Integer questionNo) {
        Map<String, Object> question = assessmentService.getAssessmentQuestion(sessionId, questionNo);
        return success(question);
    }

    /**
     * 提交答案
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:answer')")
    @Log(title = "测评流程", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/answer")
    public AjaxResult submitAnswer(
            @PathVariable("sessionId") String sessionId,
            @RequestParam Long questionId,
            @RequestParam String answerContent,
            @RequestParam(required = false) Integer responseTime) {
        Map<String, Object> result = assessmentService.submitAnswer(sessionId, questionId, answerContent, responseTime);
        return success(result);
    }

    /**
     * 批量提交答案
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:answer')")
    @Log(title = "测评流程", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/batch-answer")
    public AjaxResult batchSubmitAnswers(
            @PathVariable("sessionId") String sessionId,
            @RequestBody List<PsyTAnswerRecord> answers) {
        Map<String, Object> result = assessmentService.batchSubmitAnswers(sessionId, answers);
        return success(result);
    }

    /**
     * 获取下一题
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/next")
    public AjaxResult getNextQuestion(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> question = assessmentService.getNextQuestion(sessionId);
        return success(question);
    }

    /**
     * 获取上一题
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/previous")
    public AjaxResult getPreviousQuestion(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> question = assessmentService.getPreviousQuestion(sessionId);
        return success(question);
    }

    /**
     * 跳转到指定题目
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/jump/{questionNo}")
    public AjaxResult jumpToQuestion(
            @PathVariable("sessionId") String sessionId,
            @PathVariable("questionNo") Integer questionNo) {
        Map<String, Object> question = assessmentService.jumpToQuestion(sessionId, questionNo);
        return success(question);
    }

    /**
     * 保存测评进度
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:save')")
    @Log(title = "测评流程", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/save")
    public AjaxResult saveProgress(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.saveProgress(sessionId);
        return success(result);
    }

    /**
     * 恢复测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:resume')")
    @PostMapping("/{sessionId}/resume")
    public AjaxResult resumeAssessment(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.resumeAssessment(sessionId);
        return success(result);
    }

    /**
     * 暂停测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:pause')")
    @Log(title = "测评流程", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/pause")
    public AjaxResult pauseAssessment(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.pauseAssessment(sessionId);
        return success(result);
    }

    /**
     * 完成测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:complete')")
    @Log(title = "测评流程", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/complete")
    public AjaxResult completeAssessment(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.completeAssessment(sessionId);
        return success(result);
    }

    /**
     * 放弃测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:abandon')")
    @Log(title = "测评流程", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/abandon")
    public AjaxResult abandonAssessment(
            @PathVariable("sessionId") String sessionId,
            @RequestParam(required = false) String reason) {
        Map<String, Object> result = assessmentService.abandonAssessment(sessionId, reason);
        return success(result);
    }

    /**
     * 获取测评进度
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/progress")
    public AjaxResult getProgress(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> progress = assessmentService.getAssessmentProgress(sessionId);
        return success(progress);
    }

    /**
     * 获取测评状态
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/status")
    public AjaxResult getStatus(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> status = assessmentService.getAssessmentStatus(sessionId);
        return success(status);
    }

    /**
     * 计算测评结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:calculate')")
    @PostMapping("/record/{recordId}/calculate")
    public AjaxResult calculateResult(@PathVariable("recordId") Long recordId) {
        Map<String, Object> result = assessmentService.calculateResult(recordId);
        return success(result);
    }

    /**
     * 生成测评报告
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:report')")
    @PostMapping("/record/{recordId}/report")
    public AjaxResult generateReport(
            @PathVariable("recordId") Long recordId,
            @RequestParam(defaultValue = "1") Integer reportLevel) {
        Map<String, Object> report = assessmentService.generateReport(recordId, reportLevel);
        return success(report);
    }

    /**
     * 获取测评建议
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/record/{recordId}/suggestions")
    public AjaxResult getSuggestions(@PathVariable("recordId") Long recordId) {
        Map<String, Object> suggestions = assessmentService.getAssessmentSuggestions(recordId);
        return success(suggestions);
    }

    /**
     * 验证测评权限
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:validate')")
    @GetMapping("/validate")
    public AjaxResult validatePermission(@RequestParam Long userId, @RequestParam Long scaleId) {
        Map<String, Object> result = assessmentService.validateAssessmentPermission(userId, scaleId);
        return success(result);
    }

    /**
     * 检查测评前置条件
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:validate')")
    @GetMapping("/check-prerequisites")
    public AjaxResult checkPrerequisites(@RequestParam Long userId, @RequestParam Long scaleId) {
        Map<String, Object> result = assessmentService.checkAssessmentPrerequisites(userId, scaleId);
        return success(result);
    }

    /**
     * 获取测评历史
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/history")
    public AjaxResult getHistory(@RequestParam Long userId, @RequestParam Long scaleId) {
        List<Map<String, Object>> history = assessmentService.getAssessmentHistory(userId, scaleId);
        return success(history);
    }

    /**
     * 比较测评结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @PostMapping("/compare")
    public AjaxResult compareResults(@RequestBody List<Long> recordIds) {
        Map<String, Object> comparison = assessmentService.compareAssessmentResults(recordIds);
        return success(comparison);
    }

    /**
     * 导出测评结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:export')")
    @GetMapping("/record/{recordId}/export")
    public AjaxResult exportResult(
            @PathVariable("recordId") Long recordId,
            @RequestParam(defaultValue = "pdf") String format) {
        Map<String, Object> data = assessmentService.exportAssessmentResult(recordId, format);
        return success(data);
    }

    /**
     * 分享测评结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:share')")
    @PostMapping("/record/{recordId}/share")
    public AjaxResult shareResult(
            @PathVariable("recordId") Long recordId,
            @RequestParam String shareType) {
        Map<String, Object> shareInfo = assessmentService.shareAssessmentResult(recordId, shareType);
        return success(shareInfo);
    }

    /**
     * 获取测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/statistics")
    public AjaxResult getStatistics(
            @RequestParam(required = false) Long scaleId,
            @RequestParam(defaultValue = "month") String timeRange) {
        Map<String, Object> statistics = assessmentService.getAssessmentStatistics(scaleId, timeRange);
        return success(statistics);
    }

    /**
     * 获取用户测评概览
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/user/{userId}/overview")
    public AjaxResult getUserOverview(@PathVariable("userId") Long userId) {
        Map<String, Object> overview = assessmentService.getUserAssessmentOverview(userId);
        return success(overview);
    }

    /**
     * 预览测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:preview')")
    @GetMapping("/scale/{scaleId}/preview")
    public AjaxResult previewAssessment(@PathVariable("scaleId") Long scaleId) {
        Map<String, Object> preview = assessmentService.previewAssessment(scaleId);
        return success(preview);
    }

    /**
     * 模拟测评
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:simulate')")
    @PostMapping("/scale/{scaleId}/simulate")
    public AjaxResult simulateAssessment(
            @PathVariable("scaleId") Long scaleId,
            @RequestBody Map<Long, String> answers) {
        Map<String, Object> result = assessmentService.simulateAssessment(scaleId, answers);
        return success(result);
    }

    /**
     * 标记题目
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:mark')")
    @PostMapping("/{sessionId}/mark")
    public AjaxResult markQuestion(
            @PathVariable("sessionId") String sessionId,
            @RequestParam Long questionId,
            @RequestParam Boolean marked) {
        Map<String, Object> result = assessmentService.markQuestion(sessionId, questionId, marked);
        return success(result);
    }

    /**
     * 获取已标记题目
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/marked")
    public AjaxResult getMarkedQuestions(@PathVariable("sessionId") String sessionId) {
        List<Map<String, Object>> marked = assessmentService.getMarkedQuestions(sessionId);
        return success(marked);
    }

    /**
     * 检查测评完整性
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/check-completeness")
    public AjaxResult checkCompleteness(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.checkAssessmentCompleteness(sessionId);
        return success(result);
    }

    /**
     * 获取测评摘要
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/{sessionId}/summary")
    public AjaxResult getSummary(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> summary = assessmentService.getAssessmentSummary(sessionId);
        return success(summary);
    }

    /**
     * 重新计算结果
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:calculate')")
    @PostMapping("/record/{recordId}/recalculate")
    public AjaxResult recalculateResult(@PathVariable("recordId") Long recordId) {
        Map<String, Object> result = assessmentService.recalculateResult(recordId);
        return success(result);
    }

    /**
     * 清理过期会话
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:clean')")
    @Log(title = "测评流程", businessType = BusinessType.CLEAN)
    @PostMapping("/clean-expired-sessions")
    public AjaxResult cleanExpiredSessions(@RequestParam(defaultValue = "24") Integer expireHours) {
        int count = assessmentService.cleanExpiredSessions(expireHours);
        return success("清理了 " + count + " 个过期会话");
    }
}
