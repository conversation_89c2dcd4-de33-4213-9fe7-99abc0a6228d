package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.PsyTScaleMapper;
import com.xihuan.system.service.IPsyTScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 量表基础信息Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTScaleServiceImpl implements IPsyTScaleService {
    
    @Autowired
    private PsyTScaleMapper scaleMapper;

    /**
     * 查询量表列表
     * 
     * @param scale 量表信息
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectScaleList(PsyTScale scale) {
        return scaleMapper.selectScaleList(scale);
    }

    /**
     * 根据ID查询量表
     * 
     * @param id 量表ID
     * @return 量表信息
     */
    @Override
    public PsyTScale selectScaleById(Long id) {
        return scaleMapper.selectScaleById(id);
    }

    /**
     * 查询量表详情（包含题目、分量表、计分规则等信息）
     * 
     * @param id 量表ID
     * @return 量表详情
     */
    @Override
    public PsyTScale selectScaleWithDetails(Long id) {
        return scaleMapper.selectScaleWithDetails(id);
    }

    /**
     * 根据编码查询量表
     * 
     * @param code 量表编码
     * @return 量表信息
     */
    @Override
    public PsyTScale selectScaleByCode(String code) {
        return scaleMapper.selectScaleByCode(code);
    }

    /**
     * 新增量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertScale(PsyTScale scale) {
        // 校验量表编码唯一性
        if (!checkScaleCodeUnique(scale.getCode(), null)) {
            throw new ServiceException("量表编码已存在");
        }
        
        // 设置默认值
        if (scale.getStatus() == null) {
            scale.setStatus(PsyTScale.STATUS_DISABLED);
        }
        if (scale.getPayMode() == null) {
            scale.setPayMode(PsyTScale.PAY_MODE_FREE);
        }
        if (scale.getPayPhase() == null) {
            scale.setPayPhase(PsyTScale.PAY_PHASE_TEST);
        }
        if (scale.getFreeReportLevel() == null) {
            scale.setFreeReportLevel(1);
        }
        if (scale.getPaidReportLevel() == null) {
            scale.setPaidReportLevel(3);
        }
        if (scale.getSort() == null) {
            scale.setSort(0);
        }
        if (scale.getSearchCount() == null) {
            scale.setSearchCount(0);
        }
        if (scale.getViewCount() == null) {
            scale.setViewCount(0);
        }
        if (StringUtils.isEmpty(scale.getDelFlag())) {
            scale.setDelFlag(PsyTScale.DEL_FLAG_NORMAL);
        }
        
        return scaleMapper.insertScale(scale);
    }

    /**
     * 修改量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateScale(PsyTScale scale) {
        // 校验量表编码唯一性
        if (StringUtils.isNotEmpty(scale.getCode()) && 
            !checkScaleCodeUnique(scale.getCode(), scale.getId())) {
            throw new ServiceException("量表编码已存在");
        }
        
        return scaleMapper.updateScale(scale);
    }

    /**
     * 删除量表
     * 
     * @param ids 需要删除的量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteScaleByIds(Long[] ids) {
        // 检查量表是否被使用
        for (Long id : ids) {
            if (checkScaleInUse(id)) {
                PsyTScale scale = selectScaleById(id);
                throw new ServiceException("量表【" + scale.getName() + "】正在使用中，不能删除");
            }
        }
        
        return scaleMapper.deleteScaleByIds(ids);
    }

    /**
     * 根据企业ID查询量表列表
     * 
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectScalesByEnterpriseId(Long enterpriseId) {
        return scaleMapper.selectScalesByEnterpriseId(enterpriseId);
    }

    /**
     * 查询用户已购买的量表列表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectPurchasedScalesByUserId(Long userId) {
        return scaleMapper.selectPurchasedScalesByUserId(userId);
    }

    /**
     * 查询热门量表列表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectHotScales(Integer limit) {
        return scaleMapper.selectHotScales(limit);
    }

    /**
     * 查询推荐量表列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectRecommendScales(Long userId, Integer limit) {
        return scaleMapper.selectRecommendScales(userId, limit);
    }

    /**
     * 查询免费量表列表
     * 
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectFreeScales() {
        return scaleMapper.selectFreeScales();
    }

    /**
     * 更新量表查看次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int updateViewCount(Long id) {
        return scaleMapper.updateViewCount(id);
    }

    /**
     * 更新量表搜索次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int updateSearchCount(Long id) {
        return scaleMapper.updateSearchCount(id);
    }

    /**
     * 检查量表编码唯一性
     * 
     * @param code 量表编码
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    @Override
    public boolean checkScaleCodeUnique(String code, Long excludeId) {
        Long scaleId = excludeId == null ? -1L : excludeId;
        int count = scaleMapper.checkScaleCodeUnique(code, scaleId);
        return count == 0;
    }

    /**
     * 检查量表是否被使用
     * 
     * @param id 量表ID
     * @return 是否被使用
     */
    @Override
    public boolean checkScaleInUse(Long id) {
        int count = scaleMapper.checkScaleInUse(id);
        return count > 0;
    }

    /**
     * 搜索量表
     * 
     * @param keyword 关键词
     * @param scoringType 计分类型
     * @param payMode 付费模式
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> searchScales(String keyword, String scoringType, Integer payMode, Long enterpriseId) {
        return scaleMapper.searchScales(keyword, scoringType, payMode, enterpriseId);
    }

    /**
     * 统计量表数量
     * 
     * @param scale 查询条件
     * @return 数量
     */
    @Override
    public int countScales(PsyTScale scale) {
        return scaleMapper.countScales(scale);
    }

    /**
     * 查询量表统计信息
     * 
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectScaleStats() {
        return scaleMapper.selectScaleStats();
    }

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserTestStats(Long userId) {
        return scaleMapper.selectUserTestStats(userId);
    }

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleTestStats(Long scaleId) {
        return scaleMapper.selectScaleTestStats(scaleId);
    }

    /**
     * 查询今日热门量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectTodayHotScales(Integer limit) {
        return scaleMapper.selectTodayHotScales(limit);
    }

    /**
     * 查询最新量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectLatestScales(Integer limit) {
        return scaleMapper.selectLatestScales(limit);
    }

//    /**
//     * 查询推荐量表
//     *
//     * @param userId 用户ID
//     * @param limit 限制数量
//     * @return 量表集合
//     */
//    @Override
//    public List<PsyTScale> selectRecommendScales(Long userId, Integer limit) {
//        return scaleMapper.selectRecommendScales(userId, limit);
//    }

    /**
     * 查询用户收藏的量表
     *
     * @param userId 用户ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectFavoriteScalesByUserId(Long userId) {
        return scaleMapper.selectFavoriteScalesByUserId(userId);
    }

    /**
     * 查询相似量表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectSimilarScales(Long scaleId, Integer limit) {
        return scaleMapper.selectSimilarScales(scaleId, limit);
    }

    /**
     * 查询企业可用量表
     * 
     * @param enterpriseId 企业ID
     * @return 量表集合
     */
    @Override
    public List<PsyTScale> selectAvailableScalesForEnterprise(Long enterpriseId) {
        return scaleMapper.selectAvailableScalesForEnterprise(enterpriseId);
    }

    /**
     * 查询量表使用统计
     * 
     * @param scaleId 量表ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleUsageStats(Long scaleId, String startDate, String endDate) {
        return scaleMapper.selectScaleUsageStats(scaleId, startDate, endDate);
    }

    /**
     * 查询量表趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectScaleTrendStats(Integer days) {
        return scaleMapper.selectScaleTrendStats(days);
    }

    /**
     * 批量更新量表状态
     * 
     * @param ids 量表ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateScaleStatus(Long[] ids, Integer status) {
        return scaleMapper.batchUpdateScaleStatus(ids, status);
    }

    /**
     * 查询量表分类统计
     * 
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectScaleCategoryStats() {
        return scaleMapper.selectScaleCategoryStats();
    }

    /**
     * 查询量表评分分布
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectScaleScoreDistribution(Long scaleId) {
        return scaleMapper.selectScaleScoreDistribution(scaleId);
    }

    // 以下方法需要在后续实现中添加具体逻辑
    @Override
    public int copyScale(Long sourceId, PsyTScale targetScale) {
        // TODO: 实现量表复制逻辑
        throw new ServiceException("功能暂未实现");
    }

    @Override
    public int publishScale(Long id) {
        // TODO: 实现量表发布逻辑
        throw new ServiceException("功能暂未实现");
    }

    @Override
    public int unpublishScale(Long id) {
        // TODO: 实现量表下架逻辑
        throw new ServiceException("功能暂未实现");
    }

    @Override
    public Map<String, Object> validateScaleCompleteness(Long id) {
        // TODO: 实现量表完整性验证逻辑
        throw new ServiceException("功能暂未实现");
    }

    @Override
    public int importScale(Map<String, Object> scaleData) {
        // TODO: 实现量表导入逻辑
        throw new ServiceException("功能暂未实现");
    }

    @Override
    public Map<String, Object> exportScale(Long id) {
        // TODO: 实现量表导出逻辑
        throw new ServiceException("功能暂未实现");
    }
}
