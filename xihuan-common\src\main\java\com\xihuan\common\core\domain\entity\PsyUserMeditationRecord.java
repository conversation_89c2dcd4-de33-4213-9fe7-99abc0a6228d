package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 用户冥想记录表对象 psy_user_meditation_record
 * 
 * <AUTHOR>
 */
@Data
public class PsyUserMeditationRecord {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @Excel(name = "记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    private Long userId;

    /** 冥想ID */
    @Excel(name = "冥想ID", cellType = Excel.ColumnType.NUMERIC)
    private Long meditationId;

    /** 播放时长（秒） */
    @Excel(name = "播放时长（秒）")
    private Integer durationPlayed;

    /** 是否完成 */
    @Excel(name = "是否完成", readConverterExp = "0=未完成,1=已完成")
    private Integer isCompleted;

    /** 冥想前心情（1-5分） */
    @Excel(name = "冥想前心情", readConverterExp = "1=很差,2=较差,3=一般,4=较好,5=很好")
    private Integer moodBefore;

    /** 冥想后心情（1-5分） */
    @Excel(name = "冥想后心情", readConverterExp = "1=很差,2=较差,3=一般,4=较好,5=很好")
    private Integer moodAfter;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /** 关联的用户信息 */
    private SysUser user;

    /** 关联的冥想信息 */
    private PsyMeditation meditation;
}
