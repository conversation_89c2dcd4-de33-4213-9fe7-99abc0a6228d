# 收藏功能API接口文档

## 📋 概述

本文档描述了优化后的收藏功能API接口，支持咨询师、课程、冥想、测评的收藏管理，以及收藏分组功能。

## 🔗 基础信息

- **基础路径**: `/miniapp/favorite`
- **认证方式**: Bearer Token
- **数据格式**: JSON

## 📊 收藏类型说明

| 类型值 | 类型名称 | 说明 |
|--------|----------|------|
| 1 | 咨询师 | 收藏心理咨询师 |
| 2 | 课程 | 收藏心理课程 |
| 3 | 冥想 | 收藏冥想内容 |
| 4 | 测评 | 收藏心理测评 |

## 🔧 核心收藏接口

### 1. 添加收藏

**接口地址**: `POST /miniapp/favorite/add`

**请求参数**:
```json
{
  "targetType": 1,
  "targetId": 123,
  "targetTitle": "张医生",
  "targetImage": "https://example.com/avatar.jpg",
  "tags": "专业,温和",
  "notes": "很专业的咨询师",
  "isPublic": 0
}
```

**参数说明**:
- `targetType` (必填): 收藏目标类型
- `targetId` (必填): 目标对象ID
- `targetTitle` (可选): 目标标题（系统会自动获取）
- `targetImage` (可选): 目标图片（系统会自动获取）
- `tags` (可选): 用户自定义标签
- `notes` (可选): 用户备注
- `isPublic` (可选): 是否公开收藏，默认0私有

**响应示例**:
```json
{
  "code": 200,
  "msg": "收藏成功",
  "data": null
}
```

### 2. 取消收藏

**接口地址**: `DELETE /miniapp/favorite/remove/{favoriteId}`

**路径参数**:
- `favoriteId`: 收藏ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "取消收藏成功",
  "data": null
}
```

### 3. 批量取消收藏

**接口地址**: `DELETE /miniapp/favorite/batchRemove`

**请求参数**:
```json
[1, 2, 3, 4, 5]
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "批量取消收藏成功",
  "data": null
}
```

### 4. 查询收藏列表

**接口地址**: `GET /miniapp/favorite/list`

**查询参数**:
- `targetType` (可选): 收藏类型筛选

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "favoriteId": 1,
      "userId": 123,
      "targetType": 1,
      "targetId": 456,
      "targetTitle": "张医生",
      "targetImage": "https://example.com/avatar.jpg",
      "tags": "专业,温和",
      "notes": "很专业的咨询师",
      "favoriteTime": "2024-01-01 10:00:00",
      "viewCount": 5,
      "targetTypeName": "咨询师",
      "actualTitle": "张医生",
      "actualImage": "https://example.com/avatar.jpg",
      "description": "专业心理咨询师",
      "price": "200.00",
      "targetStatus": 1
    }
  ]
}
```

### 5. 查询收藏详情

**接口地址**: `GET /miniapp/favorite/detail/{favoriteId}`

**路径参数**:
- `favoriteId`: 收藏ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "favoriteId": 1,
    "userId": 123,
    "targetType": 1,
    "targetId": 456,
    "targetTitle": "张医生",
    "targetImage": "https://example.com/avatar.jpg",
    "tags": "专业,温和",
    "notes": "很专业的咨询师",
    "favoriteTime": "2024-01-01 10:00:00",
    "lastViewTime": "2024-01-02 15:30:00",
    "viewCount": 6
  }
}
```

### 6. 检查是否已收藏

**接口地址**: `GET /miniapp/favorite/check`

**查询参数**:
- `targetType`: 目标类型
- `targetId`: 目标ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "data": true
}
```

### 7. 查询收藏统计

**接口地址**: `GET /miniapp/favorite/stats`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "totalCount": 25,
    "consultantCount": 8,
    "courseCount": 10,
    "meditationCount": 5,
    "assessmentCount": 2,
    "publicCount": 3,
    "totalViews": 150
  }
}
```

## 📁 收藏分组接口

### 1. 查询收藏分组列表

**接口地址**: `GET /miniapp/favorite/group/list`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "groupId": 1,
      "userId": 123,
      "groupName": "默认收藏夹",
      "groupIcon": "📁",
      "groupColor": "#007AFF",
      "description": "系统默认收藏分组",
      "favoriteCount": 15,
      "isDefault": 1,
      "isPublic": 0,
      "status": 1,
      "createTime": "2024-01-01 10:00:00"
    }
  ]
}
```

### 2. 创建收藏分组

**接口地址**: `POST /miniapp/favorite/group/create`

**请求参数**:
```json
{
  "groupName": "我的专业收藏",
  "groupIcon": "⭐",
  "groupColor": "#FF6B6B",
  "description": "收藏专业相关内容",
  "isPublic": 0
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "创建成功",
  "data": {
    "groupId": 2,
    "groupName": "我的专业收藏",
    "groupIcon": "⭐",
    "groupColor": "#FF6B6B",
    "description": "收藏专业相关内容",
    "favoriteCount": 0,
    "isDefault": 0,
    "isPublic": 0,
    "status": 1
  }
}
```

### 3. 更新收藏分组

**接口地址**: `PUT /miniapp/favorite/group/update`

**请求参数**:
```json
{
  "groupId": 2,
  "groupName": "专业学习收藏",
  "groupIcon": "📚",
  "groupColor": "#4ECDC4",
  "description": "专业学习相关收藏"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "更新成功",
  "data": null
}
```

### 4. 删除收藏分组

**接口地址**: `DELETE /miniapp/favorite/group/delete/{groupId}`

**路径参数**:
- `groupId`: 分组ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "删除成功",
  "data": null
}
```

### 5. 添加收藏到分组

**接口地址**: `POST /miniapp/favorite/group/addFavorite`

**查询参数**:
- `favoriteId`: 收藏ID
- `groupId`: 分组ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "添加成功",
  "data": null
}
```

### 6. 从分组中移除收藏

**接口地址**: `DELETE /miniapp/favorite/group/removeFavorite`

**查询参数**:
- `favoriteId`: 收藏ID
- `groupId`: 分组ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "移除成功",
  "data": null
}
```

### 7. 查询分组内收藏列表

**接口地址**: `GET /miniapp/favorite/group/favorites/{groupId}`

**路径参数**:
- `groupId`: 分组ID

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "favoriteId": 1,
      "targetType": 1,
      "targetId": 456,
      "targetTitle": "张医生",
      "targetImage": "https://example.com/avatar.jpg",
      "tags": "专业,温和",
      "notes": "很专业的咨询师",
      "favoriteTime": "2024-01-01 10:00:00",
      "viewCount": 5,
      "groupSort": 1,
      "targetTypeName": "咨询师"
    }
  ]
}
```

## 🔒 权限说明

1. **用户认证**: 所有接口都需要用户登录认证
2. **数据权限**: 用户只能操作自己的收藏和分组
3. **默认分组**: 系统会为每个用户自动创建默认收藏分组，不能删除
4. **公开收藏**: 用户可以设置收藏为公开，供其他用户查看

## 📝 使用示例

### 收藏咨询师示例
```javascript
// 1. 检查是否已收藏
const checkResponse = await fetch('/miniapp/favorite/check?targetType=1&targetId=123');
const isCollected = await checkResponse.json();

if (!isCollected.data) {
  // 2. 添加收藏
  const addResponse = await fetch('/miniapp/favorite/add', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer ' + token
    },
    body: JSON.stringify({
      targetType: 1,
      targetId: 123,
      tags: '专业,温和',
      notes: '很专业的咨询师'
    })
  });
}
```

### 分组管理示例
```javascript
// 1. 创建分组
const createGroupResponse = await fetch('/miniapp/favorite/group/create', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    groupName: '我的专业收藏',
    groupIcon: '⭐',
    groupColor: '#FF6B6B'
  })
});

const newGroup = await createGroupResponse.json();

// 2. 添加收藏到分组
await fetch('/miniapp/favorite/group/addFavorite?favoriteId=1&groupId=' + newGroup.data.groupId, {
  method: 'POST',
  headers: {
    'Authorization': 'Bearer ' + token
  }
});
```

## ⚠️ 注意事项

1. **重复收藏**: 系统会检查重复收藏，避免同一用户重复收藏同一内容
2. **数据同步**: 收藏时会自动获取目标对象的最新标题和图片信息
3. **统计更新**: 收藏和取消收藏会实时更新统计数据
4. **分组限制**: 默认分组不能删除，每个用户至少有一个分组
5. **性能优化**: 使用了冗余字段和索引优化查询性能

这个收藏功能提供了完整的收藏管理能力，支持多种内容类型的收藏，以及灵活的分组管理功能。
