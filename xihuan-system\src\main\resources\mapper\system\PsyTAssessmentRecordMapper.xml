<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTAssessmentRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="RecordResultMap" type="PsyTAssessmentRecord">
        <id property="id" column="id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="userId" column="user_id"/>
        <result property="sessionId" column="session_id"/>
        <result property="startTime" column="start_time"/>
        <result property="completionTime" column="completion_time"/>
        <result property="totalScore" column="total_score"/>
        <result property="resultLevel" column="result_level"/>
        <result property="resultDescription" column="result_description"/>
        <result property="currentQuestionNo" column="current_question_no"/>
        <result property="answeredCount" column="answered_count"/>
        <result property="totalQuestions" column="total_questions"/>
        <result property="progress" column="progress"/>
        <result property="duration" column="duration"/>
        <result property="status" column="status"/>
        <result property="source" column="source"/>
        <result property="reportLevel" column="report_level"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 详细结果映射 -->
    <resultMap id="RecordDetailMap" type="PsyTAssessmentRecord" extends="RecordResultMap">
        <association property="scale" javaType="PsyTScale">
            <id property="id" column="s_id"/>
            <result property="name" column="s_name"/>
            <result property="code" column="s_code"/>
            <result property="description" column="s_description"/>
            <result property="questionCount" column="s_question_count"/>
            <result property="timeLimit" column="s_time_limit"/>
        </association>
        <association property="user" javaType="SysUser">
            <id property="userId" column="u_user_id"/>
            <result property="userName" column="u_user_name"/>
            <result property="nickName" column="u_nick_name"/>
            <result property="phonenumber" column="u_phonenumber"/>
            <result property="email" column="u_email"/>
        </association>
        <collection property="answers" ofType="PsyTAnswerRecord" column="id" select="com.xihuan.system.mapper.PsyTAnswerRecordMapper.selectAnswersByRecordId"/>
    </resultMap>

    <!-- 查询测评记录列表 -->
    <select id="selectRecordList" parameterType="PsyTAssessmentRecord" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record
        WHERE del_flag = '0'
        <if test="scaleId != null">
            AND scale_id = #{scaleId}
        </if>
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="source != null and source != ''">
            AND source = #{source}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询测评记录 -->
    <select id="selectRecordById" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 根据会话ID查询测评记录 -->
    <select id="selectRecordBySessionId" parameterType="String" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record WHERE session_id = #{sessionId} AND del_flag = '0'
    </select>

    <!-- 查询测评记录详情 -->
    <select id="selectRecordWithDetails" parameterType="Long" resultMap="RecordDetailMap">
        SELECT r.*, 
               s.id as s_id, s.name as s_name, s.code as s_code, s.description as s_description,
               s.question_count as s_question_count, s.time_limit as s_time_limit,
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.phonenumber as u_phonenumber, u.email as u_email
        FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.id = #{id} AND r.del_flag = '0'
    </select>

    <!-- 根据用户ID查询测评记录 -->
    <select id="selectRecordsByUserId" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE user_id = #{userId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 根据量表ID查询测评记录 -->
    <select id="selectRecordsByScaleId" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE scale_id = #{scaleId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 查询用户在指定量表的测评记录 -->
    <select id="selectUserRecordsByScaleId" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE user_id = #{userId} AND scale_id = #{scaleId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <!-- 新增测评记录 -->
    <insert id="insertRecord" parameterType="PsyTAssessmentRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_assessment_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">scale_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="sessionId != null and sessionId != ''">session_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="completionTime != null">completion_time,</if>
            <if test="totalScore != null">total_score,</if>
            <if test="resultLevel != null">result_level,</if>
            <if test="resultDescription != null">result_description,</if>
            <if test="currentQuestionNo != null">current_question_no,</if>
            <if test="answeredCount != null">answered_count,</if>
            <if test="totalQuestions != null">total_questions,</if>
            <if test="progress != null">progress,</if>
            <if test="duration != null">duration,</if>
            <if test="status != null">status,</if>
            <if test="source != null">source,</if>
            <if test="reportLevel != null">report_level,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="scaleId != null">#{scaleId},</if>
            <if test="userId != null">#{userId},</if>
            <if test="sessionId != null and sessionId != ''">#{sessionId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="completionTime != null">#{completionTime},</if>
            <if test="totalScore != null">#{totalScore},</if>
            <if test="resultLevel != null">#{resultLevel},</if>
            <if test="resultDescription != null">#{resultDescription},</if>
            <if test="currentQuestionNo != null">#{currentQuestionNo},</if>
            <if test="answeredCount != null">#{answeredCount},</if>
            <if test="totalQuestions != null">#{totalQuestions},</if>
            <if test="progress != null">#{progress},</if>
            <if test="duration != null">#{duration},</if>
            <if test="status != null">#{status},</if>
            <if test="source != null">#{source},</if>
            <if test="reportLevel != null">#{reportLevel},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <!-- 修改测评记录 -->
    <update id="updateRecord" parameterType="PsyTAssessmentRecord">
        UPDATE psy_t_assessment_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="completionTime != null">completion_time = #{completionTime},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="resultLevel != null">result_level = #{resultLevel},</if>
            <if test="resultDescription != null">result_description = #{resultDescription},</if>
            <if test="currentQuestionNo != null">current_question_no = #{currentQuestionNo},</if>
            <if test="answeredCount != null">answered_count = #{answeredCount},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="status != null">status = #{status},</if>
            <if test="reportLevel != null">report_level = #{reportLevel},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除测评记录 -->
    <delete id="deleteRecordById" parameterType="Long">
        DELETE FROM psy_t_assessment_record WHERE id = #{id}
    </delete>

    <!-- 批量删除测评记录 -->
    <delete id="deleteRecordByIds" parameterType="String">
        DELETE FROM psy_t_assessment_record WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 查询进行中的测评记录 -->
    <select id="selectInProgressRecords" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE status = 1 AND del_flag = '0'
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 查询已完成的测评记录 -->
    <select id="selectCompletedRecords" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE status = 2 AND del_flag = '0'
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        ORDER BY completion_time DESC
    </select>

    <!-- 查询已放弃的测评记录 -->
    <select id="selectAbandonedRecords" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE status = 3 AND del_flag = '0'
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        ORDER BY update_time DESC
    </select>

    <!-- 统计用户测评记录数量 -->
    <select id="countRecordsByUserId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_assessment_record 
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <!-- 统计量表测评记录数量 -->
    <select id="countRecordsByScaleId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_assessment_record 
        WHERE scale_id = #{scaleId} AND del_flag = '0'
    </select>

    <!-- 搜索测评记录 -->
    <select id="searchRecords" resultMap="RecordResultMap">
        SELECT r.* FROM psy_t_assessment_record r
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.del_flag = '0'
        <if test="keyword != null and keyword != ''">
            AND (
                s.name LIKE CONCAT('%', #{keyword}, '%')
                OR u.user_name LIKE CONCAT('%', #{keyword}, '%')
                OR u.nick_name LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="userId != null">
            AND r.user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        <if test="status != null">
            AND r.status = #{status}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 更新测评记录状态 -->
    <update id="updateRecordStatus">
        UPDATE psy_t_assessment_record 
        SET status = #{status}, update_time = sysdate()
        WHERE id = #{id}
    </update>

    <!-- 批量更新测评记录状态 -->
    <update id="batchUpdateRecordStatus">
        UPDATE psy_t_assessment_record 
        SET status = #{status}, update_time = sysdate()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询超时未完成的测评记录 -->
    <select id="selectTimeoutRecords" parameterType="Integer" resultMap="RecordResultMap">
        SELECT * FROM psy_t_assessment_record 
        WHERE status = 1 AND del_flag = '0'
        AND TIMESTAMPDIFF(HOUR, start_time, NOW()) > #{timeoutHours}
        ORDER BY start_time ASC
    </select>

    <!-- 清理过期的测评记录 -->
    <delete id="cleanExpiredRecords" parameterType="Integer">
        DELETE FROM psy_t_assessment_record 
        WHERE status IN (1, 3) AND del_flag = '0'
        AND TIMESTAMPDIFF(DAY, create_time, NOW()) > #{expireDays}
    </delete>

</mapper>
