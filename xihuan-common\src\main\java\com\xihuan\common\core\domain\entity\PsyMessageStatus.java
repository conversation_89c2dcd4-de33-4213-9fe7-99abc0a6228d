package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

@Data
public class PsyMessageStatus {
    private static final long serialVersionUID = 1L;

    @Excel(name = "状态ID")
    private Long statusId;

    @Excel(name = "消息ID")
    private Long messageId;

    @Excel(name = "用户ID")
    private Long userId;

    @Excel(name = "是否已读", readConverterExp = "0=未读,1=已读")
    private String isRead;

    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;
}