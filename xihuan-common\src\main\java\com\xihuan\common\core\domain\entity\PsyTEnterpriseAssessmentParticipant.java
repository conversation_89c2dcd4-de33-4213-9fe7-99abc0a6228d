package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 企业测评参与记录表 psy_t_enterprise_assessment_participant
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTEnterpriseAssessmentParticipant extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 参与记录ID */
    @Excel(name = "参与记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 计划ID */
    @Excel(name = "计划ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "计划ID不能为空")
    private Long planId;

    /** 企业ID */
    @Excel(name = "企业ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "企业ID不能为空")
    private Long enterpriseId;

    /** 员工ID */
    @Excel(name = "员工ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "员工ID不能为空")
    private Long employeeId;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    private Long userId;

    /** 测评记录ID */
    @Excel(name = "测评记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long recordId;

    /** 邀请时间 */
    @Excel(name = "邀请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @NotNull(message = "邀请时间不能为空")
    private Date invitationTime;

    /** 开始时间 */
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 完成时间 */
    @Excel(name = "完成时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date completionTime;

    /** 总分 */
    @Excel(name = "总分")
    private BigDecimal totalScore;

    /** 结果等级 */
    @Excel(name = "结果等级")
    @Size(max = 50, message = "结果等级不能超过50个字符")
    private String resultLevel;

    /** 参与状态(0未开始 1进行中 2已完成 3已放弃) */
    @Excel(name = "参与状态", readConverterExp = "0=未开始,1=进行中,2=已完成,3=已放弃")
    private Integer participationStatus;

    /** 是否提醒过：0否,1是 */
    @Excel(name = "是否提醒过", readConverterExp = "0=否,1=是")
    private Integer isReminded;

    /** 提醒次数 */
    @Excel(name = "提醒次数")
    @Min(value = 0, message = "提醒次数不能小于0")
    private Integer reminderCount;

    /** 最后提醒时间 */
    @Excel(name = "最后提醒时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date lastReminderTime;

    /** 完成方式(1主动完成 2提醒后完成) */
    @Excel(name = "完成方式", readConverterExp = "1=主动完成,2=提醒后完成")
    private Integer completionSource;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 计划信息 */
    private PsyTEnterpriseAssessmentPlan plan;

    /** 企业信息 */
    private PsyTEnterprise enterprise;

    /** 用户信息 */
    private SysUser user;

    /** 测评记录 */
    private PsyTAssessmentRecord record;

    // 扩展字段
    /** 计划名称 */
    private String planName;

    /** 企业名称 */
    private String enterpriseName;

    /** 员工姓名 */
    private String employeeName;

    /** 员工工号 */
    private String employeeCode;

    /** 部门名称 */
    private String departmentName;

    /** 职位名称 */
    private String positionName;

    /** 联系电话 */
    private String phoneNumber;

    /** 邮箱地址 */
    private String email;

    /** 量表名称 */
    private String scaleName;

    /** 测评用时(秒) */
    private Integer duration;

    /** 完成进度 */
    private BigDecimal progress;

    /** 是否超时 */
    private Boolean overtime;

    /** 距离截止时间 */
    private Integer remainingHours;

    // 常量定义
    /** 参与状态：未开始 */
    public static final Integer STATUS_NOT_STARTED = 0;
    
    /** 参与状态：进行中 */
    public static final Integer STATUS_IN_PROGRESS = 1;
    
    /** 参与状态：已完成 */
    public static final Integer STATUS_COMPLETED = 2;
    
    /** 参与状态：已放弃 */
    public static final Integer STATUS_ABANDONED = 3;

    /** 完成方式：主动完成 */
    public static final Integer COMPLETION_SOURCE_ACTIVE = 1;
    
    /** 完成方式：提醒后完成 */
    public static final Integer COMPLETION_SOURCE_REMINDED = 2;

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取参与状态描述
     */
    public String getParticipationStatusDesc() {
        if (participationStatus == null) return "";
        switch (participationStatus) {
            case 0: return "未开始";
            case 1: return "进行中";
            case 2: return "已完成";
            case 3: return "已放弃";
            default: return "未知";
        }
    }

    /**
     * 获取完成方式描述
     */
    public String getCompletionSourceDesc() {
        if (completionSource == null) return "";
        switch (completionSource) {
            case 1: return "主动完成";
            case 2: return "提醒后完成";
            default: return "未知";
        }
    }

    /**
     * 计算测评用时
     */
    public Integer calculateDuration() {
        if (startTime == null) return 0;
        Date endTime = completionTime != null ? completionTime : new Date();
        return (int) ((endTime.getTime() - startTime.getTime()) / 1000);
    }

    /**
     * 计算距离邀请时间的天数
     */
    public Integer getDaysFromInvitation() {
        if (invitationTime == null) return 0;
        Date now = new Date();
        return (int) ((now.getTime() - invitationTime.getTime()) / (1000 * 60 * 60 * 24));
    }

    /**
     * 是否未开始
     */
    public boolean isNotStarted() {
        return STATUS_NOT_STARTED.equals(participationStatus);
    }

    /**
     * 是否进行中
     */
    public boolean isInProgress() {
        return STATUS_IN_PROGRESS.equals(participationStatus);
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(participationStatus);
    }

    /**
     * 是否已放弃
     */
    public boolean isAbandoned() {
        return STATUS_ABANDONED.equals(participationStatus);
    }

    /**
     * 是否主动完成
     */
    public boolean isActiveCompletion() {
        return COMPLETION_SOURCE_ACTIVE.equals(completionSource);
    }

    /**
     * 是否提醒后完成
     */
    public boolean isRemindedCompletion() {
        return COMPLETION_SOURCE_REMINDED.equals(completionSource);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 是否需要提醒
     */
    public boolean needsReminder() {
        if (isCompleted() || isAbandoned()) return false;
        
        // 如果从未提醒过，需要提醒
        if (lastReminderTime == null) return true;
        
        // 如果距离上次提醒超过24小时，需要提醒
        Date now = new Date();
        long hoursSinceLastReminder = (now.getTime() - lastReminderTime.getTime()) / (1000 * 60 * 60);
        return hoursSinceLastReminder >= 24;
    }

    /**
     * 是否超时未完成
     */
    public boolean isOvertime() {
        if (isCompleted()) return false;
        
        // 如果计划已结束但未完成，则为超时
        if (plan != null && plan.getEndTime() != null) {
            return new Date().after(plan.getEndTime());
        }
        
        return false;
    }

    /**
     * 获取参与状态样式类
     */
    public String getStatusClass() {
        if (participationStatus == null) return "";
        switch (participationStatus) {
            case 0: return "text-warning";
            case 1: return "text-info";
            case 2: return "text-success";
            case 3: return "text-danger";
            default: return "";
        }
    }
}
