package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序课程Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/course")
public class MiniAppCourseController extends BaseController {
    
    @Autowired
    private IPsyCourseService courseService;
    
    @Autowired
    private IPsyCourseChapterService chapterService;
    
    @Autowired
    private IPsyCourseOrderService orderService;
    
    @Autowired
    private IPsyCourseReviewService reviewService;
    
    @Autowired
    private IPsyUserCourseProgressService progressService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取课程列表
     */
    @GetMapping("/list")
    public AjaxResult list(PsyCourse course) {
        // 小程序端只显示已发布的课程
        course.setStatus(1);
        List<PsyCourse> list = courseService.selectCourseList(course);
        return success(list);
    }

    /**
     * 获取课程详情
     */
    @GetMapping("/{id}")
    public AjaxResult getInfo(HttpServletRequest request, @PathVariable("id") Long id) {
        // 获取课程详情
        PsyCourse course = courseService.selectCourseWithDetails(id);
        if (course == null || course.getStatus() != 1) {
            return error("课程不存在或已下架");
        }
        
        // 增加课程观看次数
        courseService.incrementViewCount(id);
        
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser != null) {
            Long userId = loginUser.getUserId();
            
            // 检查用户是否已购买
            boolean purchased = orderService.checkUserPurchased(userId, id);
            course.getParams().put("purchased", purchased);
            
            // 获取用户学习进度
            if (purchased) {
                BigDecimal progress = progressService.calculateCourseProgress(userId, id);
                course.getParams().put("progress", progress);
            }
        }
        
        return success(course);
    }

    /**
     * 获取课程章节列表
     */
    @GetMapping("/chapters/{courseId}")
    public AjaxResult getChapters(@PathVariable("courseId") Long courseId) {
        List<PsyCourseChapter> chapters = chapterService.selectChapterTreeByCourseId(courseId);
        return success(chapters);
    }

    /**
     * 获取章节详情
     */
    @GetMapping("/chapter/{id}")
    public AjaxResult getChapterInfo(HttpServletRequest request,@PathVariable("id") Long id) {
        PsyCourseChapter chapter = chapterService.selectChapterById(id);
        if (chapter == null) {
            return error("章节不存在");
        }
        
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser != null) {
            Long userId = loginUser.getUserId();
            
            // 检查用户是否已购买课程
            boolean purchased = orderService.checkUserPurchased(userId, chapter.getCourseId());
            
            // 如果未购买且不是试听章节，则不返回内容
            if (!purchased && (chapter.getIsTrial() == null || chapter.getIsTrial() != 1)) {
                chapter.setChapterContent(null);
                chapter.getParams().put("needPurchase", true);
            } else {
                // 获取用户章节学习进度
                PsyUserCourseProgress progress = progressService.selectProgressByUserAndChapter(userId, id);
                if (progress != null) {
                    chapter.getParams().put("progress", progress);
                }
            }
        } else {
            // 未登录用户只能查看试听章节
            if (chapter.getIsTrial() == null || chapter.getIsTrial() != 1) {
                chapter.setChapterContent(null);
                chapter.getParams().put("needLogin", true);
            }
        }
        
        return success(chapter);
    }

    /**
     * 更新学习进度
     */
    @PostMapping("/progress")
    public AjaxResult updateProgress(HttpServletRequest request,@RequestBody PsyUserCourseProgress progress) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        progress.setUserId(loginUser.getUserId());
        progress.setUpdateTime(DateUtils.getNowDate());
        
        // 检查用户是否已购买课程
        boolean purchased = orderService.checkUserPurchased(progress.getUserId(), progress.getCourseId());
        if (!purchased) {
            // 检查是否是试听章节
            PsyCourseChapter chapter = chapterService.selectChapterById(progress.getChapterId());
            if (chapter == null || chapter.getIsTrial() != 1) {
                return error("请先购买课程");
            }
        }
        
        return toAjax(progressService.saveProgress(progress));
    }

    /**
     * 创建课程订单
     */
    @PostMapping("/order/{courseId}")
    public AjaxResult createOrder(HttpServletRequest request,@PathVariable("courseId") Long courseId) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        // 检查用户是否已购买
        boolean purchased = orderService.checkUserPurchased(loginUser.getUserId(), courseId);
        if (purchased) {
            return error("您已购买该课程");
        }
        
        // 创建订单
        PsyCourseOrder order = new PsyCourseOrder();
        order.setCourseId(courseId);
        order.setUserId(loginUser.getUserId());
        order.setStatus(0); // 待支付
        order.setCreateBy(loginUser.getUsername());
        
        // 保存订单
        int result = orderService.insertOrder(order);
        if (result > 0) {
            Map<String, Object> data = new HashMap<>();
            data.put("orderNo", order.getOrderNo());
            data.put("orderId", order.getId());
            return success(data);
        } else {
            return error("创建订单失败");
        }
    }

    /**
     * 提交课程评价
     */
    @PostMapping("/review")
    public AjaxResult submitReview(HttpServletRequest request,@RequestBody PsyCourseReview review) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        // 检查用户是否已购买
        boolean purchased = orderService.checkUserPurchased(loginUser.getUserId(), review.getCourseId());
        if (!purchased) {
            return error("您尚未购买该课程，无法评价");
        }
        
        // 检查用户是否已评价
        boolean reviewed = reviewService.checkUserReviewed(loginUser.getUserId(), review.getCourseId());
        if (reviewed) {
            return error("您已评价过该课程");
        }
        
        // 设置评价信息
        review.setUserId(loginUser.getUserId());
        review.setCreateBy(loginUser.getUsername());
        review.setCreateTime(DateUtils.getNowDate());
        review.setDelFlag(0);
        
        // 保存评价
        int result = reviewService.insertReview(review);
        if (result > 0) {
            // 更新课程评分信息
            courseService.updateCourseRating(review.getCourseId());
            return success();
        } else {
            return error("提交评价失败");
        }
    }

    /**
     * 获取课程评价列表
     */
    @GetMapping("/reviews/{courseId}")
    public AjaxResult getReviews(@PathVariable("courseId") Long courseId) {
        List<PsyCourseReview> reviews = reviewService.selectReviewsByCourseId(courseId);
        return success(reviews);
    }

    /**
     * 获取用户已购课程列表
     */
    @GetMapping("/purchased")
    public AjaxResult getPurchasedCourses(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        List<PsyCourse> courses = orderService.selectUserPurchasedCourses(loginUser.getUserId());
        return success(courses);
    }

    /**
     * 获取用户学习进度列表
     */
    @GetMapping("/progress/{courseId}")
    public AjaxResult getUserProgress(HttpServletRequest request,@PathVariable("courseId") Long courseId) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        List<PsyUserCourseProgress> progressList = progressService.selectProgressByUserAndCourse(loginUser.getUserId(), courseId);
        return success(progressList);
    }

    /**
     * 根据分类ID查询课程列表
     */
    @GetMapping("/category/{categoryId}")
    public AjaxResult getCoursesByCategory(@PathVariable Long categoryId) {
        List<PsyCourse> list = courseService.selectCoursesByCategoryId(categoryId);
        return success(list);
    }
}
