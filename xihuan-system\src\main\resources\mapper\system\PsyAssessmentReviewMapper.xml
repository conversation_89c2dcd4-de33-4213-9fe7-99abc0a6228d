<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentReviewMapper">

    <!-- 结果映射 -->
    <resultMap id="ReviewResultMap" type="PsyAssessmentReview">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="recordId" column="record_id"/>
        <result property="rating" column="rating"/>
        <result property="content" column="content"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="status" column="status"/>
        <result property="auditBy" column="audit_by"/>
        <result property="auditTime" column="audit_time"/>
        <result property="auditRemark" column="audit_remark"/>
        <result property="likeCount" column="like_count"/>
        <result property="replyCount" column="reply_count"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 带详情的结果映射 -->
    <resultMap id="ReviewWithDetailsMap" type="PsyAssessmentReview" extends="ReviewResultMap">
        <association property="user" javaType="SysUser">
            <id property="userId" column="u_user_id"/>
            <result property="userName" column="u_user_name"/>
            <result property="nickName" column="u_nick_name"/>
            <result property="email" column="u_email"/>
            <result property="phonenumber" column="u_phonenumber"/>
            <result property="avatar" column="u_avatar"/>
        </association>
        <association property="scale" javaType="PsyAssessmentScale">
            <id property="id" column="s_id"/>
            <result property="scaleName" column="s_scale_name"/>
            <result property="scaleCode" column="s_scale_code"/>
        </association>
        <association property="record" javaType="PsyAssessmentRecord">
            <id property="id" column="r_id"/>
            <result property="sessionId" column="r_session_id"/>
            <result property="totalScore" column="r_total_score"/>
            <result property="percentage" column="r_percentage"/>
        </association>
    </resultMap>

    <!-- 查询评价列表 -->
    <select id="selectReviewList" parameterType="PsyAssessmentReview" resultMap="ReviewWithDetailsMap">
        SELECT rv.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.email as u_email, u.phonenumber as u_phonenumber, u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name, s.scale_code as s_scale_code,
               r.id as r_id, r.session_id as r_session_id, r.total_score as r_total_score, r.percentage as r_percentage
        FROM psy_t_review rv
        LEFT JOIN sys_user u ON rv.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON rv.scale_id = s.id
        LEFT JOIN psy_t_record r ON rv.record_id = r.id
        WHERE rv.del_flag = 0
        <if test="userId != null">
            AND rv.user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND rv.scale_id = #{scaleId}
        </if>
        <if test="recordId != null">
            AND rv.record_id = #{recordId}
        </if>
        <if test="status != null">
            AND rv.status = #{status}
        </if>
        <if test="isAnonymous != null">
            AND rv.is_anonymous = #{isAnonymous}
        </if>
        <if test="rating != null">
            AND rv.rating = #{rating}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            AND rv.create_time >= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND rv.create_time &lt;= #{params.endTime}
        </if>
        ORDER BY rv.create_time DESC
    </select>

    <!-- 根据ID查询评价 -->
    <select id="selectReviewById" parameterType="Long" resultMap="ReviewResultMap">
        SELECT * FROM psy_t_review WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询评价详情 -->
    <select id="selectReviewWithDetails" parameterType="Long" resultMap="ReviewWithDetailsMap">
        SELECT rv.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.email as u_email, u.phonenumber as u_phonenumber, u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name, s.scale_code as s_scale_code,
               r.id as r_id, r.session_id as r_session_id, r.total_score as r_total_score, r.percentage as r_percentage
        FROM psy_t_review rv
        LEFT JOIN sys_user u ON rv.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON rv.scale_id = s.id
        LEFT JOIN psy_t_record r ON rv.record_id = r.id
        WHERE rv.id = #{id} AND rv.del_flag = 0
    </select>

    <!-- 根据用户ID查询评价列表 -->
    <select id="selectReviewsByUserId" parameterType="Long" resultMap="ReviewResultMap">
        SELECT * FROM psy_t_review 
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据量表ID查询评价列表 -->
    <select id="selectReviewsByScaleId" parameterType="Long" resultMap="ReviewResultMap">
        SELECT * FROM psy_t_review 
        WHERE scale_id = #{scaleId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 新增评价 -->
    <insert id="insertReview" parameterType="PsyAssessmentReview" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_review (
            user_id, scale_id, record_id, rating, content, is_anonymous, status,
            audit_by, audit_time, audit_remark, like_count, reply_count,
            del_flag, create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{userId}, #{scaleId}, #{recordId}, #{rating}, #{content}, #{isAnonymous}, #{status},
            #{auditBy}, #{auditTime}, #{auditRemark}, #{likeCount}, #{replyCount},
            #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate(), #{remark}
        )
    </insert>

    <!-- 修改评价 -->
    <update id="updateReview" parameterType="PsyAssessmentReview">
        UPDATE psy_t_review
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="rating != null">rating = #{rating},</if>
            <if test="content != null">content = #{content},</if>
            <if test="isAnonymous != null">is_anonymous = #{isAnonymous},</if>
            <if test="status != null">status = #{status},</if>
            <if test="auditBy != null">audit_by = #{auditBy},</if>
            <if test="auditTime != null">audit_time = #{auditTime},</if>
            <if test="auditRemark != null">audit_remark = #{auditRemark},</if>
            <if test="likeCount != null">like_count = #{likeCount},</if>
            <if test="replyCount != null">reply_count = #{replyCount},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除评价 -->
    <update id="deleteReviewById" parameterType="Long">
        UPDATE psy_t_review SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除评价 -->
    <update id="deleteReviewByIds" parameterType="Long">
        UPDATE psy_t_review SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 审核评价 -->
    <update id="auditReview">
        UPDATE psy_t_review 
        SET status = #{status}, audit_by = #{auditBy}, audit_time = sysdate(), audit_remark = #{auditRemark}, update_time = sysdate()
        WHERE id = #{id}
    </update>

    <!-- 更新点赞数 -->
    <update id="updateLikeCount">
        UPDATE psy_t_review 
        SET like_count = like_count + #{increment}, update_time = sysdate()
        WHERE id = #{id}
    </update>

    <!-- 查询用户是否已点赞 -->
    <select id="checkUserLiked" resultType="int">
        SELECT COUNT(1) FROM psy_review_like
        WHERE review_id = #{reviewId} AND user_id = #{userId}
    </select>

    <!-- 查询量表的评价统计 -->
    <select id="selectReviewStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            AVG(rating) as avg_rating,
            COUNT(CASE WHEN rating = 5 THEN 1 END) as rating5_count,
            COUNT(CASE WHEN rating = 4 THEN 1 END) as rating4_count,
            COUNT(CASE WHEN rating = 3 THEN 1 END) as rating3_count,
            COUNT(CASE WHEN rating = 2 THEN 1 END) as rating2_count,
            COUNT(CASE WHEN rating = 1 THEN 1 END) as rating1_count
        FROM psy_t_review
        WHERE scale_id = #{scaleId} AND status = 1 AND del_flag = 0
    </select>

    <!-- 查询用户的评价统计 -->
    <select id="selectUserReviewStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            AVG(rating) as avg_rating,
            SUM(like_count) as total_likes
        FROM psy_t_review
        WHERE user_id = #{userId} AND del_flag = 0
    </select>

    <!-- 查询待审核评价数量 -->
    <select id="countPendingReviews" resultType="int">
        SELECT COUNT(1) FROM psy_t_review WHERE status = 0 AND del_flag = 0
    </select>

    <!-- 查询最新评价列表 -->
    <select id="selectLatestReviews" resultMap="ReviewWithDetailsMap">
        SELECT rv.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name
        FROM psy_t_review rv
        LEFT JOIN sys_user u ON rv.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON rv.scale_id = s.id
        WHERE rv.status = 1 AND rv.del_flag = 0
        ORDER BY rv.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 查询热门评价列表 -->
    <select id="selectHotReviews" resultMap="ReviewWithDetailsMap">
        SELECT rv.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name
        FROM psy_t_review rv
        LEFT JOIN sys_user u ON rv.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON rv.scale_id = s.id
        WHERE rv.status = 1 AND rv.del_flag = 0
        ORDER BY rv.like_count DESC, rv.create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 统计评价数量 -->
    <select id="countReviews" parameterType="PsyAssessmentReview" resultType="int">
        SELECT COUNT(1) FROM psy_t_review
        WHERE del_flag = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND scale_id = #{scaleId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>
</mapper>
