<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeScheduleTemplateMapper">
    
    <resultMap type="PsyTimeScheduleTemplate" id="PsyTimeScheduleTemplateResult">
        <result property="id"               column="id"               />
        <result property="counselorId"      column="counselor_id"     />
        <result property="name"             column="name"             />
        <result property="isDefault"        column="is_default"       />
        <result property="effectiveStart"   column="effective_start"  />
        <result property="effectiveEnd"     column="effective_end"    />
        <result property="delFlag"          column="del_flag"         />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
    </resultMap>

    <sql id="selectPsyTimeScheduleTemplateVo">
        select id, counselor_id, name, is_default, effective_start, effective_end, 
               del_flag, create_by, create_time, update_by, update_time, remark
        from psy_time_schedule_template
    </sql>

    <select id="selectTemplateList" parameterType="PsyTimeScheduleTemplate" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        <where>  
            del_flag = 0
            <if test="counselorId != null"> and counselor_id = #{counselorId}</if>
            <if test="name != null and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="isDefault != null"> and is_default = #{isDefault}</if>
        </where>
        order by counselor_id, is_default desc, create_time desc
    </select>
    
    <select id="selectTemplateById" parameterType="Long" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where id = #{id}
    </select>
    
    <select id="selectTemplatesByCounselorId" parameterType="Long" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where counselor_id = #{counselorId} and del_flag = 0
        order by is_default desc, create_time desc
    </select>
    
    <select id="selectDefaultTemplateByCounselorId" parameterType="Long" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where counselor_id = #{counselorId} and is_default = 1 and del_flag = 0
        limit 1
    </select>
    
    <select id="selectEffectiveTemplate" resultMap="PsyTimeScheduleTemplateResult">
        <include refid="selectPsyTimeScheduleTemplateVo"/>
        where counselor_id = #{counselorId} and del_flag = 0
        and (effective_start is null or effective_start &lt;= #{date})
        and (effective_end is null or effective_end &gt;= #{date})
        order by is_default desc, create_time desc
        limit 1
    </select>
        
    <insert id="insertTemplate" parameterType="PsyTimeScheduleTemplate" useGeneratedKeys="true" keyProperty="id">
        insert into psy_time_schedule_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="counselorId != null">counselor_id,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="effectiveStart != null">effective_start,</if>
            <if test="effectiveEnd != null">effective_end,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="counselorId != null">#{counselorId},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="effectiveStart != null">#{effectiveStart},</if>
            <if test="effectiveEnd != null">#{effectiveEnd},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateTemplate" parameterType="PsyTimeScheduleTemplate">
        update psy_time_schedule_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="counselorId != null">counselor_id = #{counselorId},</if>
            <if test="name != null and name != ''">name = #{name},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="effectiveStart != null">effective_start = #{effectiveStart},</if>
            <if test="effectiveEnd != null">effective_end = #{effectiveEnd},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="setDefaultTemplate">
        update psy_time_schedule_template 
        set is_default = case when id = #{templateId} then 1 else 0 end,
            update_time = now()
        where counselor_id = #{counselorId} and del_flag = 0
    </update>

    <update id="clearDefaultTemplates">
        update psy_time_schedule_template 
        set is_default = 0, update_time = now()
        where counselor_id = #{counselorId} and del_flag = 0
    </update>

    <delete id="deleteTemplateById" parameterType="Long">
        update psy_time_schedule_template set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteTemplateByIds" parameterType="String">
        update psy_time_schedule_template set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="checkTemplateNameUnique" resultType="int">
        select count(1) from psy_time_schedule_template 
        where counselor_id = #{counselorId} and name = #{name} and del_flag = 0
        <if test="id != null">and id != #{id}</if>
    </select>
</mapper>
