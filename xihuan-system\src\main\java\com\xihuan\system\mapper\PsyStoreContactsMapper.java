package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.store.PsyStoreContacts;

import java.util.List;

/**
 * 门店联系方式Mapper接口
 */
public interface PsyStoreContactsMapper {
    /**
     * 查询门店联系方式列表
     * 
     * @param psyStoreContacts 门店联系方式信息
     * @return 门店联系方式集合
     */
    List<PsyStoreContacts> selectPsyStoreContactsList(PsyStoreContacts psyStoreContacts);

    /**
     * 根据门店ID查询联系方式列表
     * 
     * @param storeId 门店ID
     * @return 门店联系方式集合
     */
    List<PsyStoreContacts> selectPsyStoreContactsByStoreId(Long storeId);

    /**
     * 查询门店联系方式信息
     * 
     * @param id 联系方式主键
     * @return 门店联系方式信息
     */
    PsyStoreContacts selectPsyStoreContactsById(Long id);

    /**
     * 新增门店联系方式
     * 
     * @param psyStoreContacts 门店联系方式信息
     * @return 结果
     */
    int insertPsyStoreContacts(PsyStoreContacts psyStoreContacts);

    /**
     * 修改门店联系方式
     * 
     * @param psyStoreContacts 门店联系方式信息
     * @return 结果
     */
    int updatePsyStoreContacts(PsyStoreContacts psyStoreContacts);

    /**
     * 删除门店联系方式
     * 
     * @param id 联系方式主键
     * @return 结果
     */
    int deletePsyStoreContactsById(Long id);

    /**
     * 批量删除门店联系方式
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePsyStoreContactsByIds(Long[] ids);

    /**
     * 根据门店ID删除联系方式
     * 
     * @param storeId 门店ID
     * @return 结果
     */
    int deletePsyStoreContactsByStoreId(Long storeId);
} 