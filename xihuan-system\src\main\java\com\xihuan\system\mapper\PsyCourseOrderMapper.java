package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyCourseOrder;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 课程订单表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyCourseOrderMapper {
    
    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    List<PsyCourseOrder> selectOrderList(PsyCourseOrder order);

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    PsyCourseOrder selectOrderById(Long id);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    PsyCourseOrder selectOrderByOrderNo(String orderNo);

    /**
     * 查询订单详情（包含课程和用户信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyCourseOrder selectOrderWithDetails(Long id);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyCourseOrder> selectOrdersByUserId(Long userId);

    /**
     * 根据课程ID查询订单列表
     * 
     * @param courseId 课程ID
     * @return 订单集合
     */
    List<PsyCourseOrder> selectOrdersByCourseId(Long courseId);

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int insertOrder(PsyCourseOrder order);

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int updateOrder(PsyCourseOrder order);

    /**
     * 删除订单
     * 
     * @param id 订单ID
     * @return 结果
     */
    int deleteOrderById(Long id);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param status 订单状态
     * @param paymentMethod 支付方式
     * @param transactionId 第三方交易号
     * @param paymentTime 支付时间
     * @return 结果
     */
    int updateOrderPaymentStatus(@Param("orderNo") String orderNo,
                                @Param("status") Integer status,
                                @Param("paymentMethod") String paymentMethod,
                                @Param("transactionId") String transactionId,
                                @Param("paymentTime") Date paymentTime);

    /**
     * 更新订单退款信息
     * 
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param refundTime 退款时间
     * @return 结果
     */
    int updateOrderRefund(@Param("orderNo") String orderNo,
                         @Param("refundAmount") BigDecimal refundAmount,
                         @Param("refundTime") Date refundTime);

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 检查用户是否已购买课程
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 已购买的订单数量
     */
    int checkUserPurchased(@Param("userId") Long userId, @Param("courseId") Long courseId);
}
