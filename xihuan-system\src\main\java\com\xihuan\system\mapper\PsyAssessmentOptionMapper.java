package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentOption;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 心理测评题目选项Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentOptionMapper {
    
    /**
     * 查询选项列表
     * 
     * @param option 选项信息
     * @return 选项集合
     */
    List<PsyAssessmentOption> selectOptionList(PsyAssessmentOption option);

    /**
     * 根据ID查询选项
     * 
     * @param id 选项ID
     * @return 选项信息
     */
    PsyAssessmentOption selectOptionById(Long id);

    /**
     * 根据题目ID查询选项列表
     * 
     * @param questionId 题目ID
     * @return 选项集合
     */
    List<PsyAssessmentOption> selectOptionsByQuestionId(Long questionId);

    /**
     * 新增选项
     * 
     * @param option 选项信息
     * @return 结果
     */
    int insertOption(PsyAssessmentOption option);

    /**
     * 修改选项
     * 
     * @param option 选项信息
     * @return 结果
     */
    int updateOption(PsyAssessmentOption option);

    /**
     * 删除选项
     * 
     * @param id 选项ID
     * @return 结果
     */
    int deleteOptionById(Long id);

    /**
     * 批量删除选项
     * 
     * @param ids 需要删除的选项ID
     * @return 结果
     */
    int deleteOptionByIds(Long[] ids);

    /**
     * 根据题目ID删除选项
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    int deleteOptionsByQuestionId(Long questionId);

    /**
     * 批量插入选项
     * 
     * @param options 选项列表
     * @return 结果
     */
    int batchInsertOptions(List<PsyAssessmentOption> options);

    /**
     * 更新选项顺序
     * 
     * @param id 选项ID
     * @param orderNum 显示顺序
     * @return 结果
     */
    int updateOptionOrder(@Param("id") Long id, @Param("orderNum") Integer orderNum);

    /**
     * 检查选项是否被使用
     * 
     * @param id 选项ID
     * @return 数量
     */
    int checkOptionInUse(Long id);

    /**
     * 查询选项统计信息
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectOptionStats(Long questionId);
}
