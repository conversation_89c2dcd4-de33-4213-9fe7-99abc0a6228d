package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyCourse;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 心理咨询课程主表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyCourseMapper {
    
    /**
     * 查询课程列表
     * 
     * @param course 课程信息
     * @return 课程集合
     */
    List<PsyCourse> selectCourseList(PsyCourse course);

    /**
     * 查询课程详情（包含章节、讲师、分类等信息）
     * 
     * @param id 课程ID
     * @return 课程详情
     */
    PsyCourse selectCourseWithDetails(Long id);

    /**
     * 根据ID查询课程
     * 
     * @param id 课程ID
     * @return 课程信息
     */
    PsyCourse selectCourseById(Long id);

    /**
     * 新增课程
     * 
     * @param course 课程信息
     * @return 结果
     */
    int insertCourse(PsyCourse course);

    /**
     * 修改课程
     * 
     * @param course 课程信息
     * @return 结果
     */
    int updateCourse(PsyCourse course);

    /**
     * 删除课程
     * 
     * @param id 课程ID
     * @return 结果
     */
    int deleteCourseById(Long id);

    /**
     * 批量删除课程
     * 
     * @param ids 需要删除的课程ID
     * @return 结果
     */
    int deleteCourseByIds(Long[] ids);

    /**
     * 根据分类ID查询课程列表
     * 
     * @param categoryId 分类ID
     * @return 课程集合
     */
    List<PsyCourse> selectCoursesByCategoryId(Long categoryId);

    /**
     * 根据讲师ID查询课程列表
     * 
     * @param instructorId 讲师ID
     * @return 课程集合
     */
    List<PsyCourse> selectCoursesByInstructorId(Long instructorId);

    /**
     * 批量插入课程分类关联
     * 
     * @param courseId 课程ID
     * @param categoryIds 分类ID列表
     * @return 影响行数
     */
    int batchInsertCourseCategories(@Param("courseId") Long courseId, @Param("categoryIds") List<Long> categoryIds);

    /**
     * 删除课程的分类关联
     * 
     * @param courseId 课程ID
     * @return 影响行数
     */
    int deleteCourseCategories(Long courseId);

    /**
     * 批量删除课程的分类关联
     * 
     * @param courseIds 课程ID数组
     * @return 影响行数
     */
    int deleteCourseCategoriesByIds(Long[] courseIds);

    /**
     * 更新课程统计信息
     * 
     * @param courseId 课程ID
     * @param chapterCount 章节数量
     * @param trialChapterCount 试听章节数量
     * @param durationTotal 总时长
     * @return 影响行数
     */
    int updateCourseStatistics(@Param("courseId") Long courseId, 
                              @Param("chapterCount") Integer chapterCount,
                              @Param("trialChapterCount") Integer trialChapterCount,
                              @Param("durationTotal") Integer durationTotal);

    /**
     * 更新课程评分信息
     * 
     * @param courseId 课程ID
     * @param ratingAvg 平均评分
     * @param ratingCount 评分人数
     * @return 影响行数
     */
    int updateCourseRating(@Param("courseId") Long courseId, 
                          @Param("ratingAvg") java.math.BigDecimal ratingAvg,
                          @Param("ratingCount") Integer ratingCount);

    /**
     * 增加课程观看次数
     * 
     * @param courseId 课程ID
     * @return 影响行数
     */
    int incrementViewCount(Long courseId);

    /**
     * 增加课程销售数量
     * 
     * @param courseId 课程ID
     * @param count 增加数量
     * @return 影响行数
     */
    int incrementSalesCount(@Param("courseId") Long courseId, @Param("count") Integer count);
}
