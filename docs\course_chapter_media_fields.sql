-- 为课程章节表添加媒体文件字段
-- 执行时间：2024-12-19

-- 添加媒体文件相关字段
ALTER TABLE `psy_course_chapter` 
ADD COLUMN `media_url` varchar(500) NULL COMMENT '媒体文件URL（视频/音频/文档地址）' AFTER `is_trial`,
ADD COLUMN `media_file_name` varchar(255) NULL COMMENT '媒体文件名称' AFTER `media_url`,
ADD COLUMN `media_file_size` bigint NULL COMMENT '媒体文件大小（字节）' AFTER `media_file_name`;

-- 添加索引以提高查询性能
CREATE INDEX `idx_media_url` ON `psy_course_chapter` (`media_url`);

-- 更新表注释
ALTER TABLE `psy_course_chapter` COMMENT = '课程章节内容表（包含媒体文件信息）';

-- 验证字段添加结果
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT,
    COLUMN_COMMENT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
  AND TABLE_NAME = 'psy_course_chapter' 
  AND COLUMN_NAME IN ('media_url', 'media_file_name', 'media_file_size')
ORDER BY ORDINAL_POSITION;
