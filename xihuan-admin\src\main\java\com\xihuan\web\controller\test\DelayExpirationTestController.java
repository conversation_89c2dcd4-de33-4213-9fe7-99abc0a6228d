package com.xihuan.web.controller.test;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.system.service.ISysConfigService;
import com.xihuan.system.service.task.PsyTimeSlotTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

/**
 * 延后过期功能测试Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test/delayExpiration")
public class DelayExpirationTestController extends BaseController {
    
    @Autowired
    private ISysConfigService configService;
    
    @Autowired
    private PsyTimeSlotTaskService taskService;

    /**
     * 测试配置读取
     */
    @GetMapping("/config")
    public AjaxResult testConfig() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 读取配置
            String enabledValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            String hoursValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            
            boolean enabled = "true".equalsIgnoreCase(enabledValue) || "1".equals(enabledValue);
            int hours = 2;
            try {
                hours = Integer.parseInt(hoursValue);
            } catch (Exception e) {
                // 使用默认值
            }
            
            result.put("enabled", enabled);
            result.put("hours", hours);
            result.put("enabledValue", enabledValue);
            result.put("hoursValue", hoursValue);
            result.put("status", "success");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", e.getMessage());
        }
        
        return AjaxResult.success(result);
    }

    /**
     * 测试时间计算
     */
    @GetMapping("/timeCalculation")
    public AjaxResult testTimeCalculation() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 获取当前时间
            LocalDateTime now = LocalDateTime.now();
            
            // 模拟时间槽结束时间（1小时前）
            LocalDateTime slotEndTime = now.minusHours(1);
            
            // 获取延后配置
            String enabledValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            String hoursValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            
            boolean enabled = "true".equalsIgnoreCase(enabledValue) || "1".equals(enabledValue);
            int delayHours = 2;
            try {
                delayHours = Integer.parseInt(hoursValue);
            } catch (Exception e) {
                // 使用默认值
            }
            
            // 计算过期判断时间
            LocalDateTime cutoffTime = enabled ? now.minusHours(delayHours) : now;
            
            // 判断是否过期
            boolean isExpired = slotEndTime.isBefore(cutoffTime);
            
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            
            result.put("currentTime", now.format(formatter));
            result.put("slotEndTime", slotEndTime.format(formatter));
            result.put("cutoffTime", cutoffTime.format(formatter));
            result.put("delayEnabled", enabled);
            result.put("delayHours", delayHours);
            result.put("isExpired", isExpired);
            result.put("explanation", enabled ? 
                String.format("延后过期已启用，时间槽在 %s 之前才会过期", cutoffTime.format(formatter)) :
                "延后过期未启用，时间槽在当前时间之前就会过期");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", e.getMessage());
        }
        
        return AjaxResult.success(result);
    }

    /**
     * 测试定时任务方法
     */
    @PostMapping("/taskTest")
    public AjaxResult testTask() {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 测试延后过期配置检查
            boolean enabled = taskService.isDelayExpirationEnabled();
            int hours = taskService.getDelayExpirationHours();
            
            result.put("delayEnabled", enabled);
            result.put("delayHours", hours);
            result.put("status", "success");
            result.put("message", "定时任务配置读取成功");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", e.getMessage());
            logger.error("测试定时任务失败", e);
        }
        
        return AjaxResult.success(result);
    }

    /**
     * 快速设置配置（用于测试）
     */
    @PostMapping("/quickSet")
    public AjaxResult quickSetConfig(@RequestParam boolean enabled, @RequestParam(defaultValue = "2") int hours) {
        Map<String, Object> result = new HashMap<>();
        
        try {
            // 这里简化处理，直接返回设置的值
            result.put("enabled", enabled);
            result.put("hours", hours);
            result.put("message", String.format("配置已设置：延后过期%s，延后%d小时", 
                enabled ? "启用" : "禁用", hours));
            result.put("note", "这是测试接口，实际配置请使用 /system/timeSlotConfig/delayExpiration 接口");
            
        } catch (Exception e) {
            result.put("status", "error");
            result.put("message", e.getMessage());
        }
        
        return AjaxResult.success(result);
    }
}
