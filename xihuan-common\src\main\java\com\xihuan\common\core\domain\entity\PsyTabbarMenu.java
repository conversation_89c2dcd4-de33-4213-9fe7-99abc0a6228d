package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 心理咨询移动端导航菜单对象 psy_tabbar_menu
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTabbarMenu extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 菜单序号 */
    @Excel(name = "菜单序号")
    private Integer index;

    /** 菜单名称 */
    @Excel(name = "菜单名称")
    private String name;

    /** 默认图标路径 */
    @Excel(name = "默认图标路径")
    private String img;

    /** 选中图标路径 */
    @Excel(name = "选中图标路径")
    private String acImg;

    /** 页面路径 */
    @Excel(name = "页面路径")
    private String path;

    /** 所需权限标识 */
    @Excel(name = "所需权限标识")
    private String permissions;

    /** 排序值（升序） */
    @Excel(name = "排序值")
    private Integer sortOrder;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0存在 1删除） */
    private String delFlag;
} 