package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.dto.PsyTimeSlotDTO;
import com.xihuan.common.core.domain.entity.PsyTimeCounselorSchedule;
import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.spring.SpringUtils;
import com.xihuan.system.mapper.PsyTimeCounselorScheduleMapper;
import com.xihuan.system.mapper.PsyTimeSlotMapper;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.ISysConfigService;
import com.xihuan.system.service.wxServiceImpl.PsyConsultantServiceImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间槽Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeSlotServiceImpl implements IPsyTimeSlotService {

    private static final Logger logger = LoggerFactory.getLogger(PsyTimeSlotServiceImpl.class);

    @Autowired
    private PsyTimeSlotMapper timeSlotMapper;

    @Autowired
    private PsyTimeCounselorScheduleMapper scheduleMapper;

    @Autowired
    private IPsyTimeRangeService timeRangeService;

    @Autowired
    private PsyConsultantServiceImpl consultantService;

    /**
     * 查询时间槽列表
     */
    @Override
    public List<PsyTimeSlot> selectTimeSlotList(PsyTimeSlot timeSlot) {
        return timeSlotMapper.selectTimeSlotList(timeSlot);
    }

    /**
     * 根据ID查询时间槽
     */
    @Override
    public PsyTimeSlot selectTimeSlotById(Long id) {
        return timeSlotMapper.selectTimeSlotById(id);
    }

    /**
     * 查询咨询师在指定日期范围内的时间槽
     */
    @Override
    public List<PsyTimeSlot> selectSlotsByCounselorAndDateRange(Long counselorId, LocalDate startDate, LocalDate endDate) {
        return timeSlotMapper.selectSlotsByCounselorAndDateRange(
            counselorId, 
            startDate.toString(), 
            endDate.toString()
        );
    }

    /**
     * 查询指定日期的可用时间槽
     */
    @Override
    public List<PsyTimeSlot> selectAvailableSlotsByDate(LocalDate date, Long centerId, Long counselorId) {
        return timeSlotMapper.selectAvailableSlotsByDate(date.toString(), centerId, counselorId);
    }

    /**
     * 查询公开时间槽
     */
    @Override
    public List<PsyTimeSlot> selectPublicSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        return timeSlotMapper.selectPublicSlots(startDate.toString(), endDate.toString(), centerId);
    }

    /**
     * 获取格式化的时间槽数据（按日期和时间段分组）
     */
    @Override
    public List<PsyTimeSlotDTO> getFormattedTimeSlots(Long counselorId, LocalDate startDate, LocalDate endDate) {
        List<PsyTimeSlot> slots = selectSlotsByCounselorAndDateRange(counselorId, startDate, endDate);
        
        if (CollectionUtils.isEmpty(slots)) {
            return new ArrayList<>();
        }

        // 按日期分组
        Map<String, List<PsyTimeSlot>> slotsByDate = slots.stream()
            .collect(Collectors.groupingBy(PsyTimeSlot::getDateKey));

        List<PsyTimeSlotDTO> result = new ArrayList<>();
        
        for (Map.Entry<String, List<PsyTimeSlot>> entry : slotsByDate.entrySet()) {
            String dateKey = entry.getKey();
            List<PsyTimeSlot> daySlots = entry.getValue();
            
            PsyTimeSlotDTO dayDTO = buildDayDTO(dateKey, daySlots);
            result.add(dayDTO);
        }

        // 按日期排序
        result.sort(Comparator.comparing(PsyTimeSlotDTO::getDate));
        return result;
    }

    /**
     * 新增时间槽
     */
    @Override
    public int insertTimeSlot(PsyTimeSlot timeSlot) {
        timeSlot.setCreateTime(DateUtils.getNowDate());
        return timeSlotMapper.insertTimeSlot(timeSlot);
    }

    /**
     * 批量新增时间槽
     */
    @Override
    @Transactional
    public int batchInsertTimeSlots(List<PsyTimeSlot> timeSlots) {
        if (CollectionUtils.isEmpty(timeSlots)) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        timeSlots.forEach(slot -> slot.setCreateTime(now));
        
        return timeSlotMapper.batchInsertTimeSlots(timeSlots);
    }

    /**
     * 修改时间槽
     */
    @Override
    public int updateTimeSlot(PsyTimeSlot timeSlot) {
        timeSlot.setUpdateTime(DateUtils.getNowDate());
        return timeSlotMapper.updateTimeSlot(timeSlot);
    }

    /**
     * 批量更新时间槽状态
     */
    @Override
    public int batchUpdateSlotStatus(List<Long> slotIds, Integer status) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return 0;
        }
        return timeSlotMapper.batchUpdateSlotStatus(slotIds, status);
    }

    /**
     * 删除时间槽信息
     */
    @Override
    public int deleteTimeSlotById(Long id) {
        return timeSlotMapper.deleteTimeSlotById(id);
    }

    /**
     * 批量删除时间槽
     */
    @Override
    public int deleteTimeSlotByIds(Long[] ids) {
        return timeSlotMapper.deleteTimeSlotByIds(ids);
    }

    /**
     * 为咨询师生成指定日期范围的时间槽
     */
    @Override
    @Transactional
    public int generateSlotsForCounselor(Long counselorId, LocalDate startDate, LocalDate endDate) {
        int totalGenerated = 0;
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            totalGenerated += generateSlotsForCounselorOnDate(counselorId, currentDate);
            currentDate = currentDate.plusDays(1);
        }
        
        return totalGenerated;
    }

    /**
     * 为所有咨询师生成指定日期范围的时间槽
     */
    @Override
    @Transactional
    public int generateSlotsForAllCounselors(LocalDate startDate, LocalDate endDate) {
        logger.info("开始为所有咨询师生成时间槽，日期范围：{} 到 {}", startDate, endDate);

        try {
            // 获取所有可用的咨询师
            List<PsyConsultant> consultants = consultantService.listAllAvailableConsultants();

            if (CollectionUtils.isEmpty(consultants)) {
                logger.warn("没有找到可用的咨询师，无法生成时间槽");
                return 0;
            }

            logger.info("找到 {} 个可用咨询师", consultants.size());

            int totalGenerated = 0;

            // 为每个咨询师生成时间槽
            for (PsyConsultant consultant : consultants) {
                try {
                    int generated = generateSlotsForCounselor(consultant.getId(), startDate, endDate);
                    totalGenerated += generated;

                    if (generated > 0) {
                        logger.debug("为咨询师 {} (ID: {}) 生成了 {} 个时间槽",
                            consultant.getName(), consultant.getId(), generated);
                    }
                } catch (Exception e) {
                    logger.error("为咨询师 {} (ID: {}) 生成时间槽失败",
                        consultant.getName(), consultant.getId(), e);
                }
            }

            logger.info("为所有咨询师生成时间槽完成，总共生成 {} 个时间槽", totalGenerated);
            return totalGenerated;

        } catch (Exception e) {
            logger.error("为所有咨询师生成时间槽失败", e);
            return 0;
        }
    }

    /**
     * 清理过期的时间槽
     */
    @Override
    @Transactional
    public int cleanExpiredSlots(LocalDate beforeDate) {
        return timeSlotMapper.deleteSlotsByDateRange("1900-01-01", beforeDate.toString(), null);
    }

    /**
     * 更新过期时间槽的状态
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatus() {
        // 查询今天之前且状态为可用的时间槽
        PsyTimeSlot query = new PsyTimeSlot();
        query.setStatus(0); // 可用状态
        
        List<PsyTimeSlot> slots = selectTimeSlotList(query);
        
        LocalDate today = LocalDate.now();
        List<Long> expiredSlotIds = slots.stream()
            .filter(slot -> LocalDate.parse(slot.getDateKey()).isBefore(today))
            .map(PsyTimeSlot::getId)
            .collect(Collectors.toList());
        
        if (!expiredSlotIds.isEmpty()) {
            return batchUpdateSlotStatus(expiredSlotIds, 2); // 设置为已过期
        }

        return 0;
    }

    /**
     * 更新过期时间槽的状态（支持延后过期配置）
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatusWithDelay() {
        // 查询状态为可用的时间槽
        PsyTimeSlot query = new PsyTimeSlot();
        query.setStatus(0); // 可用状态

        List<PsyTimeSlot> slots = selectTimeSlotList(query);

        if (CollectionUtils.isEmpty(slots)) {
            return 0;
        }

        // 获取延后配置
        boolean isDelayEnabled = isDelayExpirationEnabled();
        int delayHours = getDelayExpirationHours();

        LocalDateTime now = LocalDateTime.now();
        LocalDateTime cutoffTime = isDelayEnabled ? now.minusHours(delayHours) : now;

        List<Long> expiredSlotIds = slots.stream()
            .filter(slot -> {
                try {
                    LocalDate slotDate = LocalDate.parse(slot.getDateKey());
                    LocalDateTime slotDateTime = LocalDateTime.of(slotDate, slot.getEndTime());
                    return slotDateTime.isBefore(cutoffTime);
                } catch (Exception e) {
                    // 解析失败的时间槽，按今天之前处理
                    return LocalDate.parse(slot.getDateKey()).isBefore(now.toLocalDate());
                }
            })
            .map(PsyTimeSlot::getId)
            .collect(Collectors.toList());

        if (!expiredSlotIds.isEmpty()) {
//            logger.info("更新过期时间槽状态，延后配置：{}，延后小时数：{}，过期时间槽数量：{}",
//                isDelayEnabled, delayHours, expiredSlotIds.size());
            return batchUpdateSlotStatus(expiredSlotIds, 2); // 设置为已过期
        }

        return 0;
    }

    /**
     * 检查是否启用延后过期功能
     */
    private boolean isDelayExpirationEnabled() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
        } catch (Exception e) {
//            logger.warn("获取延后过期配置失败，使用默认值：false", e);
            return false;
        }
    }

    /**
     * 获取延后过期的小时数
     */
    private int getDelayExpirationHours() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            return Integer.parseInt(configValue);
        } catch (Exception e) {
//            logger.warn("获取延后过期小时数配置失败，使用默认值：2小时", e);
            return 2;
        }
    }

    /**
     * 为咨询师在指定日期生成时间槽
     */
    private int generateSlotsForCounselorOnDate(Long counselorId, LocalDate date) {
        // 查询咨询师在该日期的排班
        PsyTimeCounselorSchedule schedule = scheduleMapper.selectScheduleByCounselorAndDate(counselorId, date);

        if (schedule == null) {
            logger.debug("咨询师 {} 在日期 {} 没有排班记录", counselorId, date);
            return 0; // 没有排班
        }

        if (schedule.getIsWorking() == 0) {
            logger.debug("咨询师 {} 在日期 {} 为休假状态", counselorId, date);
            return 0; // 休假
        }

        logger.debug("为咨询师 {} 在日期 {} 生成时间槽，工作时间：{} - {}",
            counselorId, date, schedule.getStartTime(), schedule.getEndTime());

        return generateSlotsFromSchedule(schedule);
    }

    /**
     * 根据排班生成时间槽
     */
    private int generateSlotsFromSchedule(PsyTimeCounselorSchedule schedule) {
        List<PsyTimeSlot> slots = new ArrayList<>();
        
        LocalTime startTime = schedule.getStartTime();
        LocalTime endTime = schedule.getEndTime();
        LocalDate scheduleDate = schedule.getScheduleDate();
        
        // 15分钟间隔生成时间槽
        LocalTime currentTime = startTime;
        while (currentTime.isBefore(endTime)) {
            LocalTime slotEndTime = currentTime.plusMinutes(15);
            if (slotEndTime.isAfter(endTime)) {
                break;
            }
            
            PsyTimeSlot slot = createTimeSlot(schedule, currentTime, slotEndTime, scheduleDate);
            slots.add(slot);
            
            currentTime = slotEndTime;
        }
        
        return batchInsertTimeSlots(slots);
    }

    /**
     * 创建时间槽对象
     */
    private PsyTimeSlot createTimeSlot(PsyTimeCounselorSchedule schedule, LocalTime startTime, LocalTime endTime, LocalDate date) {
        PsyTimeSlot slot = new PsyTimeSlot();
        slot.setScheduleId(schedule.getId());
        slot.setIsPublic(0);
        slot.setCenterId(schedule.getCenterId());
        slot.setCounselorId(schedule.getCounselorId());
        slot.setDateKey(date.toString());
        slot.setWeekDay(getWeekDay(date));
        slot.setStartTime(startTime);
        slot.setEndTime(endTime);
        slot.setStatus(0); // 可用
        slot.setDelFlag(0);
        
        // 设置时间段ID
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeByHour(startTime.getHour());
        if (timeRange != null) {
            slot.setRangeId(timeRange.getId());
        }
        
        // 生成时间组哈希
        slot.setTimeGroupHash(generateTimeGroupHash(schedule.getId(), startTime));
        
        return slot;
    }

    /**
     * 构建日期DTO
     */
    private PsyTimeSlotDTO buildDayDTO(String dateKey, List<PsyTimeSlot> daySlots) {
        PsyTimeSlotDTO dayDTO = new PsyTimeSlotDTO();
        LocalDate date = LocalDate.parse(dateKey);
        
        dayDTO.setDate(dateKey);
        dayDTO.setWeekDay(getWeekDay(date));
        dayDTO.setIsToday(date.equals(LocalDate.now()));
        
        // 按时间段分组
        Map<Long, List<PsyTimeSlot>> slotsByRange = daySlots.stream()
            .collect(Collectors.groupingBy(PsyTimeSlot::getRangeId));
        
        List<PsyTimeSlotDTO.TimeRangeDTO> timeRanges = new ArrayList<>();
        for (Map.Entry<Long, List<PsyTimeSlot>> entry : slotsByRange.entrySet()) {
            PsyTimeSlotDTO.TimeRangeDTO rangeDTO = buildTimeRangeDTO(entry.getKey(), entry.getValue());
            timeRanges.add(rangeDTO);
        }
        
        dayDTO.setTimeRanges(timeRanges);
        return dayDTO;
    }

    /**
     * 构建时间段DTO
     */
    private PsyTimeSlotDTO.TimeRangeDTO buildTimeRangeDTO(Long rangeId, List<PsyTimeSlot> rangeSlots) {
        PsyTimeSlotDTO.TimeRangeDTO rangeDTO = new PsyTimeSlotDTO.TimeRangeDTO();
        
        // 获取时间段信息
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeById(rangeId);
        if (timeRange != null) {
            rangeDTO.setRangeName(timeRange.getName());
            rangeDTO.setIconUrl(timeRange.getIconUrl());
        }
        
        // 转换时间槽
        List<PsyTimeSlotDTO.SlotDTO> slotDTOs = rangeSlots.stream()
            .map(this::convertToSlotDTO)
            .sorted(Comparator.comparing(PsyTimeSlotDTO.SlotDTO::getStartTime))
            .collect(Collectors.toList());
        
        rangeDTO.setSlots(slotDTOs);
        return rangeDTO;
    }

    /**
     * 转换为时间槽DTO
     */
    private PsyTimeSlotDTO.SlotDTO convertToSlotDTO(PsyTimeSlot slot) {
        PsyTimeSlotDTO.SlotDTO slotDTO = new PsyTimeSlotDTO.SlotDTO();
        slotDTO.setSlotId(slot.getId());
        slotDTO.setStartTime(slot.getStartTime());
        slotDTO.setEndTime(slot.getEndTime());
        slotDTO.setTimeDisplay(slot.getStartTime() + "-" + slot.getEndTime());
        slotDTO.setStatus(slot.getStatus());
        slotDTO.setAvailable(slot.getStatus() == 0);
        slotDTO.setCounselorId(slot.getCounselorId());
        
        // 设置状态描述
        switch (slot.getStatus()) {
            case 0:
                slotDTO.setStatusText("可预约");
                break;
            case 1:
                slotDTO.setStatusText("已预约");
                break;
            case 2:
                slotDTO.setStatusText("已过期");
                break;
            default:
                slotDTO.setStatusText("未知");
        }
        
        return slotDTO;
    }

    /**
     * 获取星期几
     */
    private String getWeekDay(LocalDate date) {
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        return weekDays[date.getDayOfWeek().getValue() - 1];
    }

    /**
     * 生成时间组哈希
     */
    private String generateTimeGroupHash(Long scheduleId, LocalTime startTime) {
        String input = scheduleId + "_" + startTime.toSecondOfDay();
        return Integer.toHexString(input.hashCode());
    }
}
