package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyConsultantOrder;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 心理咨询订单表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyConsultantOrderMapper {
    
    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    List<PsyConsultantOrder> selectOrderList(PsyConsultantOrder order);

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    PsyConsultantOrder selectOrderById(Long id);

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    PsyConsultantOrder selectOrderByOrderNo(String orderNo);

    /**
     * 查询订单详情（包含用户、咨询师等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyConsultantOrder selectOrderWithDetails(Long id);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyConsultantOrder> selectOrdersByUserId(Long userId);

    /**
     * 根据咨询师ID查询订单列表
     * 
     * @param consultantId 咨询师ID
     * @return 订单集合
     */
    List<PsyConsultantOrder> selectOrdersByConsultantId(Long consultantId);

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int insertOrder(PsyConsultantOrder order);

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int updateOrder(PsyConsultantOrder order);

    /**
     * 删除订单
     * 
     * @param id 订单ID
     * @return 结果
     */
    int deleteOrderById(Long id);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param status 订单状态
     * @param paymentMethod 支付方式
     * @param paymentTime 支付时间
     * @return 结果
     */
    int updateOrderPaymentStatus(@Param("orderNo") String orderNo, @Param("status") String status, 
                                @Param("paymentMethod") String paymentMethod, @Param("paymentTime") Date paymentTime);

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 订单状态
     * @return 结果
     */
    int updateOrderStatus(@Param("id") Long id, @Param("status") String status);

    /**
     * 取消订单
     * 
     * @param id 订单ID
     * @param cancelReason 取消原因
     * @param cancelTime 取消时间
     * @return 结果
     */
    int cancelOrder(@Param("id") Long id, @Param("cancelReason") String cancelReason, @Param("cancelTime") Date cancelTime);

    /**
     * 退款订单
     * 
     * @param id 订单ID
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @param refundTime 退款时间
     * @return 结果
     */
    int refundOrder(@Param("id") Long id, @Param("refundAmount") BigDecimal refundAmount, 
                   @Param("refundReason") String refundReason, @Param("refundTime") Date refundTime);

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    String generateOrderNo();

    /**
     * 查询即将到期的订单
     * 
     * @param minutes 提前分钟数
     * @return 订单集合
     */
    List<PsyConsultantOrder> selectExpiringOrders(Integer minutes);

    /**
     * 查询已过期的订单
     * 
     * @return 订单集合
     */
    List<PsyConsultantOrder> selectExpiredOrders();

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @return 订单数量
     */
    int countUserOrders(Long userId);

    /**
     * 统计咨询师订单数量
     * 
     * @param consultantId 咨询师ID
     * @return 订单数量
     */
    int countConsultantOrders(Long consultantId);

    /**
     * 统计订单收入
     * 
     * @param consultantId 咨询师ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 收入金额
     */
    BigDecimal sumOrderIncome(@Param("consultantId") Long consultantId, @Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 检查时间段冲突
     * 
     * @param consultantId 咨询师ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param excludeOrderId 排除的订单ID
     * @return 冲突订单数量
     */
    int checkTimeConflict(@Param("consultantId") Long consultantId, @Param("startTime") Date startTime, 
                         @Param("endTime") Date endTime, @Param("excludeOrderId") Long excludeOrderId);
}
