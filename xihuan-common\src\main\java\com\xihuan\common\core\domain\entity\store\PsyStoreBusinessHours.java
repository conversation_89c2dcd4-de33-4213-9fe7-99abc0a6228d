package com.xihuan.common.core.domain.entity.store;

import com.xihuan.common.core.domain.BaseEntity;
import com.xihuan.common.annotation.Excel;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalTime;

/**
 * 门店营业时间段对象 psy_store_business_hours
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyStoreBusinessHours extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 门店ID */
    @Excel(name = "门店ID")
    private Long storeId;

    /** 开始时间 */
    @Excel(name = "开始时间")
    private LocalTime startTime;

    /** 结束时间 */
    @Excel(name = "结束时间")
    private LocalTime endTime;

    /** 是否24小时营业：1是 0否 */
    @Excel(name = "是否24小时营业", readConverterExp = "1=是,0=否")
    private Integer is24h;
} 