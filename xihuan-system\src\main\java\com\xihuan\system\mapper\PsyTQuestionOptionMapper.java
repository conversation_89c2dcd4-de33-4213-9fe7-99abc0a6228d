package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTQuestionOption;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 题目选项Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTQuestionOptionMapper {
    
    /**
     * 查询题目选项列表
     * 
     * @param option 题目选项信息
     * @return 题目选项集合
     */
    List<PsyTQuestionOption> selectOptionList(PsyTQuestionOption option);

    /**
     * 根据ID查询题目选项
     * 
     * @param id 题目选项ID
     * @return 题目选项信息
     */
    PsyTQuestionOption selectOptionById(Long id);

    /**
     * 根据题目ID查询选项列表
     * 
     * @param questionId 题目ID
     * @return 选项集合
     */
    List<PsyTQuestionOption> selectOptionsByQuestionId(Long questionId);

    /**
     * 新增题目选项
     * 
     * @param option 题目选项信息
     * @return 结果
     */
    int insertOption(PsyTQuestionOption option);

    /**
     * 批量新增题目选项
     * 
     * @param options 选项列表
     * @return 结果
     */
    int batchInsertOptions(List<PsyTQuestionOption> options);

    /**
     * 修改题目选项
     * 
     * @param option 题目选项信息
     * @return 结果
     */
    int updateOption(PsyTQuestionOption option);

    /**
     * 删除题目选项
     * 
     * @param id 题目选项ID
     * @return 结果
     */
    int deleteOptionById(Long id);

    /**
     * 批量删除题目选项
     * 
     * @param ids 需要删除的题目选项ID
     * @return 结果
     */
    int deleteOptionByIds(Long[] ids);

    /**
     * 根据题目ID删除选项
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    int deleteOptionsByQuestionId(Long questionId);

    /**
     * 统计题目选项数量
     * 
     * @param questionId 题目ID
     * @return 数量
     */
    int countOptionsByQuestionId(Long questionId);

    /**
     * 查询选项选择统计
     * 
     * @param optionId 选项ID
     * @return 统计信息
     */
    Map<String, Object> selectOptionChoiceStats(Long optionId);

    /**
     * 查询题目选项分布
     * 
     * @param questionId 题目ID
     * @return 分布信息
     */
    List<Map<String, Object>> selectQuestionOptionDistribution(Long questionId);

    /**
     * 批量更新选项排序
     * 
     * @param options 选项列表
     * @return 结果
     */
    int batchUpdateOptionSort(List<PsyTQuestionOption> options);

    /**
     * 批量更新选项状态
     * 
     * @param ids 选项ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateOptionStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 复制选项到新题目
     * 
     * @param sourceQuestionId 源题目ID
     * @param targetQuestionId 目标题目ID
     * @return 结果
     */
    int copyOptionsToQuestion(@Param("sourceQuestionId") Long sourceQuestionId, 
                             @Param("targetQuestionId") Long targetQuestionId);
}
