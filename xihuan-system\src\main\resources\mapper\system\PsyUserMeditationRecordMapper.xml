<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyUserMeditationRecordMapper">

    <resultMap id="BaseResultMap" type="PsyUserMeditationRecord">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="meditation_id" property="meditationId"/>
        <result column="duration_played" property="durationPlayed"/>
        <result column="is_completed" property="isCompleted"/>
        <result column="mood_before" property="moodBefore"/>
        <result column="mood_after" property="moodAfter"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <resultMap id="RecordWithDetailsMap" type="PsyUserMeditationRecord" extends="BaseResultMap">
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
        </association>
        <association property="meditation" javaType="PsyMeditation">
            <id column="meditation_id" property="id"/>
            <result column="meditation_title" property="title"/>
            <result column="meditation_duration" property="duration"/>
            <result column="narrator" property="narrator"/>
            <result column="cover_image" property="coverImage"/>
        </association>
    </resultMap>

    <select id="selectRecordList" parameterType="PsyUserMeditationRecord" resultMap="BaseResultMap">
        SELECT * FROM psy_user_meditation_record
        <where>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="meditationId != null">AND meditation_id = #{meditationId}</if>
            <if test="isCompleted != null">AND is_completed = #{isCompleted}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectRecordById" resultMap="BaseResultMap">
        SELECT * FROM psy_user_meditation_record WHERE id = #{id}
    </select>

    <select id="selectLatestRecordByUserAndMeditation" resultMap="BaseResultMap">
        SELECT * FROM psy_user_meditation_record 
        WHERE user_id = #{userId} AND meditation_id = #{meditationId}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="selectRecordsByUserId" resultMap="RecordWithDetailsMap">
        SELECT 
            r.*,
            m.title AS meditation_title,
            m.duration AS meditation_duration,
            m.narrator,
            m.cover_image,
            u.user_name,
            u.nick_name
        FROM psy_user_meditation_record r
        LEFT JOIN psy_meditation m ON r.meditation_id = m.id
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.user_id = #{userId}
        ORDER BY r.create_time DESC
    </select>

    <select id="selectRecordsByMeditationId" resultMap="BaseResultMap">
        SELECT * FROM psy_user_meditation_record 
        WHERE meditation_id = #{meditationId}
        ORDER BY create_time DESC
    </select>

    <insert id="insertRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_user_meditation_record (
            user_id, meditation_id, duration_played, is_completed,
            mood_before, mood_after, create_time
        ) VALUES (
            #{userId}, #{meditationId}, #{durationPlayed}, #{isCompleted},
            #{moodBefore}, #{moodAfter}, #{createTime}
        )
    </insert>

    <update id="updateRecord" parameterType="PsyUserMeditationRecord">
        UPDATE psy_user_meditation_record
        <set>
            <if test="durationPlayed != null">duration_played = #{durationPlayed},</if>
            <if test="isCompleted != null">is_completed = #{isCompleted},</if>
            <if test="moodBefore != null">mood_before = #{moodBefore},</if>
            <if test="moodAfter != null">mood_after = #{moodAfter},</if>
        </set>
        WHERE id = #{id}
    </update>

    <delete id="deleteRecordById">
        DELETE FROM psy_user_meditation_record WHERE id = #{id}
    </delete>

    <delete id="deleteRecordByIds">
        DELETE FROM psy_user_meditation_record
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteRecordByUserId">
        DELETE FROM psy_user_meditation_record WHERE user_id = #{userId}
    </delete>

    <delete id="deleteRecordByMeditationId">
        DELETE FROM psy_user_meditation_record WHERE meditation_id = #{meditationId}
    </delete>

    <select id="sumUserMeditationDuration" resultType="int">
        SELECT COALESCE(SUM(duration_played), 0) 
        FROM psy_user_meditation_record 
        WHERE user_id = #{userId}
    </select>

    <select id="countUserMeditationTimes" resultType="int">
        SELECT COUNT(*) 
        FROM psy_user_meditation_record 
        WHERE user_id = #{userId}
    </select>

    <select id="countUserCompletedMeditations" resultType="int">
        SELECT COUNT(*) 
        FROM psy_user_meditation_record 
        WHERE user_id = #{userId} AND is_completed = 1
    </select>

    <select id="countMeditationPlayTimes" resultType="int">
        SELECT COUNT(*) 
        FROM psy_user_meditation_record 
        WHERE meditation_id = #{meditationId}
    </select>

</mapper>
