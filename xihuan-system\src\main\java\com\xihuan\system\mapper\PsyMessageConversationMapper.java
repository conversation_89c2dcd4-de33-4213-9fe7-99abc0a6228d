package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyMessageConversation;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 消息会话Mapper接口
 */
public interface PsyMessageConversationMapper {
    
    /**
     * 插入会话
     * 
     * @param conversation 会话信息
     * @return 结果
     */
    int insertConversation(PsyMessageConversation conversation);
    
    /**
     * 根据ID查询会话
     * 
     * @param conversationId 会话ID
     * @return 会话信息
     */
    PsyMessageConversation selectConversationById(Long conversationId);
    
    /**
     * 根据用户ID和咨询师ID查询会话
     * 
     * @param userId 用户ID
     * @param consultantId 咨询师ID
     * @return 会话信息
     */
    PsyMessageConversation selectConversationByUserAndConsultant(@Param("userId") Long userId, @Param("consultantId") Long consultantId);
    
    /**
     * 根据用户ID查询会话列表
     * 
     * @param userId 用户ID
     * @return 会话列表
     */
    List<PsyMessageConversation> selectConversationsByUserId(Long userId);
    
    /**
     * 根据咨询师ID查询会话列表
     * 
     * @param consultantId 咨询师ID
     * @return 会话列表
     */
    List<PsyMessageConversation> selectConversationsByConsultantId(Long consultantId);
    
    /**
     * 查询所有会话列表
     * 
     * @return 会话列表
     */
    List<PsyMessageConversation> selectAllConversations();
    
    /**
     * 更新会话
     * 
     * @param conversation 会话信息
     * @return 结果
     */
    int updateConversation(PsyMessageConversation conversation);
    
    /**
     * 增加用户未读计数
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    int incrementUserUnreadCount(Long conversationId);
    
    /**
     * 增加咨询师未读计数
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    int incrementConsultantUnreadCount(Long conversationId);
    
    /**
     * 重置用户未读计数
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    int resetUserUnreadCount(Long conversationId);
    
    /**
     * 重置咨询师未读计数
     * 
     * @param conversationId 会话ID
     * @return 结果
     */
    int resetConsultantUnreadCount(Long conversationId);
} 