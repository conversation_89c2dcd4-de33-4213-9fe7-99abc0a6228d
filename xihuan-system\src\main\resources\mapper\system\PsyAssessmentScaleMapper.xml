<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentScaleMapper">

    <!-- 结果映射 -->
    <resultMap id="ScaleResultMap" type="PsyAssessmentScale">
        <id property="id" column="id"/>
        <result property="scaleName" column="scale_name"/>
        <result property="scaleCode" column="scale_code"/>
        <result property="description" column="description"/>
        <result property="instruction" column="instruction"/>
        <result property="categoryId" column="category_id"/>
        <result property="author" column="author"/>
        <result property="version" column="version"/>
        <result property="questionCount" column="question_count"/>
        <result property="timeLimit" column="time_limit"/>
        <result property="difficultyLevel" column="difficulty_level"/>
        <result property="price" column="price"/>
        <result property="isFree" column="is_free"/>
        <result property="coverImage" column="cover_image"/>
        <result property="tags" column="tags"/>
        <result property="status" column="status"/>
        <result property="viewCount" column="view_count"/>
        <result property="testCount" column="test_count"/>
        <result property="ratingAvg" column="rating_avg"/>
        <result property="ratingCount" column="rating_count"/>
        <result property="searchKeywords" column="search_keywords"/>
        <result property="searchCount" column="search_count"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 带分类的结果映射 -->
    <resultMap id="ScaleWithCategoryMap" type="PsyAssessmentScale" extends="ScaleResultMap">
        <association property="category" javaType="PsyCategory">
            <id property="categoryId" column="c_category_id"/>
            <result property="categoryName" column="c_category_name"/>
            <result property="parentId" column="c_parent_id"/>
            <result property="orderNum" column="c_order_num"/>
            <result property="status" column="c_status"/>
        </association>
    </resultMap>

    <!-- 详细结果映射 -->
    <resultMap id="ScaleDetailMap" type="PsyAssessmentScale" extends="ScaleWithCategoryMap">
        <collection property="questions" ofType="PsyAssessmentQuestion" column="id" select="com.xihuan.system.mapper.PsyAssessmentQuestionMapper.selectQuestionsByScaleId"/>
        <collection property="categories" ofType="PsyCategory" column="id" select="selectCategoriesByScaleId"/>
        <collection property="interpretations" ofType="PsyAssessmentInterpretation" column="id" select="com.xihuan.system.mapper.PsyAssessmentInterpretationMapper.selectInterpretationsByScaleId"/>
    </resultMap>

    <!-- 查询量表列表 -->
    <select id="selectScaleList" parameterType="PsyAssessmentScale" resultMap="ScaleWithCategoryMap">
        SELECT s.*, 
               c.category_id as c_category_id, 
               c.category_name as c_category_name,
               c.parent_id as c_parent_id,
               c.order_num as c_order_num,
               c.status as c_status
        FROM psy_t_scale s
        LEFT JOIN psy_category c ON s.category_id = c.category_id
        WHERE s.del_flag = 0
        <if test="scaleName != null and scaleName != ''">
            AND s.scale_name LIKE CONCAT('%', #{scaleName}, '%')
        </if>
        <if test="scaleCode != null and scaleCode != ''">
            AND s.scale_code LIKE CONCAT('%', #{scaleCode}, '%')
        </if>
        <if test="categoryId != null">
            AND s.category_id = #{categoryId}
        </if>
        <if test="difficultyLevel != null">
            AND s.difficulty_level = #{difficultyLevel}
        </if>
        <if test="isFree != null">
            AND s.is_free = #{isFree}
        </if>
        <if test="status != null">
            AND s.status = #{status}
        </if>
        <if test="searchKeywords != null and searchKeywords != ''">
            AND (
                s.scale_name LIKE CONCAT('%', #{searchKeywords}, '%')
                OR s.description LIKE CONCAT('%', #{searchKeywords}, '%')
                OR s.search_keywords LIKE CONCAT('%', #{searchKeywords}, '%')
            )
        </if>
        ORDER BY s.create_time DESC
    </select>

    <!-- 根据ID查询量表 -->
    <select id="selectScaleById" parameterType="Long" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询量表详情 -->
    <select id="selectScaleWithDetails" parameterType="Long" resultMap="ScaleDetailMap">
        SELECT s.*, 
               c.category_id as c_category_id, 
               c.category_name as c_category_name,
               c.parent_id as c_parent_id,
               c.order_num as c_order_num,
               c.status as c_status
        FROM psy_t_scale s
        LEFT JOIN psy_category c ON s.category_id = c.category_id
        WHERE s.id = #{id} AND s.del_flag = 0
    </select>

    <!-- 根据编码查询量表 -->
    <select id="selectScaleByCode" parameterType="String" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale WHERE scale_code = #{scaleCode} AND del_flag = 0
    </select>

    <!-- 新增量表 -->
    <insert id="insertScale" parameterType="PsyAssessmentScale" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_scale (
            scale_name, scale_code, description, instruction, category_id, author, version,
            question_count, time_limit, difficulty_level, price, is_free, cover_image, tags,
            status, view_count, test_count, rating_avg, rating_count, search_keywords, search_count,
            del_flag, create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{scaleName}, #{scaleCode}, #{description}, #{instruction}, #{categoryId}, #{author}, #{version},
            #{questionCount}, #{timeLimit}, #{difficultyLevel}, #{price}, #{isFree}, #{coverImage}, #{tags},
            #{status}, #{viewCount}, #{testCount}, #{ratingAvg}, #{ratingCount}, #{searchKeywords}, #{searchCount},
            #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate(), #{remark}
        )
    </insert>

    <!-- 修改量表 -->
    <update id="updateScale" parameterType="PsyAssessmentScale">
        UPDATE psy_t_scale
        <set>
            <if test="scaleName != null">scale_name = #{scaleName},</if>
            <if test="scaleCode != null">scale_code = #{scaleCode},</if>
            <if test="description != null">description = #{description},</if>
            <if test="instruction != null">instruction = #{instruction},</if>
            <if test="categoryId != null">category_id = #{categoryId},</if>
            <if test="author != null">author = #{author},</if>
            <if test="version != null">version = #{version},</if>
            <if test="questionCount != null">question_count = #{questionCount},</if>
            <if test="timeLimit != null">time_limit = #{timeLimit},</if>
            <if test="difficultyLevel != null">difficulty_level = #{difficultyLevel},</if>
            <if test="price != null">price = #{price},</if>
            <if test="isFree != null">is_free = #{isFree},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="status != null">status = #{status},</if>
            <if test="viewCount != null">view_count = #{viewCount},</if>
            <if test="testCount != null">test_count = #{testCount},</if>
            <if test="ratingAvg != null">rating_avg = #{ratingAvg},</if>
            <if test="ratingCount != null">rating_count = #{ratingCount},</if>
            <if test="searchKeywords != null">search_keywords = #{searchKeywords},</if>
            <if test="searchCount != null">search_count = #{searchCount},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除量表 -->
    <update id="deleteScaleById" parameterType="Long">
        UPDATE psy_t_scale SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除量表 -->
    <update id="deleteScaleByIds" parameterType="Long">
        UPDATE psy_t_scale SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据分类ID查询量表列表 -->
    <select id="selectScalesByCategoryId" parameterType="Long" resultMap="ScaleResultMap">
        SELECT s.* FROM psy_t_scale s
        LEFT JOIN psy_t_scale_category_rel r ON s.id = r.scale_id
        WHERE r.category_id = #{categoryId} AND s.del_flag = 0 AND s.status = 1
        ORDER BY s.create_time DESC
    </select>

    <!-- 查询用户已购买的量表列表 -->
    <select id="selectPurchasedScalesByUserId" parameterType="Long" resultMap="ScaleResultMap">
        SELECT s.* FROM psy_t_scale s
        LEFT JOIN psy_t_order o ON s.id = o.scale_id
        WHERE o.user_id = #{userId} AND o.payment_status = 1 AND s.del_flag = 0
        ORDER BY o.create_time DESC
    </select>

    <!-- 查询热门量表列表 -->
    <select id="selectHotScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = 0 AND status = 1
        ORDER BY test_count DESC, view_count DESC
        LIMIT #{limit}
    </select>

    <!-- 查询推荐量表列表 -->
    <select id="selectRecommendScales" resultMap="ScaleResultMap">
        SELECT s.* FROM psy_t_scale s
        WHERE s.del_flag = 0 AND s.status = 1
        <if test="userId != null">
            AND NOT EXISTS (
                SELECT 1 FROM psy_t_record r
                WHERE r.user_id = #{userId} AND r.scale_id = s.id AND r.status = 1
            )
        </if>
        ORDER BY s.rating_avg DESC, s.test_count DESC
        LIMIT #{limit}
    </select>

    <!-- 查询免费量表列表 -->
    <select id="selectFreeScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale
        WHERE del_flag = 0 AND status = 1 AND is_free = 1
        ORDER BY create_time DESC
    </select>

    <!-- 更新量表查看次数 -->
    <update id="updateViewCount" parameterType="Long">
        UPDATE psy_t_scale SET view_count = view_count + 1 WHERE id = #{id}
    </update>

    <!-- 更新量表测试次数 -->
    <update id="updateTestCount" parameterType="Long">
        UPDATE psy_t_scale SET test_count = test_count + 1 WHERE id = #{id}
    </update>

    <!-- 更新量表搜索次数 -->
    <update id="updateSearchCount" parameterType="Long">
        UPDATE psy_t_scale SET search_count = search_count + 1 WHERE id = #{id}
    </update>

    <!-- 更新量表评分统计 -->
    <update id="updateRatingStats" parameterType="Long">
        UPDATE psy_t_scale s
        SET s.rating_avg = (
                SELECT IFNULL(AVG(r.rating), 0)
                FROM psy_t_review r
                WHERE r.scale_id = s.id AND r.status = 1 AND r.del_flag = 0
            ),
            s.rating_count = (
                SELECT COUNT(1)
                FROM psy_t_review r
                WHERE r.scale_id = s.id AND r.status = 1 AND r.del_flag = 0
            )
        WHERE s.id = #{scaleId}
    </update>

    <!-- 检查量表编码唯一性 -->
    <select id="checkScaleCodeUnique" resultType="int">
        SELECT COUNT(1) FROM psy_t_scale
        WHERE scale_code = #{scaleCode}
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 检查量表是否被使用 -->
    <select id="checkScaleInUse" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_record WHERE scale_id = #{id} AND del_flag = 0
    </select>

    <!-- 搜索量表 -->
    <select id="searchScales" resultMap="ScaleResultMap">
        SELECT * FROM psy_t_scale s
        WHERE s.del_flag = 0 AND s.status = 1
        <if test="keyword != null and keyword != ''">
            AND (
                s.scale_name LIKE CONCAT('%', #{keyword}, '%')
                OR s.description LIKE CONCAT('%', #{keyword}, '%')
                OR s.search_keywords LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="categoryId != null">
            AND (
                s.category_id = #{categoryId}
                OR EXISTS (
                    SELECT 1 FROM psy_t_scale_category_rel r
                    WHERE r.scale_id = s.id AND r.category_id = #{categoryId}
                )
            )
        </if>
        <if test="difficultyLevel != null">
            AND s.difficulty_level = #{difficultyLevel}
        </if>
        <if test="isFree != null">
            AND s.is_free = #{isFree}
        </if>
        ORDER BY 
        <if test="keyword != null and keyword != ''">
            CASE 
                WHEN s.scale_name LIKE CONCAT(#{keyword}, '%') THEN 1
                WHEN s.scale_name LIKE CONCAT('%', #{keyword}, '%') THEN 2
                ELSE 3
            END,
        </if>
        s.test_count DESC, s.view_count DESC
    </select>

    <!-- 统计量表数量 -->
    <select id="countScales" parameterType="PsyAssessmentScale" resultType="int">
        SELECT COUNT(1) FROM psy_t_scale
        WHERE del_flag = 0
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="isFree != null">
            AND is_free = #{isFree}
        </if>
        <if test="categoryId != null">
            AND category_id = #{categoryId}
        </if>
    </select>

    <!-- 查询量表统计信息 -->
    <select id="selectScaleStats" resultType="java.util.Map">
        SELECT
            COUNT(CASE WHEN status = 1 THEN 1 END) as published_count,
            COUNT(CASE WHEN is_free = 1 THEN 1 END) as free_count,
            SUM(test_count) as total_test_count,
            SUM(view_count) as total_view_count,
            AVG(rating_avg) as avg_rating
        FROM psy_t_scale
        WHERE del_flag = 0
    </select>

    <!-- 查询量表分类 -->
    <select id="selectCategoriesByScaleId" parameterType="Long" resultType="PsyCategory">
        SELECT c.* FROM psy_category c
        INNER JOIN psy_t_scale_category_rel r ON c.category_id = r.category_id
        WHERE r.scale_id = #{id} AND c.status = '0'
        ORDER BY c.order_num
    </select>
</mapper>
