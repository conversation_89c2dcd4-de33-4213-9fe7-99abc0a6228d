package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 心理咨询记录表 psy_consultation_record
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyConsultationRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 咨询师ID */
    @Excel(name = "咨询师ID")
    private Long consultantId;

    /** 关联订单ID */
    @Excel(name = "关联订单ID")
    private Long orderId;

    /** 咨询开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "咨询开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 咨询结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "咨询结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 实际开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualStartTime;

    /** 实际结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "实际结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date actualEndTime;

    /** 实际咨询时长(分钟) */
    @Excel(name = "实际咨询时长(分钟)")
    private Integer duration;

    /** 咨询类型 */
    @Excel(name = "咨询类型")
    private String consultType;

    /** 主要症状 */
    @Excel(name = "主要症状")
    private String mainSymptoms;

    /** 咨询内容摘要 */
    @Excel(name = "咨询内容摘要")
    private String consultContent;

    /** 当前咨询次数(系列咨询中的第几次) */
    @Excel(name = "当前咨询次数")
    private Integer consultCount;

    /** 用户评价(1-5星) */
    @Excel(name = "用户评价")
    private Integer userRating;

    /** 删除标志(0正常 2删除) */
    private String delFlag;

    /** 附件记录(保存咨询摘要/报告等附件路径) */
    @Excel(name = "附件记录")
    private String attachment;

    // 关联对象
    /** 用户信息 */
    private SysUser user;

    /** 咨询师信息 */
    private PsyConsultant consultant;

    /** 订单信息 */
    private PsyConsultantOrder order;

    /** 中断记录列表 */
    private java.util.List<PsyConsultantInterruption> interruptions;

    /** 评价信息 */
    private PsyConsultantReview review;

    // 统计字段
    /** 总咨询时长 */
    private Integer totalDuration;

    /** 咨询次数 */
    private Integer totalCount;

    /** 平均评分 */
    private java.math.BigDecimal avgRating;
}
