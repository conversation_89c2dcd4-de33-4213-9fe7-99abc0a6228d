package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyTAnswerRecord;

import java.util.List;
import java.util.Map;

/**
 * 测评流程Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAssessmentService {
    
    /**
     * 开始测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param source 测评来源（个人/企业）
     * @return 测评会话信息
     */
    Map<String, Object> startAssessment(Long userId, Long scaleId, String source);

    /**
     * 获取测评题目
     * 
     * @param sessionId 会话ID
     * @param questionNo 题目序号
     * @return 题目信息
     */
    Map<String, Object> getAssessmentQuestion(String sessionId, Integer questionNo);

    /**
     * 提交答案
     * 
     * @param sessionId 会话ID
     * @param questionId 题目ID
     * @param answerContent 答案内容
     * @param responseTime 答题耗时
     * @return 提交结果
     */
    Map<String, Object> submitAnswer(String sessionId, Long questionId, String answerContent, Integer responseTime);

    /**
     * 批量提交答案
     * 
     * @param sessionId 会话ID
     * @param answers 答案列表
     * @return 提交结果
     */
    Map<String, Object> batchSubmitAnswers(String sessionId, List<PsyTAnswerRecord> answers);

    /**
     * 获取下一题
     * 
     * @param sessionId 会话ID
     * @return 下一题信息
     */
    Map<String, Object> getNextQuestion(String sessionId);

    /**
     * 获取上一题
     * 
     * @param sessionId 会话ID
     * @return 上一题信息
     */
    Map<String, Object> getPreviousQuestion(String sessionId);

    /**
     * 跳转到指定题目
     * 
     * @param sessionId 会话ID
     * @param questionNo 题目序号
     * @return 题目信息
     */
    Map<String, Object> jumpToQuestion(String sessionId, Integer questionNo);

    /**
     * 保存测评进度
     * 
     * @param sessionId 会话ID
     * @return 保存结果
     */
    Map<String, Object> saveProgress(String sessionId);

    /**
     * 恢复测评
     * 
     * @param sessionId 会话ID
     * @return 测评状态
     */
    Map<String, Object> resumeAssessment(String sessionId);

    /**
     * 暂停测评
     * 
     * @param sessionId 会话ID
     * @return 暂停结果
     */
    Map<String, Object> pauseAssessment(String sessionId);

    /**
     * 完成测评
     * 
     * @param sessionId 会话ID
     * @return 测评结果
     */
    Map<String, Object> completeAssessment(String sessionId);

    /**
     * 放弃测评
     * 
     * @param sessionId 会话ID
     * @param reason 放弃原因
     * @return 放弃结果
     */
    Map<String, Object> abandonAssessment(String sessionId, String reason);

    /**
     * 获取测评进度
     * 
     * @param sessionId 会话ID
     * @return 进度信息
     */
    Map<String, Object> getAssessmentProgress(String sessionId);

    /**
     * 获取测评状态
     * 
     * @param sessionId 会话ID
     * @return 状态信息
     */
    Map<String, Object> getAssessmentStatus(String sessionId);

    /**
     * 计算测评结果
     * 
     * @param recordId 测评记录ID
     * @return 计算结果
     */
    Map<String, Object> calculateResult(Long recordId);

    /**
     * 生成测评报告
     * 
     * @param recordId 测评记录ID
     * @param reportLevel 报告层级
     * @return 报告内容
     */
    Map<String, Object> generateReport(Long recordId, Integer reportLevel);

    /**
     * 获取测评建议
     * 
     * @param recordId 测评记录ID
     * @return 建议内容
     */
    Map<String, Object> getAssessmentSuggestions(Long recordId);

    /**
     * 验证测评权限
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 权限验证结果
     */
    Map<String, Object> validateAssessmentPermission(Long userId, Long scaleId);

    /**
     * 检查测评前置条件
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 检查结果
     */
    Map<String, Object> checkAssessmentPrerequisites(Long userId, Long scaleId);

    /**
     * 获取测评历史
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 历史记录
     */
    List<Map<String, Object>> getAssessmentHistory(Long userId, Long scaleId);

    /**
     * 比较测评结果
     * 
     * @param recordIds 测评记录ID列表
     * @return 比较结果
     */
    Map<String, Object> compareAssessmentResults(List<Long> recordIds);

    /**
     * 导出测评结果
     * 
     * @param recordId 测评记录ID
     * @param format 导出格式
     * @return 导出数据
     */
    Map<String, Object> exportAssessmentResult(Long recordId, String format);

    /**
     * 分享测评结果
     * 
     * @param recordId 测评记录ID
     * @param shareType 分享类型
     * @return 分享信息
     */
    Map<String, Object> shareAssessmentResult(Long recordId, String shareType);

    /**
     * 获取测评统计
     * 
     * @param scaleId 量表ID
     * @param timeRange 时间范围
     * @return 统计信息
     */
    Map<String, Object> getAssessmentStatistics(Long scaleId, String timeRange);

    /**
     * 获取用户测评概览
     * 
     * @param userId 用户ID
     * @return 概览信息
     */
    Map<String, Object> getUserAssessmentOverview(Long userId);

    /**
     * 预览测评
     * 
     * @param scaleId 量表ID
     * @return 预览信息
     */
    Map<String, Object> previewAssessment(Long scaleId);

    /**
     * 模拟测评
     * 
     * @param scaleId 量表ID
     * @param answers 模拟答案
     * @return 模拟结果
     */
    Map<String, Object> simulateAssessment(Long scaleId, Map<Long, String> answers);

    /**
     * 自动答题（测试用）
     * 
     * @param sessionId 会话ID
     * @param strategy 答题策略
     * @return 自动答题结果
     */
    Map<String, Object> autoAnswer(String sessionId, String strategy);

    /**
     * 验证答案有效性
     * 
     * @param questionId 题目ID
     * @param answerContent 答案内容
     * @return 验证结果
     */
    Map<String, Object> validateAnswer(Long questionId, String answerContent);

    /**
     * 获取答题提示
     * 
     * @param questionId 题目ID
     * @return 提示信息
     */
    Map<String, Object> getAnswerHint(Long questionId);

    /**
     * 标记题目
     * 
     * @param sessionId 会话ID
     * @param questionId 题目ID
     * @param marked 是否标记
     * @return 标记结果
     */
    Map<String, Object> markQuestion(String sessionId, Long questionId, Boolean marked);

    /**
     * 获取已标记题目
     * 
     * @param sessionId 会话ID
     * @return 标记题目列表
     */
    List<Map<String, Object>> getMarkedQuestions(String sessionId);

    /**
     * 检查测评完整性
     * 
     * @param sessionId 会话ID
     * @return 完整性检查结果
     */
    Map<String, Object> checkAssessmentCompleteness(String sessionId);

    /**
     * 获取测评摘要
     * 
     * @param sessionId 会话ID
     * @return 测评摘要
     */
    Map<String, Object> getAssessmentSummary(String sessionId);

    /**
     * 重新计算结果
     * 
     * @param recordId 测评记录ID
     * @return 重新计算结果
     */
    Map<String, Object> recalculateResult(Long recordId);

    /**
     * 清理过期会话
     * 
     * @param expireHours 过期小时数
     * @return 清理数量
     */
    int cleanExpiredSessions(Integer expireHours);
}
