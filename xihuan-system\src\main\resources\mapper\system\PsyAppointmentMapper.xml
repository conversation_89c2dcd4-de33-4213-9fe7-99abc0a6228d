<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.xihuan.system.mapper.PsyAppointmentMapper">

    <!-- 基本字段映射 -->
    <resultMap id="BaseResultMap" type="PsyAppointment">
        <id column="appointment_id" property="appointmentId" />
        <result column="counselor_id" property="counselorId"/>
        <result column="appointment_date" property="appointmentDate" jdbcType="DATE"/>
        <result column="time_slot" property="timeSlot"/>
        <result column="week_day" property="weekDay"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>

    <!-- 删除指定日期之前的时段 -->
    <delete id="deleteBeforeDate">
        DELETE FROM psy_appointment
        WHERE appointment_date &lt; #{date}
    </delete>

    <!-- 删除指定日期之后的公共时段 -->
    <delete id="deletePublicSlotsAfterDate">
        DELETE FROM psy_appointment 
        WHERE counselor_id is null 
        AND appointment_date >= #{date}
    </delete>

    <!-- 删除指定咨询师在指定日期之后的时段 -->
    <delete id="deleteCounselorSlotsAfterDate">
        DELETE FROM psy_appointment 
        WHERE counselor_id = #{counselorId} 
        AND appointment_date >= #{date}
    </delete>

    <!-- 检查时段是否存在 -->
    <select id="existsSlot" resultType="boolean">
        SELECT CASE WHEN COUNT(1) > 0 THEN 1 ELSE 0 END
        FROM psy_appointment
        WHERE counselor_id = #{counselorId}
          AND DATE_FORMAT(appointment_date, '%Y-%m-%d') = #{date}
          AND time_slot = #{timeSlot}
          AND del_flag = 0
    </select>

    <!-- 检查某天是否已生成时段 -->
    <select id="existsDaySlots" resultType="boolean">
        SELECT COUNT(1) > 0
        FROM psy_appointment
        WHERE counselor_id = #{counselorId}
        AND DATE_FORMAT(appointment_date, '%Y-%m-%d') = #{date}
        LIMIT 1
    </select>

    <!-- 更新时段状态 -->
    <update id="updateSlotStatus">
        UPDATE psy_appointment
        SET status = #{status},
            update_time = NOW()
        WHERE counselor_id = #{counselorId}
          AND DATE_FORMAT(appointment_date, '%Y-%m-%d') = #{date}
          AND time_slot = #{timeSlot}
    </update>

    <!-- 插入新时段 -->
    <insert id="insert" parameterType="PsyAppointment" useGeneratedKeys="true" keyProperty="appointmentId">
        INSERT INTO psy_appointment (
            counselor_id,
            appointment_date,
            time_slot,
            week_day,
            status,
            create_time,
            del_flag
        ) VALUES (
            #{counselorId},
            #{appointmentDate},
            #{timeSlot},
            #{weekDay},
            #{status},
            #{createTime},
            #{delFlag}
        )
    </insert>

    <!-- 查询有效时段 -->
    <select id="selectByCounselorAndDate" resultMap="BaseResultMap">
        SELECT * FROM psy_appointment
        WHERE counselor_id = #{counselorId}
          AND appointment_date BETWEEN #{startDate} AND #{endDate}
          AND del_flag = 0
        ORDER BY appointment_date, time_slot
    </select>

    <!-- 查询公共时段 -->
    <select id="selectPublicSlots" resultMap="BaseResultMap">
        SELECT * FROM psy_appointment
        WHERE counselor_id IS NULL
          AND appointment_date BETWEEN #{startDate} AND #{endDate}
          AND del_flag = 0
        ORDER BY appointment_date, time_slot
    </select>

</mapper>