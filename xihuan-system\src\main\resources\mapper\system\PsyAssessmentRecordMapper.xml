<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="RecordResultMap" type="PsyAssessmentRecord">
        <id property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="sessionId" column="session_id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="duration" column="duration"/>
        <result property="totalScore" column="total_score"/>
        <result property="maxScore" column="max_score"/>
        <result property="percentage" column="percentage"/>
        <result property="status" column="status"/>
        <result property="resultLevel" column="result_level"/>
        <result property="resultDescription" column="result_description"/>
        <result property="suggestions" column="suggestions"/>
        <result property="dimensionScores" column="dimension_scores"/>
        <result property="isAnonymous" column="is_anonymous"/>
        <result property="ipAddress" column="ip_address"/>
        <result property="userAgent" column="user_agent"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 带详情的结果映射 -->
    <resultMap id="RecordWithDetailsMap" type="PsyAssessmentRecord" extends="RecordResultMap">
        <association property="user" javaType="SysUser">
            <id property="userId" column="u_user_id"/>
            <result property="userName" column="u_user_name"/>
            <result property="nickName" column="u_nick_name"/>
            <result property="email" column="u_email"/>
            <result property="phonenumber" column="u_phonenumber"/>
            <result property="avatar" column="u_avatar"/>
        </association>
        <association property="scale" javaType="PsyAssessmentScale">
            <id property="id" column="s_id"/>
            <result property="scaleName" column="s_scale_name"/>
            <result property="scaleCode" column="s_scale_code"/>
            <result property="description" column="s_description"/>
            <result property="questionCount" column="s_question_count"/>
            <result property="timeLimit" column="s_time_limit"/>
        </association>
    </resultMap>

    <!-- 查询测评记录列表 -->
    <select id="selectRecordList" parameterType="PsyAssessmentRecord" resultMap="RecordWithDetailsMap">
        SELECT r.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.email as u_email, u.phonenumber as u_phonenumber, u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name, s.scale_code as s_scale_code,
               s.description as s_description, s.question_count as s_question_count, s.time_limit as s_time_limit
        FROM psy_t_record r
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.del_flag = 0
        <if test="userId != null">
            AND r.user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND r.scale_id = #{scaleId}
        </if>
        <if test="status != null">
            AND r.status = #{status}
        </if>
        <if test="isAnonymous != null">
            AND r.is_anonymous = #{isAnonymous}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            AND r.create_time >= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND r.create_time &lt;= #{params.endTime}
        </if>
        ORDER BY r.create_time DESC
    </select>

    <!-- 根据ID查询测评记录 -->
    <select id="selectRecordById" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询测评记录详情 -->
    <select id="selectRecordWithDetails" parameterType="Long" resultMap="RecordWithDetailsMap">
        SELECT r.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.email as u_email, u.phonenumber as u_phonenumber, u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name, s.scale_code as s_scale_code,
               s.description as s_description, s.question_count as s_question_count, s.time_limit as s_time_limit
        FROM psy_t_record r
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON r.scale_id = s.id
        WHERE r.id = #{id} AND r.del_flag = 0
    </select>

    <!-- 根据会话ID查询测评记录 -->
    <select id="selectRecordBySessionId" parameterType="String" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record WHERE session_id = #{sessionId} AND del_flag = 0
    </select>

    <!-- 根据用户ID查询测评记录列表 -->
    <select id="selectRecordsByUserId" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据量表ID查询测评记录列表 -->
    <select id="selectRecordsByScaleId" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE scale_id = #{scaleId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 新增测评记录 -->
    <insert id="insertRecord" parameterType="PsyAssessmentRecord" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_record (
            user_id, scale_id, session_id, start_time, end_time, duration, total_score, max_score,
            percentage, status, result_level, result_description, suggestions, dimension_scores,
            is_anonymous, ip_address, user_agent, del_flag, create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{userId}, #{scaleId}, #{sessionId}, #{startTime}, #{endTime}, #{duration}, #{totalScore}, #{maxScore},
            #{percentage}, #{status}, #{resultLevel}, #{resultDescription}, #{suggestions}, #{dimensionScores},
            #{isAnonymous}, #{ipAddress}, #{userAgent}, #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate(), #{remark}
        )
    </insert>

    <!-- 修改测评记录 -->
    <update id="updateRecord" parameterType="PsyAssessmentRecord">
        UPDATE psy_t_record
        <set>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="sessionId != null">session_id = #{sessionId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="totalScore != null">total_score = #{totalScore},</if>
            <if test="maxScore != null">max_score = #{maxScore},</if>
            <if test="percentage != null">percentage = #{percentage},</if>
            <if test="status != null">status = #{status},</if>
            <if test="resultLevel != null">result_level = #{resultLevel},</if>
            <if test="resultDescription != null">result_description = #{resultDescription},</if>
            <if test="suggestions != null">suggestions = #{suggestions},</if>
            <if test="dimensionScores != null">dimension_scores = #{dimensionScores},</if>
            <if test="isAnonymous != null">is_anonymous = #{isAnonymous},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="userAgent != null">user_agent = #{userAgent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除测评记录 -->
    <update id="deleteRecordById" parameterType="Long">
        UPDATE psy_t_record SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除测评记录 -->
    <update id="deleteRecordByIds" parameterType="Long">
        UPDATE psy_t_record SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询用户在指定量表的最新测评记录 -->
    <select id="selectLatestRecordByUserAndScale" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE user_id = #{userId} AND scale_id = #{scaleId} AND del_flag = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 查询用户在指定量表的测评次数 -->
    <select id="countRecordsByUserAndScale" resultType="int">
        SELECT COUNT(1) FROM psy_t_record 
        WHERE user_id = #{userId} AND scale_id = #{scaleId} AND del_flag = 0
    </select>

    <!-- 查询用户的测评统计 -->
    <select id="selectUserRecordStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completed_count,
            COUNT(CASE WHEN status = 0 THEN 1 END) as in_progress_count,
            COUNT(CASE WHEN status = 2 THEN 1 END) as interrupted_count,
            AVG(CASE WHEN status = 1 THEN percentage END) as avg_percentage,
            MAX(CASE WHEN status = 1 THEN percentage END) as max_percentage,
            AVG(CASE WHEN status = 1 THEN duration END) as avg_duration
        FROM psy_t_record
        WHERE user_id = #{userId} AND del_flag = 0
    </select>

    <!-- 查询量表的测评统计 -->
    <select id="selectScaleRecordStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completed_count,
            COUNT(DISTINCT user_id) as user_count,
            AVG(CASE WHEN status = 1 THEN percentage END) as avg_percentage,
            AVG(CASE WHEN status = 1 THEN duration END) as avg_duration,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as today_count
        FROM psy_t_record
        WHERE scale_id = #{scaleId} AND del_flag = 0
    </select>

    <!-- 查询进行中的测评记录 -->
    <select id="selectInProgressRecords" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE user_id = #{userId} AND status = 0 AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询已完成的测评记录 -->
    <select id="selectCompletedRecords" parameterType="Long" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE user_id = #{userId} AND status = 1 AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询指定时间范围的测评记录 -->
    <select id="selectRecordsByTimeRange" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE create_time BETWEEN #{startTime} AND #{endTime} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询今日测评记录 -->
    <select id="selectTodayRecords" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE DATE(create_time) = CURDATE() AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询本周测评记录 -->
    <select id="selectWeekRecords" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE YEARWEEK(create_time) = YEARWEEK(NOW()) AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询本月测评记录 -->
    <select id="selectMonthRecords" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE YEAR(create_time) = YEAR(NOW()) AND MONTH(create_time) = MONTH(NOW()) AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 更新测评进度 -->
    <update id="updateProgress">
        UPDATE psy_t_record 
        SET current_question_no = #{currentQuestionNo}, progress = #{progress}, update_time = sysdate()
        WHERE session_id = #{sessionId}
    </update>

    <!-- 完成测评 -->
    <update id="completeRecord">
        UPDATE psy_t_record 
        SET end_time = #{endTime}, duration = #{duration}, total_score = #{totalScore}, 
            percentage = #{percentage}, result_level = #{resultLevel}, result_description = #{resultDescription},
            suggestions = #{suggestions}, dimension_scores = #{dimensionScores}, status = 1, update_time = sysdate()
        WHERE session_id = #{sessionId}
    </update>

    <!-- 中断测评 -->
    <update id="interruptRecord" parameterType="String">
        UPDATE psy_t_record SET status = 2, update_time = sysdate() WHERE session_id = #{sessionId}
    </update>

    <!-- 查询测评排行榜 -->
    <select id="selectRecordRanking" resultMap="RecordWithDetailsMap">
        SELECT r.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.avatar as u_avatar
        FROM psy_t_record r
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        WHERE r.scale_id = #{scaleId} AND r.status = 1 AND r.del_flag = 0
        ORDER BY r.percentage DESC, r.duration ASC
        LIMIT #{limit}
    </select>

    <!-- 查询用户测评历史 -->
    <select id="selectUserTestHistory" resultMap="RecordResultMap">
        SELECT * FROM psy_t_record 
        WHERE user_id = #{userId} AND scale_id = #{scaleId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 统计测评记录数量 -->
    <select id="countRecords" parameterType="PsyAssessmentRecord" resultType="int">
        SELECT COUNT(1) FROM psy_t_record
        WHERE del_flag = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND scale_id = #{scaleId}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 查询测评趋势统计 -->
    <select id="selectRecordTrend" parameterType="Integer" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(1) as total_count,
            COUNT(CASE WHEN status = 1 THEN 1 END) as completed_count
        FROM psy_t_record
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) AND del_flag = 0
        GROUP BY DATE(create_time)
        ORDER BY DATE(create_time)
    </select>
</mapper>
