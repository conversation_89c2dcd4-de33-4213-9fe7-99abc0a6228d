package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 量表基础信息表 psy_t_scale
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTScale extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 量表唯一ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表名称 */
    @Excel(name = "量表名称")
    @NotBlank(message = "量表名称不能为空")
    @Size(max = 100, message = "量表名称不能超过100个字符")
    private String name;

    /** 量表编码 */
    @Excel(name = "量表编码")
    @NotBlank(message = "量表编码不能为空")
    @Size(max = 50, message = "量表编码不能超过50个字符")
    private String code;

    /** 量表描述 */
    @Excel(name = "量表描述")
    private String description;

    /** 量表介绍 */
    @Excel(name = "量表介绍")
    private String introduction;

    /** 总题数 */
    @Excel(name = "总题数")
    @NotNull(message = "总题数不能为空")
    @Min(value = 1, message = "总题数不能小于1")
    private Integer questionCount;

    /** 计分类型 */
    @Excel(name = "计分类型", readConverterExp = "LIKERT=李克特量表,BINARY=二分法,COMPOSITE=复合计分")
    @NotBlank(message = "计分类型不能为空")
    private String scoringType;

    /** 预估时长 */
    @Excel(name = "预估时长")
    @Size(max = 20, message = "预估时长不能超过20个字符")
    private String duration;

    /** 常模均值 */
    @Excel(name = "常模均值")
    private BigDecimal normMean;

    /** 常模标准差 */
    @Excel(name = "常模标准差")
    private BigDecimal normSd;

    /** 适用年龄 */
    @Excel(name = "适用年龄")
    @Size(max = 50, message = "适用年龄不能超过50个字符")
    private String applicableAge;

    /** 量表封面图片 */
    @Excel(name = "量表封面图片")
    @Size(max = 500, message = "量表封面图片不能超过500个字符")
    private String imageUrl;

    /** 价格 */
    @Excel(name = "价格")
    @DecimalMin(value = "0.00", message = "价格不能小于0")
    private BigDecimal price;

    /** 付费模式(0免费 1付费) */
    @Excel(name = "付费模式", readConverterExp = "0=免费,1=付费")
    private Integer payMode;

    /** 付费阶段(0测试 1报告) */
    @Excel(name = "付费阶段", readConverterExp = "0=测试,1=报告")
    private Integer payPhase;

    /** 免费VIP等级 */
    @Excel(name = "免费VIP等级")
    private Integer freeVipLevel;

    /** 免费报告层级 */
    @Excel(name = "免费报告层级")
    private Integer freeReportLevel;

    /** 付费报告层级 */
    @Excel(name = "付费报告层级")
    private Integer paidReportLevel;

    /** 企业ID */
    @Excel(name = "企业ID")
    private Long enterpriseId;

    /** 状态(0停用 1启用) */
    @Excel(name = "状态", readConverterExp = "0=停用,1=启用")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /** 排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 搜索关键词 */
    @Excel(name = "搜索关键词")
    private String searchKeywords;

    /** 被搜索次数 */
    @Excel(name = "被搜索次数")
    private Integer searchCount;

    /** 查看次数 */
    @Excel(name = "查看次数")
    private Integer viewCount;

    /** 删除标志 */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private String delFlag;

    // 关联对象
    /** 题目列表 */
    private List<PsyTQuestion> questions;

    /** 分量表列表 */
    private List<PsyTSubscale> subscales;

    /** 计分规则列表 */
    private List<PsyTScoringRule> scoringRules;

    /** 功能损害评估列表 */
    private List<PsyTFunctionImpairment> functionImpairments;

    /** 企业信息 */
    private PsyTEnterprise enterprise;

    // 扩展字段
    /** 用户是否已购买 */
    private Boolean purchased;

    /** 用户测评记录 */
    private PsyTAssessmentRecord userRecord;

    /** 是否可以测评 */
    private Boolean canTest;

    /** 剩余测评次数 */
    private Integer remainingTests;

    /** 最近测评时间 */
    private String lastTestTime;

    /** 最高分数 */
    private BigDecimal bestScore;

    /** 用户测评次数 */
    private Integer userTestCount;

    // 统计字段
    /** 今日测试次数 */
    private Integer todayTestCount;

    /** 本周测试次数 */
    private Integer weekTestCount;

    /** 本月测试次数 */
    private Integer monthTestCount;

    /** 完成率 */
    private BigDecimal completionRate;

    /** 平均用时 */
    private Integer avgDuration;

    // 常量定义
    /** 状态：停用 */
    public static final Integer STATUS_DISABLED = 0;
    
    /** 状态：启用 */
    public static final Integer STATUS_ENABLED = 1;

    /** 付费模式：免费 */
    public static final Integer PAY_MODE_FREE = 0;
    
    /** 付费模式：付费 */
    public static final Integer PAY_MODE_PAID = 1;

    /** 付费阶段：测试 */
    public static final Integer PAY_PHASE_TEST = 0;
    
    /** 付费阶段：报告 */
    public static final Integer PAY_PHASE_REPORT = 1;

    /** 计分类型：李克特量表 */
    public static final String SCORING_TYPE_LIKERT = "LIKERT";
    
    /** 计分类型：二分法 */
    public static final String SCORING_TYPE_BINARY = "BINARY";
    
    /** 计分类型：复合计分 */
    public static final String SCORING_TYPE_COMPOSITE = "COMPOSITE";

    /** 删除标志：正常 */
    public static final String DEL_FLAG_NORMAL = "0";
    
    /** 删除标志：删除 */
    public static final String DEL_FLAG_DELETED = "1";

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "停用";
            case 1: return "启用";
            default: return "未知";
        }
    }

    /**
     * 获取付费模式描述
     */
    public String getPayModeDesc() {
        if (payMode == null) return "";
        switch (payMode) {
            case 0: return "免费";
            case 1: return "付费";
            default: return "未知";
        }
    }

    /**
     * 获取付费阶段描述
     */
    public String getPayPhaseDesc() {
        if (payPhase == null) return "";
        switch (payPhase) {
            case 0: return "测试";
            case 1: return "报告";
            default: return "未知";
        }
    }

    /**
     * 是否启用
     */
    public boolean isEnabled() {
        return STATUS_ENABLED.equals(status);
    }

    /**
     * 是否免费
     */
    public boolean isFree() {
        return PAY_MODE_FREE.equals(payMode);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }
}
