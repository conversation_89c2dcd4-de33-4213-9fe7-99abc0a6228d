package com.xihuan.web.controller.miniapp.user;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyAssessmentOrder;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;
import com.xihuan.system.service.IPsyAssessmentOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 小程序用户端心理测评订单Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/order")
public class MiniAppUserOrderController extends BaseController {
    
    @Autowired
    private IPsyAssessmentOrderService orderService;

    /**
     * 查询订单列表
     */
    @GetMapping("/list")
    public TableDataInfo listOrders() {
        startPage();
        PsyAssessmentOrder order = new PsyAssessmentOrder();
        order.setUserId(getUserId());
        List<PsyAssessmentDTO.OrderDTO> list = orderService.getOrderListDTO(order);
        return getDataTable(list);
    }

    /**
     * 查询订单详情
     */
    @GetMapping("/{id}")
    public AjaxResult getOrderDetail(@PathVariable("id") Long id) {
        PsyAssessmentDTO.OrderDTO order = orderService.getOrderDTO(id);
        
        if (order == null) {
            return error("订单不存在");
        }
        
        // 检查是否是当前用户的订单
        if (!getUserId().equals(order.getUserId())) {
            return error("无权查看该订单");
        }
        
        return success(order);
    }

    /**
     * 创建订单
     */
    @Log(title = "心理测评订单", businessType = BusinessType.INSERT)
    @PostMapping("/create")
    public AjaxResult createOrder(@RequestBody @Valid PsyAssessmentDTO.CreateOrderRequest request) {
        PsyAssessmentOrder order = orderService.createOrder(getUserId(), request.getScaleId(), request.getCouponId());
        return success(order);
    }

    /**
     * 支付订单
     */
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PostMapping("/pay")
    public AjaxResult payOrder(@RequestBody @Valid PsyAssessmentDTO.PayOrderRequest request) {
        // 检查是否是当前用户的订单
        PsyAssessmentOrder order = orderService.selectOrderByOrderNo(request.getOrderNo());
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!getUserId().equals(order.getUserId())) {
            return error("无权操作该订单");
        }
        
        int result = orderService.payOrder(request.getOrderNo(), request.getPaymentMethod());
        
        return toAjax(result);
    }

    /**
     * 取消订单
     */
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{orderNo}")
    public AjaxResult cancelOrder(@PathVariable("orderNo") String orderNo) {
        // 检查是否是当前用户的订单
        PsyAssessmentOrder order = orderService.selectOrderByOrderNo(orderNo);
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!getUserId().equals(order.getUserId())) {
            return error("无权操作该订单");
        }
        
        int result = orderService.cancelOrder(orderNo);
        
        return toAjax(result);
    }

    /**
     * 申请退款
     */
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PostMapping("/refund")
    public AjaxResult applyRefund(@RequestBody @Valid PsyAssessmentDTO.RefundRequest request) {
        // 检查是否是当前用户的订单
        PsyAssessmentOrder order = orderService.selectOrderByOrderNo(request.getOrderNo());
        if (order == null) {
            return error("订单不存在");
        }
        
        if (!getUserId().equals(order.getUserId())) {
            return error("无权操作该订单");
        }
        
        int result = orderService.applyRefund(request.getOrderNo(), request.getRefundReason());
        
        return toAjax(result);
    }

    /**
     * 查询待支付订单
     */
    @GetMapping("/pending")
    public TableDataInfo listPendingOrders() {
        startPage();
        List<PsyAssessmentOrder> list = orderService.selectPendingOrders(getUserId());
        return getDataTable(list);
    }

    /**
     * 查询已支付订单
     */
    @GetMapping("/paid")
    public TableDataInfo listPaidOrders() {
        startPage();
        List<PsyAssessmentOrder> list = orderService.selectPaidOrders(getUserId());
        return getDataTable(list);
    }

    /**
     * 查询已完成订单
     */
    @GetMapping("/completed")
    public TableDataInfo listCompletedOrders() {
        startPage();
        List<PsyAssessmentOrder> list = orderService.selectCompletedOrders(getUserId());
        return getDataTable(list);
    }

    /**
     * 查询已取消订单
     */
    @GetMapping("/cancelled")
    public TableDataInfo listCancelledOrders() {
        startPage();
        List<PsyAssessmentOrder> list = orderService.selectCancelledOrders(getUserId());
        return getDataTable(list);
    }

    /**
     * 查询用户订单统计
     */
    @GetMapping("/stats")
    public AjaxResult getUserOrderStats() {
        return success(orderService.selectUserOrderStats(getUserId()));
    }

    /**
     * 检查用户是否已购买量表
     */
    @GetMapping("/checkPurchased/{scaleId}")
    public AjaxResult checkUserPurchased(@PathVariable("scaleId") Long scaleId) {
        boolean purchased = orderService.checkUserPurchased(getUserId(), scaleId);
        return success(purchased);
    }
}
