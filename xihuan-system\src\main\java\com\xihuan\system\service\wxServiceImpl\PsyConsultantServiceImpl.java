package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.vo.ConsultantSimpleVO;
import com.xihuan.system.mapper.PsyConsultantMapper;
import com.xihuan.system.service.wxService.PsyCategoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 心理咨询师服务实现类
 * 实现咨询师信息的增删改查、状态管理、分步保存等业务逻辑
 *
 * <AUTHOR>
 */
@Service
public class PsyConsultantServiceImpl implements PsyCategoryService.PsyConsultantService {

    /**
     * 咨询师数据访问接口
     */
    @Autowired
    private PsyConsultantMapper consultantMapper;

    /**
     * 新增咨询师
     * 创建新的咨询师记录，使用事务确保数据一致性
     *
     * @param consultant 咨询师信息对象
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int insertConsultant(PsyConsultant consultant) {
        return consultantMapper.insertConsultant(consultant);
    }

    /**
     * 更新咨询师信息
     * 更新现有咨询师的所有信息，使用事务确保数据一致性
     *
     * @param consultant 咨询师信息对象
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int updateConsultant(PsyConsultant consultant) {
        return consultantMapper.updateConsultant(consultant);
    }

    /**
     * 根据ID查询咨询师（包含用户信息）
     * 获取咨询师的基本信息以及关联的用户账号信息
     *
     * @param id 咨询师ID
     * @return 咨询师信息对象
     */
    @Override
    public PsyConsultant getConsultantWithUserById(Long id) {
        return consultantMapper.selectConsultantWithUserById(id);
    }

    /**
     * 根据用户ID查询咨询师
     * 通过系统用户ID获取对应的咨询师信息
     *
     * @param userId 用户ID
     * @return 咨询师信息对象
     */
    @Override
    public PsyConsultant getConsultantByUserId(Long userId) {
        return consultantMapper.selectConsultantByUserId(userId);
    }

    /**
     * 分页查询咨询师列表
     * 根据多个条件筛选并分页返回咨询师列表
     * 
     * @param name 咨询师姓名，模糊匹配
     * @param workStatus 工作状态（在线、离线、休假等）
     * @param auditStatus 审核状态（待审核、已通过、未通过等）
     * @param minPrice 最低价格，用于价格区间筛选
     * @param maxPrice 最高价格，用于价格区间筛选
     * @return 咨询师列表
     */
    @Override
    public List<PsyConsultant> listConsultantsByPage(String name, String workStatus, 
            String auditStatus, Integer minPrice, Integer maxPrice) {
        // 构建查询参数
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("workStatus", workStatus);
        params.put("auditStatus", auditStatus);
        params.put("minPrice", minPrice);
        params.put("maxPrice", maxPrice);
        return consultantMapper.selectConsultantListByPage(params);
    }

    /**
     * 查询所有可用的咨询师（包含详细信息）
     * 获取所有状态正常且可以提供服务的咨询师列表
     *
     * @return 可用咨询师列表
     */
    @Override
    public List<PsyConsultant> listAllAvailableConsultants() {
        return consultantMapper.selectAllConsultantsWithDetails();
    }

    /**
     * 根据条件查询咨询师列表
     * 按照姓名和擅长领域筛选咨询师
     * 
     * @param name 咨询师姓名，模糊匹配
     * @param field 擅长领域
     * @return 符合条件的咨询师列表
     */
    @Override
    public List<PsyConsultant> listConsultants(String name, String field) {
        Map<String, Object> params = new HashMap<>();
        params.put("name", name);
        params.put("field", field);
        return consultantMapper.selectAllConsultants(params);
    }

    /**
     * 根据ID查询咨询师完整信息
     * 获取咨询师的所有信息，包括基本信息、服务信息、资质证书、教育经历等
     *
     * @param id 咨询师ID
     * @return 咨询师完整信息对象
     */
    @Override
    public PsyConsultant getConsultantFullDetails(Long id) {
        return consultantMapper.selectConsultantFullDetails(id);
    }

    /**
     * 删除咨询师
     * 根据ID删除指定咨询师的所有信息，使用事务确保数据一致性
     *
     * @param id 咨询师ID
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int deleteConsultant(Long id) {
        return consultantMapper.deleteConsultantById(id);
    }

    /**
     * 批量删除咨询师
     * 批量删除多个咨询师的信息，使用事务确保数据一致性
     *
     * @param ids 咨询师ID数组
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int deleteConsultants(Long[] ids) {
        return consultantMapper.deleteConsultantByIds(ids);
    }

    /**
     * 更新咨询师工作状态
     * 修改咨询师的在线状态，使用事务确保数据一致性
     *
     * @param id 咨询师ID
     * @param workStatus 工作状态（在线、离线、休假等）
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int updateConsultantWorkStatus(Long id, String workStatus) {
        return consultantMapper.updateConsultantWorkStatus(id, workStatus);
    }

    /**
     * 更新咨询师审核状态
     * 修改咨询师的审核状态，使用事务确保数据一致性
     *
     * @param id 咨询师ID
     * @param auditStatus 审核状态（待审核、已通过、未通过等）
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int updateConsultantAuditStatus(Long id, String auditStatus) {
        return consultantMapper.updateConsultantAuditStatus(id, auditStatus);
    }

    /**
     * 保存咨询师基本信息
     * 分步保存功能之一，用于保存基础个人信息
     * 根据是否存在ID判断是新增还是更新操作
     *
     * @param consultant 包含基本信息的咨询师对象
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int saveBasicInfo(PsyConsultant consultant) {
        // 保存基本信息
        if (consultant.getId() == null) {
            return consultantMapper.insertConsultant(consultant);
        } else {
            return consultantMapper.updateConsultant(consultant);
        }
    }

    /**
     * 保存咨询师服务信息
     * 分步保存功能之一，用于保存服务相关信息
     * 包括服务方式、咨询流派、擅长领域、特色方式、可用时段等
     *
     * @param consultant 包含服务信息的咨询师对象
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int saveServiceInfo(PsyConsultant consultant) {
        // 更新服务相关信息
        PsyConsultant updateConsultant = new PsyConsultant();
        updateConsultant.setId(consultant.getId());
        updateConsultant.setServiceMethods(consultant.getServiceMethods());
        updateConsultant.setConsultStyles(consultant.getConsultStyles());
        updateConsultant.setExpertises(consultant.getExpertises());
        updateConsultant.setSpecialMethods(consultant.getSpecialMethods());
        updateConsultant.setAvailableSlots(consultant.getAvailableSlots());
        updateConsultant.setPrice(consultant.getPrice());
        updateConsultant.setMinFee(consultant.getMinFee());
        updateConsultant.setMaxFee(consultant.getMaxFee());
        updateConsultant.setCanTeach(consultant.getCanTeach());
        updateConsultant.setCanTravel(consultant.getCanTravel());
        updateConsultant.setAvailableTime(consultant.getAvailableTime());
        return consultantMapper.updateConsultant(updateConsultant);
    }

    /**
     * 保存咨询师资质信息
     * 分步保存功能之一，用于保存专业资质相关信息
     * 包括资质证书、职称、案例数量、服务时长等
     *
     * @param consultant 包含资质信息的咨询师对象
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int saveQualificationInfo(PsyConsultant consultant) {
        // 更新资质相关信息
        PsyConsultant updateConsultant = new PsyConsultant();
        updateConsultant.setId(consultant.getId());
        updateConsultant.setCertificates(consultant.getCertificates());
        updateConsultant.setPersonalTitle(consultant.getPersonalTitle());
        updateConsultant.setTotalCases(consultant.getTotalCases());
        updateConsultant.setServiceCount(consultant.getServiceCount());
        updateConsultant.setServiceHours(consultant.getServiceHours());
        updateConsultant.setWorkCase(consultant.getWorkCase());
        return consultantMapper.updateConsultant(updateConsultant);
    }

    /**
     * 保存咨询师教育和培训信息
     * 分步保存功能之一，用于保存教育背景、培训经历等信息
     * 包括学历信息、督导经历、培训证书等
     *
     * @param consultant 包含教育培训信息的咨询师对象
     * @return 影响的行数
     */
    @Override
    @Transactional
    public int saveEducationAndTraining(PsyConsultant consultant) {
        // 更新教育和培训相关信息
        PsyConsultant updateConsultant = new PsyConsultant();
        updateConsultant.setId(consultant.getId());
        updateConsultant.setEducations(consultant.getEducations());
        updateConsultant.setSupervisions(consultant.getSupervisions());
        updateConsultant.setTrainings(consultant.getTrainings());
        return consultantMapper.updateConsultant(updateConsultant);
    }

    /**
     * 查询所有咨询师简单信息列表
     *
     * @return 咨询师简单信息列表
     */
    @Override
    public List<ConsultantSimpleVO> listAllSimple() {
        return consultantMapper.selectAllSimple();
    }
}