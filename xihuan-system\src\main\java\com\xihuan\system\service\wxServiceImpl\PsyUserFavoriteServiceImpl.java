//package com.xihuan.system.service.wxServiceImpl;
//
//import com.xihuan.common.core.domain.consultant.PsyConsultant;
//import com.xihuan.common.core.domain.entity.PsyProduct;
//import com.xihuan.common.core.domain.entity.PsyUserFavorite;
//import com.xihuan.system.mapper.PsyConsultantMapper;
//import com.xihuan.system.mapper.PsyProductMapper;
//import com.xihuan.system.mapper.PsyUserFavoriteMapper;
//import com.xihuan.system.service.wxService.IPsyUserFavoriteService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//import org.springframework.transaction.annotation.Transactional;
//
//import java.util.ArrayList;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
///**
// * 用户收藏服务实现类
// */
//@Service
//public class PsyUserFavoriteServiceImpl implements IPsyUserFavoriteService {
//
//    @Autowired
//    private PsyUserFavoriteMapper favoriteMapper;
//
//    @Autowired
//    private PsyConsultantMapper consultantMapper;
//
//    @Autowired
//    private PsyProductMapper productMapper;
//
//    @Override
//    @Transactional
//    public PsyUserFavorite addFavorite(PsyUserFavorite favorite) {
//        favoriteMapper.insertFavorite(favorite);
//        // 插入成功后favorite对象会自动获得新生成的ID
//        return favorite;
//    }
//
//    @Override
//    @Transactional
//    public int removeFavorite(Long[] favoriteIds) {
//        return favoriteMapper.deleteFavoriteByIds(favoriteIds);
//    }
//
//    @Override
//    public List<PsyUserFavorite> getFavorites(PsyUserFavorite favorite) {
//        return favoriteMapper.selectFavoriteList(favorite);
//    }
//
//    @Override
//    public List<Map<String, Object>> getFavoritesWithDetails(PsyUserFavorite favorite) {
//        // 1. 获取收藏列表
//        List<PsyUserFavorite> favorites = favoriteMapper.selectFavoriteList(favorite);
//        List<Map<String, Object>> result = new ArrayList<>();
//
//        // 2. 遍历收藏列表,根据类型获取详细信息
//        for (PsyUserFavorite item : favorites) {
//            Map<String, Object> detailMap = new HashMap<>();
//            detailMap.put("favoriteId", item.getFavoriteId());
//            detailMap.put("userId", item.getUserId());
//            detailMap.put("targetType", item.getTargetType());
//            detailMap.put("createTime", item.getCreateTime());
//
//            if (item.getTargetType() == 1) { // 咨询师
//                PsyConsultant consultant = consultantMapper.selectConsultantFullDetails(item.getCounselorId());
//                if (consultant != null) {
//                    detailMap.put("counselorId", consultant.getId());
//                    detailMap.put("name", consultant.getName());
//                    detailMap.put("image", consultant.getImageUrl());
//                    detailMap.put("price", consultant.getPrice());
//                    detailMap.put("address", consultant.getAddress());
//                    detailMap.put("counselorLevel", consultant.getPersonalTitle());
//                    detailMap.put("province", consultant.getProvince());
//                    detailMap.put("city", consultant.getCity());
//                    detailMap.put("district", consultant.getDistrict());
//                    detailMap.put("serviceMethods", consultant.getServiceMethods());
//                    detailMap.put("consultStyles", consultant.getConsultStyles());
//                    detailMap.put("personalIntro", consultant.getPersonalIntro());
//                    detailMap.put("startYear", consultant.getStartYear());
//                    detailMap.put("serviceCount", consultant.getServiceCount());
//                }
//            } else if (item.getTargetType() == 2) { // 产品
//                PsyProduct product = productMapper.selectProductWithDetails(item.getProductId());
//                if (product != null) {
//                    detailMap.put("productId", product.getProductId());
//                    detailMap.put("name", product.getProductName());
//                    detailMap.put("image", product.getProductImage());
//                    detailMap.put("price", product.getDiscountPrice());
//                    detailMap.put("originalPrice", product.getOriginalPrice());
//                    detailMap.put("serviceDirection", product.getServiceDirection());
//                    detailMap.put("serviceItems", product.getServiceItems());
//                    detailMap.put("needAppointment", product.getNeedAppointment());
//                    detailMap.put("validityPeriod", product.getValidityPeriod());
//                }
//            }
//
//            result.add(detailMap);
//        }
//
//        return result;
//    }
//
//    @Override
//    public PsyUserFavorite checkFavorites(Long userId, Integer targetType, Long counselorId, Long productId) {
//        return favoriteMapper.checkFavorite(userId, targetType, counselorId, productId);
//    }
//}