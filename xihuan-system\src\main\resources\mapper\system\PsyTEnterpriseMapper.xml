<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTEnterpriseMapper">

    <!-- 结果映射 -->
    <resultMap id="EnterpriseResultMap" type="PsyTEnterprise">
        <id property="id" column="id"/>
        <result property="enterpriseCode" column="enterprise_code"/>
        <result property="enterpriseName" column="enterprise_name"/>
        <result property="enterpriseType" column="enterprise_type"/>
        <result property="industry" column="industry"/>
        <result property="scale" column="scale"/>
        <result property="employeeCount" column="employee_count"/>
        <result property="contactPerson" column="contact_person"/>
        <result property="contactPhone" column="contact_phone"/>
        <result property="contactEmail" column="contact_email"/>
        <result property="address" column="address"/>
        <result property="province" column="province"/>
        <result property="city" column="city"/>
        <result property="district" column="district"/>
        <result property="businessLicense" column="business_license"/>
        <result property="legalRepresentative" column="legal_representative"/>
        <result property="registeredCapital" column="registered_capital"/>
        <result property="establishmentDate" column="establishment_date"/>
        <result property="servicePackage" column="service_package"/>
        <result property="contractStartDate" column="contract_start_date"/>
        <result property="contractEndDate" column="contract_end_date"/>
        <result property="assessmentQuota" column="assessment_quota"/>
        <result property="usedAssessmentCount" column="used_assessment_count"/>
        <result property="remainingAssessmentCount" column="remaining_assessment_count"/>
        <result property="contractAmount" column="contract_amount"/>
        <result property="paidAmount" column="paid_amount"/>
        <result property="unpaidAmount" column="unpaid_amount"/>
        <result property="lastPaymentDate" column="last_payment_date"/>
        <result property="nextPaymentDate" column="next_payment_date"/>
        <result property="certificationStatus" column="certification_status"/>
        <result property="certificationDate" column="certification_date"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 详细结果映射 -->
    <resultMap id="EnterpriseDetailMap" type="PsyTEnterprise" extends="EnterpriseResultMap">
        <collection property="departments" ofType="PsyTEnterpriseDepartment" column="id" select="com.xihuan.system.mapper.PsyTEnterpriseDepartmentMapper.selectDepartmentsByEnterpriseId"/>
        <collection property="assessmentPlans" ofType="PsyTEnterpriseAssessmentPlan" column="id" select="com.xihuan.system.mapper.PsyTEnterpriseAssessmentPlanMapper.selectPlansByEnterpriseId"/>
    </resultMap>

    <!-- 查询企业列表 -->
    <select id="selectEnterpriseList" parameterType="PsyTEnterprise" resultMap="EnterpriseResultMap">
        SELECT * FROM psy_t_enterprise
        WHERE del_flag = '0'
        <if test="enterpriseName != null and enterpriseName != ''">
            AND enterprise_name LIKE CONCAT('%', #{enterpriseName}, '%')
        </if>
        <if test="enterpriseCode != null and enterpriseCode != ''">
            AND enterprise_code LIKE CONCAT('%', #{enterpriseCode}, '%')
        </if>
        <if test="enterpriseType != null">
            AND enterprise_type = #{enterpriseType}
        </if>
        <if test="industry != null and industry != ''">
            AND industry = #{industry}
        </if>
        <if test="scale != null and scale != ''">
            AND scale = #{scale}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        <if test="certificationStatus != null">
            AND certification_status = #{certificationStatus}
        </if>
        <if test="province != null and province != ''">
            AND province = #{province}
        </if>
        <if test="city != null and city != ''">
            AND city = #{city}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询企业 -->
    <select id="selectEnterpriseById" parameterType="Long" resultMap="EnterpriseResultMap">
        SELECT * FROM psy_t_enterprise WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 查询企业详情 -->
    <select id="selectEnterpriseWithDetails" parameterType="Long" resultMap="EnterpriseDetailMap">
        SELECT * FROM psy_t_enterprise WHERE id = #{id} AND del_flag = '0'
    </select>

    <!-- 根据企业编码查询企业 -->
    <select id="selectEnterpriseByCode" parameterType="String" resultMap="EnterpriseResultMap">
        SELECT * FROM psy_t_enterprise WHERE enterprise_code = #{enterpriseCode} AND del_flag = '0'
    </select>

    <!-- 新增企业 -->
    <insert id="insertEnterprise" parameterType="PsyTEnterprise" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_enterprise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterpriseCode != null and enterpriseCode != ''">enterprise_code,</if>
            <if test="enterpriseName != null and enterpriseName != ''">enterprise_name,</if>
            <if test="enterpriseType != null">enterprise_type,</if>
            <if test="industry != null">industry,</if>
            <if test="scale != null">scale,</if>
            <if test="employeeCount != null">employee_count,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="contactEmail != null">contact_email,</if>
            <if test="address != null">address,</if>
            <if test="province != null">province,</if>
            <if test="city != null">city,</if>
            <if test="district != null">district,</if>
            <if test="businessLicense != null">business_license,</if>
            <if test="legalRepresentative != null">legal_representative,</if>
            <if test="registeredCapital != null">registered_capital,</if>
            <if test="establishmentDate != null">establishment_date,</if>
            <if test="servicePackage != null">service_package,</if>
            <if test="contractStartDate != null">contract_start_date,</if>
            <if test="contractEndDate != null">contract_end_date,</if>
            <if test="assessmentQuota != null">assessment_quota,</if>
            <if test="usedAssessmentCount != null">used_assessment_count,</if>
            <if test="remainingAssessmentCount != null">remaining_assessment_count,</if>
            <if test="contractAmount != null">contract_amount,</if>
            <if test="paidAmount != null">paid_amount,</if>
            <if test="unpaidAmount != null">unpaid_amount,</if>
            <if test="lastPaymentDate != null">last_payment_date,</if>
            <if test="nextPaymentDate != null">next_payment_date,</if>
            <if test="certificationStatus != null">certification_status,</if>
            <if test="certificationDate != null">certification_date,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterpriseCode != null and enterpriseCode != ''">#{enterpriseCode},</if>
            <if test="enterpriseName != null and enterpriseName != ''">#{enterpriseName},</if>
            <if test="enterpriseType != null">#{enterpriseType},</if>
            <if test="industry != null">#{industry},</if>
            <if test="scale != null">#{scale},</if>
            <if test="employeeCount != null">#{employeeCount},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="contactEmail != null">#{contactEmail},</if>
            <if test="address != null">#{address},</if>
            <if test="province != null">#{province},</if>
            <if test="city != null">#{city},</if>
            <if test="district != null">#{district},</if>
            <if test="businessLicense != null">#{businessLicense},</if>
            <if test="legalRepresentative != null">#{legalRepresentative},</if>
            <if test="registeredCapital != null">#{registeredCapital},</if>
            <if test="establishmentDate != null">#{establishmentDate},</if>
            <if test="servicePackage != null">#{servicePackage},</if>
            <if test="contractStartDate != null">#{contractStartDate},</if>
            <if test="contractEndDate != null">#{contractEndDate},</if>
            <if test="assessmentQuota != null">#{assessmentQuota},</if>
            <if test="usedAssessmentCount != null">#{usedAssessmentCount},</if>
            <if test="remainingAssessmentCount != null">#{remainingAssessmentCount},</if>
            <if test="contractAmount != null">#{contractAmount},</if>
            <if test="paidAmount != null">#{paidAmount},</if>
            <if test="unpaidAmount != null">#{unpaidAmount},</if>
            <if test="lastPaymentDate != null">#{lastPaymentDate},</if>
            <if test="nextPaymentDate != null">#{nextPaymentDate},</if>
            <if test="certificationStatus != null">#{certificationStatus},</if>
            <if test="certificationDate != null">#{certificationDate},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <!-- 修改企业 -->
    <update id="updateEnterprise" parameterType="PsyTEnterprise">
        UPDATE psy_t_enterprise
        <trim prefix="SET" suffixOverrides=",">
            <if test="enterpriseCode != null and enterpriseCode != ''">enterprise_code = #{enterpriseCode},</if>
            <if test="enterpriseName != null and enterpriseName != ''">enterprise_name = #{enterpriseName},</if>
            <if test="enterpriseType != null">enterprise_type = #{enterpriseType},</if>
            <if test="industry != null">industry = #{industry},</if>
            <if test="scale != null">scale = #{scale},</if>
            <if test="employeeCount != null">employee_count = #{employeeCount},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="contactEmail != null">contact_email = #{contactEmail},</if>
            <if test="address != null">address = #{address},</if>
            <if test="province != null">province = #{province},</if>
            <if test="city != null">city = #{city},</if>
            <if test="district != null">district = #{district},</if>
            <if test="businessLicense != null">business_license = #{businessLicense},</if>
            <if test="legalRepresentative != null">legal_representative = #{legalRepresentative},</if>
            <if test="registeredCapital != null">registered_capital = #{registeredCapital},</if>
            <if test="establishmentDate != null">establishment_date = #{establishmentDate},</if>
            <if test="servicePackage != null">service_package = #{servicePackage},</if>
            <if test="contractStartDate != null">contract_start_date = #{contractStartDate},</if>
            <if test="contractEndDate != null">contract_end_date = #{contractEndDate},</if>
            <if test="assessmentQuota != null">assessment_quota = #{assessmentQuota},</if>
            <if test="usedAssessmentCount != null">used_assessment_count = #{usedAssessmentCount},</if>
            <if test="remainingAssessmentCount != null">remaining_assessment_count = #{remainingAssessmentCount},</if>
            <if test="contractAmount != null">contract_amount = #{contractAmount},</if>
            <if test="paidAmount != null">paid_amount = #{paidAmount},</if>
            <if test="unpaidAmount != null">unpaid_amount = #{unpaidAmount},</if>
            <if test="lastPaymentDate != null">last_payment_date = #{lastPaymentDate},</if>
            <if test="nextPaymentDate != null">next_payment_date = #{nextPaymentDate},</if>
            <if test="certificationStatus != null">certification_status = #{certificationStatus},</if>
            <if test="certificationDate != null">certification_date = #{certificationDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        WHERE id = #{id}
    </update>

    <!-- 删除企业 -->
    <delete id="deleteEnterpriseById" parameterType="Long">
        DELETE FROM psy_t_enterprise WHERE id = #{id}
    </delete>

    <!-- 批量删除企业 -->
    <delete id="deleteEnterpriseByIds" parameterType="String">
        DELETE FROM psy_t_enterprise WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 检查企业编码唯一性 -->
    <select id="checkEnterpriseCodeUnique" resultType="int">
        SELECT COUNT(1) FROM psy_t_enterprise 
        WHERE enterprise_code = #{enterpriseCode} AND del_flag = '0'
        <if test="excludeId != null and excludeId != 0">
            AND id != #{excludeId}
        </if>
    </select>

    <!-- 查询启用的企业列表 -->
    <select id="selectEnabledEnterprises" resultMap="EnterpriseResultMap">
        SELECT * FROM psy_t_enterprise 
        WHERE del_flag = '0' AND status = 1
        ORDER BY enterprise_name ASC
    </select>

    <!-- 查询即将过期的企业列表 -->
    <select id="selectExpiringEnterprises" parameterType="Integer" resultMap="EnterpriseResultMap">
        SELECT * FROM psy_t_enterprise 
        WHERE del_flag = '0' AND status = 1
        AND contract_end_date IS NOT NULL
        AND DATEDIFF(contract_end_date, NOW()) BETWEEN 0 AND #{days}
        ORDER BY contract_end_date ASC
    </select>

    <!-- 查询已过期的企业列表 -->
    <select id="selectExpiredEnterprises" resultMap="EnterpriseResultMap">
        SELECT * FROM psy_t_enterprise 
        WHERE del_flag = '0' AND status = 1
        AND contract_end_date IS NOT NULL
        AND contract_end_date < NOW()
        ORDER BY contract_end_date DESC
    </select>

    <!-- 更新企业测评使用次数 -->
    <update id="updateAssessmentUsageCount">
        UPDATE psy_t_enterprise 
        SET used_assessment_count = #{count},
            remaining_assessment_count = assessment_quota - #{count},
            update_time = sysdate()
        WHERE id = #{id}
    </update>

    <!-- 增加企业测评使用次数 -->
    <update id="incrementAssessmentUsageCount">
        UPDATE psy_t_enterprise 
        SET used_assessment_count = used_assessment_count + #{increment},
            remaining_assessment_count = remaining_assessment_count - #{increment},
            update_time = sysdate()
        WHERE id = #{id}
    </update>

    <!-- 搜索企业 -->
    <select id="searchEnterprises" resultMap="EnterpriseResultMap">
        SELECT * FROM psy_t_enterprise
        WHERE del_flag = '0'
        <if test="keyword != null and keyword != ''">
            AND (
                enterprise_name LIKE CONCAT('%', #{keyword}, '%')
                OR enterprise_code LIKE CONCAT('%', #{keyword}, '%')
                OR contact_person LIKE CONCAT('%', #{keyword}, '%')
                OR contact_phone LIKE CONCAT('%', #{keyword}, '%')
            )
        </if>
        <if test="enterpriseType != null">
            AND enterprise_type = #{enterpriseType}
        </if>
        <if test="scale != null and scale != ''">
            AND scale = #{scale}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新企业状态 -->
    <update id="batchUpdateEnterpriseStatus">
        UPDATE psy_t_enterprise 
        SET status = #{status}, update_time = sysdate()
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 自动更新过期企业状态 -->
    <update id="autoUpdateExpiredEnterpriseStatus">
        UPDATE psy_t_enterprise 
        SET status = 0, update_time = sysdate()
        WHERE del_flag = '0' AND status = 1
        AND contract_end_date IS NOT NULL
        AND contract_end_date < NOW()
    </update>

</mapper>
