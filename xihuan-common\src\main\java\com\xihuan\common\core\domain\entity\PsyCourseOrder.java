package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 课程订单表对象 psy_course_order
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCourseOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    @Excel(name = "订单ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    private String orderNo;

    /** 课程ID */
    @Excel(name = "课程ID", cellType = Excel.ColumnType.NUMERIC)
    private Long courseId;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    private Long userId;

    /** 订单原价 */
    @Excel(name = "订单原价")
    private BigDecimal originalPrice;

    /** 实际支付金额 */
    @Excel(name = "实际支付金额")
    private BigDecimal paymentAmount;

    /** 支付方式（wechat/alipay） */
    @Excel(name = "支付方式", readConverterExp = "wechat=微信支付,alipay=支付宝")
    private String paymentMethod;

    /** 支付时间 */
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paymentTime;

    /** 第三方交易号 */
    @Excel(name = "第三方交易号")
    private String transactionId;

    /** 退款金额 */
    @Excel(name = "退款金额")
    private BigDecimal refundAmount;

    /** 退款时间 */
    @Excel(name = "退款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /** 取消时间 */
    @Excel(name = "取消时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date cancelTime;

    /** 使用的优惠券ID */
    @Excel(name = "优惠券ID", cellType = Excel.ColumnType.NUMERIC)
    private Long couponId;

    /** 优惠券折扣金额 */
    @Excel(name = "优惠券折扣金额")
    private BigDecimal couponDiscount;

    /** 会员折扣金额 */
    @Excel(name = "会员折扣金额")
    private BigDecimal membershipDiscount;

    /** 使用的积分 */
    @Excel(name = "使用的积分")
    private Integer pointsUsed;

    /** 积分抵扣金额 */
    @Excel(name = "积分抵扣金额")
    private BigDecimal pointsDiscount;

    /** 是否会员免费 */
    @Excel(name = "是否会员免费", readConverterExp = "0=否,1=是")
    private Integer isMembershipFree;

    /** 订单状态（0:待支付 1:已支付 2:已完成 3:已取消） */
    @Excel(name = "订单状态", readConverterExp = "0=待支付,1=已支付,2=已完成,3=已取消")
    private Integer status;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    /** 关联的课程信息 */
    private PsyCourse course;

    /** 关联的用户信息 */
    private SysUser user;
}
