package com.xihuan.web.controller.miniapp;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.domain.dto.WxPayDTO;
import com.xihuan.system.service.IWxPayService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.BufferedReader;
import java.io.IOException;

/**
 * 小程序支付Controller
 */
@RestController
@RequestMapping("/miniapp/payment")
public class MiniAppPaymentController extends BaseController {
    private static final Logger log = LoggerFactory.getLogger(MiniAppPaymentController.class);

    @Autowired
    private IWxPayService wxPayService;

    @Autowired
    private TokenService tokenService;

    /**
     * 创建支付订单
     */
    @PostMapping("/create")
    @Log(title = "创建支付订单", businessType = BusinessType.INSERT)
    public AjaxResult createPayOrder(@Valid @RequestBody WxPayDTO.CreatePayOrderRequest request, HttpServletRequest httpRequest) {
        try {
            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // 设置客户端IP
            if (StringUtils.isEmpty(request.getClientIp())) {
//                request.setClientIp(ServletUtils.getClientIP(httpRequest));
            }

            // 创建支付订单
            WxPayDTO.PayOrderResponse response = wxPayService.createPayOrder(request);

            return AjaxResult.success("创建支付订单成功", response);

        } catch (Exception e) {
            log.error("创建支付订单失败", e);
            return AjaxResult.error("创建支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 查询支付订单
     */
    @GetMapping("/query")
    public AjaxResult queryOrder(@RequestParam(required = false) String outTradeNo,
                                @RequestParam(required = false) String transactionId,
                                HttpServletRequest httpRequest) {
        try {
            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            if (StringUtils.isEmpty(outTradeNo) && StringUtils.isEmpty(transactionId)) {
                return AjaxResult.error("商户订单号和微信订单号不能同时为空");
            }

            WxPayDTO.QueryOrderRequest request = new WxPayDTO.QueryOrderRequest();
            request.setOutTradeNo(outTradeNo);
            request.setTransactionId(transactionId);

            WxPayDTO.QueryOrderResponse response = wxPayService.queryOrder(request);

            return AjaxResult.success("查询支付订单成功", response);

        } catch (Exception e) {
            log.error("查询支付订单失败", e);
            return AjaxResult.error("查询支付订单失败: " + e.getMessage());
        }
    }

    /**
     * 申请退款
     */
    @PostMapping("/refund")
    @Log(title = "申请退款", businessType = BusinessType.UPDATE)
    public AjaxResult refund(@Valid @RequestBody WxPayDTO.RefundRequest request, HttpServletRequest httpRequest) {
        try {
            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // TODO: 添加权限验证，确保用户只能退款自己的订单

            WxPayDTO.RefundResponse response = wxPayService.refund(request);

            return AjaxResult.success("申请退款成功", response);

        } catch (Exception e) {
            log.error("申请退款失败", e);
            return AjaxResult.error("申请退款失败: " + e.getMessage());
        }
    }

    /**
     * 查询退款
     */
    @GetMapping("/refund/query")
    public AjaxResult queryRefund(@RequestParam String outRefundNo, HttpServletRequest httpRequest) {
        try {
            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            WxPayDTO.RefundResponse response = wxPayService.queryRefund(outRefundNo);

            return AjaxResult.success("查询退款成功", response);

        } catch (Exception e) {
            log.error("查询退款失败", e);
            return AjaxResult.error("查询退款失败: " + e.getMessage());
        }
    }

    /**
     * 关闭订单
     */
    @PostMapping("/close")
    @Log(title = "关闭订单", businessType = BusinessType.UPDATE)
    public AjaxResult closeOrder(@RequestParam String outTradeNo, HttpServletRequest httpRequest) {
        try {
            // 获取用户信息
            LoginUser loginUser = tokenService.getLoginUser(httpRequest);
            if (loginUser == null) {
                return AjaxResult.error("用户未登录");
            }

            // TODO: 添加权限验证，确保用户只能关闭自己的订单

            boolean result = wxPayService.closeOrder(outTradeNo);

            if (result) {
                return AjaxResult.success("关闭订单成功");
            } else {
                return AjaxResult.error("关闭订单失败");
            }

        } catch (Exception e) {
            log.error("关闭订单失败", e);
            return AjaxResult.error("关闭订单失败: " + e.getMessage());
        }
    }

    /**
     * 微信支付回调通知
     */
    @PostMapping("/notify/pay")
    public String payNotify(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("收到微信支付回调通知");

            // 获取请求头中的签名信息
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String signature = request.getHeader("Wechatpay-Signature");
            String serialNo = request.getHeader("Wechatpay-Serial");

            // 读取请求体
            String body = getRequestBody(request);
            log.info("微信支付回调请求体: {}", body);

            // 验证签名
            if (!wxPayService.verifyNotifySignature(timestamp, nonce, body, signature)) {
                log.error("微信支付回调签名验证失败");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return "FAIL";
            }

            // 处理回调通知
            boolean result = wxPayService.handlePayNotify(body);

            if (result) {
                log.info("微信支付回调处理成功");
                response.setStatus(HttpServletResponse.SC_OK);
                return "SUCCESS";
            } else {
                log.error("微信支付回调处理失败");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return "FAIL";
            }

        } catch (Exception e) {
            log.error("处理微信支付回调通知异常", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return "FAIL";
        }
    }

    /**
     * 微信退款回调通知
     */
    @PostMapping("/notify/refund")
    public String refundNotify(HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("收到微信退款回调通知");

            // 获取请求头中的签名信息
            String timestamp = request.getHeader("Wechatpay-Timestamp");
            String nonce = request.getHeader("Wechatpay-Nonce");
            String signature = request.getHeader("Wechatpay-Signature");

            // 读取请求体
            String body = getRequestBody(request);
            log.info("微信退款回调请求体: {}", body);

            // 验证签名
            if (!wxPayService.verifyNotifySignature(timestamp, nonce, body, signature)) {
                log.error("微信退款回调签名验证失败");
                response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
                return "FAIL";
            }

            // 处理回调通知
            boolean result = wxPayService.handleRefundNotify(body);

            if (result) {
                log.info("微信退款回调处理成功");
                response.setStatus(HttpServletResponse.SC_OK);
                return "SUCCESS";
            } else {
                log.error("微信退款回调处理失败");
                response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                return "FAIL";
            }

        } catch (Exception e) {
            log.error("处理微信退款回调通知异常", e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            return "FAIL";
        }
    }

    /**
     * 读取请求体
     */
    private String getRequestBody(HttpServletRequest request) throws IOException {
        StringBuilder sb = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                sb.append(line);
            }
        }
        return sb.toString();
    }
}
