<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyConsultantOrderMapper">

    <resultMap id="BaseResultMap" type="PsyConsultantOrder">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="user_id" property="userId"/>
        <result column="consultant_id" property="consultantId"/>
        <result column="consult_type" property="consultType"/>
        <result column="scheduled_time" property="scheduledTime"/>
        <result column="duration" property="duration"/>
        <result column="unit_price" property="unitPrice"/>
        <result column="original_amount" property="originalAmount"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_time" property="paymentTime"/>
        <result column="status" property="status"/>
        <result column="coupon_id" property="couponId"/>
        <result column="remark" property="remark"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="consult_record_id" property="consultRecordId"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_time" property="refundTime"/>
        <result column="refund_reason" property="refundReason"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
    </resultMap>

    <resultMap id="OrderWithDetailsMap" type="PsyConsultantOrder" extends="BaseResultMap">
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
            <result column="phonenumber" property="phonenumber"/>
        </association>
        <association property="consultant" javaType="PsyConsultant">
            <id column="consultant_id" property="id"/>
            <result column="consultant_name" property="name"/>
            <result column="consultant_title" property="title"/>
            <result column="consultant_avatar" property="avatar"/>
        </association>
        <association property="consultationRecord" javaType="PsyConsultationRecord">
            <id column="consult_record_id" property="id"/>
            <result column="record_duration" property="duration"/>
            <result column="record_rating" property="userRating"/>
        </association>
    </resultMap>

    <select id="selectOrderList" parameterType="PsyConsultantOrder" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_order
        <where>
            del_flag = '0'
            <if test="orderNo != null and orderNo != ''">AND order_no = #{orderNo}</if>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="consultantId != null">AND consultant_id = #{consultantId}</if>
            <if test="consultType != null and consultType != ''">AND consult_type = #{consultType}</if>
            <if test="status != null and status != ''">AND status = #{status}</if>
            <if test="paymentMethod != null and paymentMethod != ''">AND payment_method = #{paymentMethod}</if>
            <if test="params.beginTime != null and params.endTime != null">
                AND scheduled_time BETWEEN #{params.beginTime} AND #{params.endTime}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <select id="selectOrderById" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_order WHERE id = #{id} AND del_flag = '0'
    </select>

    <select id="selectOrderByOrderNo" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_order WHERE order_no = #{orderNo} AND del_flag = '0'
    </select>

    <select id="selectOrderWithDetails" resultMap="OrderWithDetailsMap">
        SELECT 
            o.*,
            u.user_name, u.nick_name, u.phonenumber,
            c.name AS consultant_name, c.title AS consultant_title, c.avatar AS consultant_avatar,
            r.duration AS record_duration, r.user_rating AS record_rating
        FROM psy_consultant_order o
        LEFT JOIN sys_user u ON o.user_id = u.user_id
        LEFT JOIN psy_consultant c ON o.consultant_id = c.id
        LEFT JOIN psy_consultation_record r ON o.consult_record_id = r.id
        WHERE o.id = #{id} AND o.del_flag = '0'
    </select>

    <select id="selectOrdersByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_order 
        WHERE user_id = #{userId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <select id="selectOrdersByConsultantId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_order 
        WHERE consultant_id = #{consultantId} AND del_flag = '0'
        ORDER BY create_time DESC
    </select>

    <insert id="insertOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_consultant_order (
            order_no, user_id, consultant_id, consult_type, scheduled_time, duration,
            unit_price, original_amount, discount_amount, payment_amount, payment_method,
            payment_time, status, coupon_id, remark, cancel_time, cancel_reason,
            consult_record_id, refund_amount, refund_time, refund_reason, del_flag,
            create_time, update_time, create_by, update_by
        ) VALUES (
            #{orderNo}, #{userId}, #{consultantId}, #{consultType}, #{scheduledTime}, #{duration},
            #{unitPrice}, #{originalAmount}, #{discountAmount}, #{paymentAmount}, #{paymentMethod},
            #{paymentTime}, #{status}, #{couponId}, #{remark}, #{cancelTime}, #{cancelReason},
            #{consultRecordId}, #{refundAmount}, #{refundTime}, #{refundReason}, #{delFlag},
            #{createTime}, #{updateTime}, #{createBy}, #{updateBy}
        )
    </insert>

    <update id="updateOrder" parameterType="PsyConsultantOrder">
        UPDATE psy_consultant_order
        <set>
            <if test="scheduledTime != null">scheduled_time = #{scheduledTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="originalAmount != null">original_amount = #{originalAmount},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="couponId != null">coupon_id = #{couponId},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="cancelReason != null">cancel_reason = #{cancelReason},</if>
            <if test="consultRecordId != null">consult_record_id = #{consultRecordId},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="refundReason != null">refund_reason = #{refundReason},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteOrderById">
        UPDATE psy_consultant_order SET del_flag = '2' WHERE id = #{id}
    </update>

    <update id="deleteOrderByIds">
        UPDATE psy_consultant_order SET del_flag = '2'
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateOrderPaymentStatus">
        UPDATE psy_consultant_order
        SET status = #{status},
            payment_method = #{paymentMethod},
            payment_time = #{paymentTime},
            update_time = NOW()
        WHERE order_no = #{orderNo}
    </update>

    <update id="updateOrderStatus">
        UPDATE psy_consultant_order
        SET status = #{status}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="cancelOrder">
        UPDATE psy_consultant_order
        SET status = '已取消',
            cancel_reason = #{cancelReason},
            cancel_time = #{cancelTime},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="refundOrder">
        UPDATE psy_consultant_order
        SET status = '已退款',
            refund_amount = #{refundAmount},
            refund_reason = #{refundReason},
            refund_time = #{refundTime},
            update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="generateOrderNo" resultType="String">
        SELECT CONCAT('CO', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(FLOOR(RAND() * 1000), 3, '0'))
    </select>

    <select id="selectExpiringOrders" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_order
        WHERE status = '已支付' 
        AND scheduled_time BETWEEN NOW() AND DATE_ADD(NOW(), INTERVAL #{minutes} MINUTE)
        AND del_flag = '0'
    </select>

    <select id="selectExpiredOrders" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_order
        WHERE status IN ('待支付', '已支付') 
        AND scheduled_time &lt; NOW()
        AND del_flag = '0'
    </select>

    <select id="countUserOrders" resultType="int">
        SELECT COUNT(*) FROM psy_consultant_order 
        WHERE user_id = #{userId} AND del_flag = '0'
    </select>

    <select id="countConsultantOrders" resultType="int">
        SELECT COUNT(*) FROM psy_consultant_order 
        WHERE consultant_id = #{consultantId} AND del_flag = '0'
    </select>

    <select id="sumOrderIncome" resultType="java.math.BigDecimal">
        SELECT COALESCE(SUM(payment_amount), 0) FROM psy_consultant_order 
        WHERE consultant_id = #{consultantId} 
        AND status IN ('已完成', '咨询中')
        AND del_flag = '0'
        <if test="startTime != null and endTime != null">
            AND payment_time BETWEEN #{startTime} AND #{endTime}
        </if>
    </select>

    <select id="checkTimeConflict" resultType="int">
        SELECT COUNT(*) FROM psy_consultant_order
        WHERE consultant_id = #{consultantId}
        AND status IN ('已支付', '待咨询', '咨询中')
        AND del_flag = '0'
        AND (
            (scheduled_time &lt;= #{startTime} AND DATE_ADD(scheduled_time, INTERVAL duration MINUTE) > #{startTime})
            OR
            (scheduled_time &lt; #{endTime} AND DATE_ADD(scheduled_time, INTERVAL duration MINUTE) >= #{endTime})
            OR
            (scheduled_time >= #{startTime} AND scheduled_time &lt; #{endTime})
        )
        <if test="excludeOrderId != null">
            AND id != #{excludeOrderId}
        </if>
    </select>

</mapper>
