package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyConsultationRecord;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 心理咨询记录表Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyConsultationRecordService {
    
    /**
     * 查询咨询记录列表
     * 
     * @param record 咨询记录信息
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordList(PsyConsultationRecord record);

    /**
     * 查询咨询记录详情（包含用户、咨询师等信息）
     * 
     * @param id 咨询记录ID
     * @return 咨询记录详情
     */
    PsyConsultationRecord selectRecordWithDetails(Long id);

    /**
     * 根据ID查询咨询记录
     * 
     * @param id 咨询记录ID
     * @return 咨询记录信息
     */
    PsyConsultationRecord selectRecordById(Long id);

    /**
     * 新增咨询记录
     * 
     * @param record 咨询记录信息
     * @return 结果
     */
    int insertRecord(PsyConsultationRecord record);

    /**
     * 修改咨询记录
     * 
     * @param record 咨询记录信息
     * @return 结果
     */
    int updateRecord(PsyConsultationRecord record);

    /**
     * 删除咨询记录
     * 
     * @param id 咨询记录ID
     * @return 结果
     */
    int deleteRecordById(Long id);

    /**
     * 批量删除咨询记录
     * 
     * @param ids 需要删除的咨询记录ID
     * @return 结果
     */
    int deleteRecordByIds(Long[] ids);

    /**
     * 根据用户ID查询咨询记录列表
     * 
     * @param userId 用户ID
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByUserId(Long userId);

    /**
     * 根据咨询师ID查询咨询记录列表
     * 
     * @param consultantId 咨询师ID
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByConsultantId(Long consultantId);

    /**
     * 根据订单ID查询咨询记录
     * 
     * @param orderId 订单ID
     * @return 咨询记录信息
     */
    PsyConsultationRecord selectRecordByOrderId(Long orderId);

    /**
     * 开始咨询
     * 
     * @param orderId 订单ID
     * @return 咨询记录
     */
    PsyConsultationRecord startConsultation(Long orderId);

    /**
     * 结束咨询
     * 
     * @param recordId 记录ID
     * @param consultContent 咨询内容摘要
     * @return 结果
     */
    int endConsultation(Long recordId, String consultContent);

    /**
     * 中断咨询
     * 
     * @param recordId 记录ID
     * @param interruptType 中断类型
     * @param reason 中断原因
     * @return 结果
     */
    int interruptConsultation(Long recordId, String interruptType, String reason);

    /**
     * 恢复咨询
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    int resumeConsultation(Long recordId);

    /**
     * 用户评价
     * 
     * @param recordId 记录ID
     * @param userRating 用户评价
     * @return 结果
     */
    int rateConsultation(Long recordId, Integer userRating);

    /**
     * 统计用户咨询数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    java.util.Map<String, Object> getUserConsultationStats(Long userId);

    /**
     * 统计咨询师咨询数据
     * 
     * @param consultantId 咨询师ID
     * @return 统计数据
     */
    java.util.Map<String, Object> getConsultantConsultationStats(Long consultantId);

    /**
     * 查询用户与咨询师的咨询记录
     * 
     * @param userId 用户ID
     * @param consultantId 咨询师ID
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByUserAndConsultant(Long userId, Long consultantId);

    /**
     * 查询指定时间段的咨询记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByTimeRange(Date startTime, Date endTime);

    /**
     * 检查咨询记录是否可以评价
     * 
     * @param recordId 记录ID
     * @param userId 用户ID
     * @return 是否可以评价
     */
    boolean canRate(Long recordId, Long userId);

    /**
     * 检查咨询记录是否可以中断
     * 
     * @param recordId 记录ID
     * @return 是否可以中断
     */
    boolean canInterrupt(Long recordId);
}
