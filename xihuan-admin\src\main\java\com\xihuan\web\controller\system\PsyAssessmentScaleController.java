package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyAssessmentScale;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyAssessmentScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心理测评量表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/assessment/scale")
public class PsyAssessmentScaleController extends BaseController {
    
    @Autowired
    private IPsyAssessmentScaleService scaleService;

    /**
     * 查询量表列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyAssessmentScale scale) {
        startPage();
        List<PsyAssessmentScale> list = scaleService.selectScaleList(scale);
        return getDataTable(list);
    }

    /**
     * 导出量表列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:export')")
    @Log(title = "心理测评量表", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyAssessmentScale scale) {
        List<PsyAssessmentScale> list = scaleService.selectScaleList(scale);
        ExcelUtil<PsyAssessmentScale> util = new ExcelUtil<>(PsyAssessmentScale.class);
        util.exportExcel(response, list, "心理测评量表数据");
    }

    /**
     * 导入量表数据
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:import')")
    @Log(title = "心理测评量表", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception {
        ExcelUtil<PsyAssessmentScale> util = new ExcelUtil<>(PsyAssessmentScale.class);
        List<PsyAssessmentScale> scaleList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = scaleService.importScale(scaleList, updateSupport, operName);
        return success(message);
    }

    /**
     * 获取导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) {
        ExcelUtil<PsyAssessmentScale> util = new ExcelUtil<>(PsyAssessmentScale.class);
        util.importTemplateExcel(response, "心理测评量表数据");
    }

    /**
     * 获取量表详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(scaleService.selectScaleWithDetails(id));
    }

    /**
     * 新增量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:add')")
    @Log(title = "心理测评量表", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyAssessmentScale scale) {
        return toAjax(scaleService.insertScale(scale));
    }

    /**
     * 修改量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "心理测评量表", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyAssessmentScale scale) {
        return toAjax(scaleService.updateScale(scale));
    }

    /**
     * 删除量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:remove')")
    @Log(title = "心理测评量表", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(scaleService.deleteScaleByIds(ids));
    }

    /**
     * 发布量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "心理测评量表", businessType = BusinessType.UPDATE)
    @PutMapping("/publish/{id}")
    public AjaxResult publish(@PathVariable("id") Long id) {
        return toAjax(scaleService.publishScale(id));
    }

    /**
     * 下架量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:edit')")
    @Log(title = "心理测评量表", businessType = BusinessType.UPDATE)
    @PutMapping("/offline/{id}")
    public AjaxResult offline(@PathVariable("id") Long id) {
        return toAjax(scaleService.offlineScale(id));
    }

    /**
     * 复制量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:add')")
    @Log(title = "心理测评量表", businessType = BusinessType.INSERT)
    @PostMapping("/copy/{id}")
    public AjaxResult copy(@PathVariable("id") Long id, @RequestParam("newScaleName") String newScaleName, @RequestParam("newScaleCode") String newScaleCode) {
        return toAjax(scaleService.copyScale(id, newScaleName, newScaleCode));
    }

    /**
     * 查询热门量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/hot")
    public AjaxResult getHotScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<PsyAssessmentScale> list = scaleService.selectHotScales(limit);
        return success(list);
    }

    /**
     * 查询推荐量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/recommend")
    public AjaxResult getRecommendScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<PsyAssessmentScale> list = scaleService.selectRecommendScales(getUserId(), limit);
        return success(list);
    }

    /**
     * 查询免费量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/free")
    public AjaxResult getFreeScales() {
        List<PsyAssessmentScale> list = scaleService.selectFreeScales();
        return success(list);
    }

    /**
     * 搜索量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/search")
    public AjaxResult searchScales(@RequestParam(value = "keyword", required = false) String keyword,
                                  @RequestParam(value = "categoryId", required = false) Long categoryId,
                                  @RequestParam(value = "difficultyLevel", required = false) Integer difficultyLevel,
                                  @RequestParam(value = "isFree", required = false) Integer isFree) {
        List<PsyAssessmentScale> list = scaleService.searchScales(keyword, categoryId, difficultyLevel, isFree);
        return success(list);
    }

    /**
     * 查询量表统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/stats")
    public AjaxResult getScaleStats() {
        return success(scaleService.selectScaleStats());
    }

    /**
     * 查询量表测评统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/stats/{scaleId}")
    public AjaxResult getScaleTestStats(@PathVariable("scaleId") Long scaleId) {
        return success(scaleService.selectScaleTestStats(scaleId));
    }

    /**
     * 查询相似量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:list')")
    @GetMapping("/similar/{scaleId}")
    public AjaxResult getSimilarScales(@PathVariable("scaleId") Long scaleId, @RequestParam(value = "limit", defaultValue = "5") Integer limit) {
        List<PsyAssessmentScale> list = scaleService.selectSimilarScales(scaleId, limit);
        return success(list);
    }

    /**
     * 检查量表编码唯一性
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:query')")
    @GetMapping("/checkScaleCodeUnique")
    public AjaxResult checkScaleCodeUnique(@RequestParam("scaleCode") String scaleCode, @RequestParam(value = "id", required = false) Long id) {
        return success(scaleService.checkScaleCodeUnique(scaleCode, id));
    }
}
