package com.xihuan.common.core.domain.dto;

import lombok.Data;
import java.time.LocalTime;
import java.util.List;

/**
 * 时间槽数据传输对象
 * 
 * <AUTHOR>
 */
@Data
public class PsyTimeSlotDTO {
    
    /** 日期（YYYY-MM-DD） */
    private String date;
    
    /** 星期几显示 */
    private String weekDay;
    
    /** 是否是今天 */
    private Boolean isToday;
    
    /** 时间段列表 */
    private List<TimeRangeDTO> timeRanges;
    
    @Data
    public static class TimeRangeDTO {
        /** 时间段名称（上午/下午/晚上） */
        private String rangeName;
        
        /** 时间段图标 */
        private String iconUrl;
        
        /** 时间槽列表 */
        private List<SlotDTO> slots;
    }
    
    @Data
    public static class SlotDTO {
        /** 时间槽ID */
        private Long slotId;
        
        /** 开始时间 */
        private LocalTime startTime;
        
        /** 结束时间 */
        private LocalTime endTime;
        
        /** 时间显示（如：09:00-09:15） */
        private String timeDisplay;
        
        /** 状态（0可用 1已预约 2已过期） */
        private Integer status;
        
        /** 状态描述 */
        private String statusText;
        
        /** 是否可预约 */
        private Boolean available;
        
        /** 咨询师ID */
        private Long counselorId;
        
        /** 咨询师姓名 */
        private String counselorName;
    }
}
