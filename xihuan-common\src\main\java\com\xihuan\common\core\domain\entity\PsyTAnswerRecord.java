package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 答题记录表 psy_t_answer_record
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTAnswerRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 答案ID */
    @Excel(name = "答案ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 测评记录ID */
    @Excel(name = "测评记录ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "测评记录ID不能为空")
    private Long recordId;

    /** 题目ID */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /** 答案内容 */
    @Excel(name = "答案内容")
    private String answerContent;

    /** 得分 */
    @Excel(name = "得分")
    private Integer answerScore;

    /** 答题耗时(秒) */
    @Excel(name = "答题耗时")
    private Integer responseTime;

    // 关联对象
    /** 测评记录信息 */
    private PsyTAssessmentRecord record;

    /** 题目信息 */
    private PsyTQuestion question;

    /** 选项信息 */
    private PsyTQuestionOption option;

    // 扩展字段
    /** 会话ID */
    private String sessionId;

    /** 量表ID */
    private Long scaleId;

    /** 量表名称 */
    private String scaleName;

    /** 用户ID */
    private Long userId;

    /** 用户昵称 */
    private String nickName;

    /** 题目序号 */
    private Integer questionNo;

    /** 题目内容 */
    private String questionContent;

    /** 题目类型 */
    private String questionType;

    /** 选项ID */
    private Long optionId;

    /** 选项文本 */
    private String optionText;

    /** 选项分值 */
    private Integer optionValue;

    /** 是否反向计分 */
    private Integer isReverse;

    /** 反向计分值 */
    private Integer reverseValue;

    /** 原始得分 */
    private Integer originalScore;

    /** 最终得分 */
    private Integer finalScore;

    /** 是否正确答案 */
    private Boolean isCorrect;

    /** 答题状态 */
    private String answerStatus;

    /**
     * 获取答案显示文本
     */
    public String getAnswerDisplayText() {
        if (answerContent != null && !answerContent.trim().isEmpty()) {
            return answerContent;
        }
        if (optionText != null && !optionText.trim().isEmpty()) {
            return optionText;
        }
        return "未答题";
    }

    /**
     * 获取耗时描述
     */
    public String getResponseTimeDesc() {
        if (responseTime == null || responseTime <= 0) return "0秒";
        
        int minutes = responseTime / 60;
        int seconds = responseTime % 60;
        
        if (minutes > 0) {
            return minutes + "分" + seconds + "秒";
        } else {
            return seconds + "秒";
        }
    }

    /**
     * 计算最终得分
     */
    public Integer calculateFinalScore() {
        if (answerScore == null) return 0;
        
        // 如果是反向计分题目
        if (isReverse != null && isReverse == 1 && reverseValue != null) {
            return reverseValue - answerScore;
        }
        
        return answerScore;
    }

    /**
     * 是否已答题
     */
    public boolean isAnswered() {
        return answerContent != null && !answerContent.trim().isEmpty();
    }

    /**
     * 是否单选题答案
     */
    public boolean isSingleChoiceAnswer() {
        return "SINGLE".equals(questionType) && optionId != null;
    }

    /**
     * 是否多选题答案
     */
    public boolean isMultipleChoiceAnswer() {
        return "MULTIPLE".equals(questionType);
    }

    /**
     * 是否文本题答案
     */
    public boolean isTextAnswer() {
        return "TEXT".equals(questionType) && answerContent != null;
    }

    /**
     * 是否复合题答案
     */
    public boolean isCompositeAnswer() {
        return "COMPOSITE".equals(questionType);
    }

    /**
     * 获取得分描述
     */
    public String getScoreDesc() {
        if (answerScore == null) return "0分";
        
        if (isReverse != null && isReverse == 1) {
            Integer finalScore = calculateFinalScore();
            return answerScore + "分(反向计分后:" + finalScore + "分)";
        }
        
        return answerScore + "分";
    }
}
