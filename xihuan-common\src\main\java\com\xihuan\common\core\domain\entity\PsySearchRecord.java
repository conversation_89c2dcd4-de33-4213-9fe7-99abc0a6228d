package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 搜索记录表 psy_search_record
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsySearchRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 搜索记录ID */
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID")
    private Long userId;

    /** 搜索关键词 */
    @Excel(name = "搜索关键词")
    private String keyword;

    /** 搜索类型 */
    @Excel(name = "搜索类型", readConverterExp = "all=全部,consultant=咨询师,course=课程,meditation=冥想,question=问题")
    private String searchType;

    /** 搜索结果数量 */
    @Excel(name = "搜索结果数量")
    private Integer resultCount;

    /** 搜索时间 */
    @Excel(name = "搜索时间", dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date searchTime;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;

    /** 用户代理 */
    private String userAgent;

    /** 删除标志 */
    private String delFlag;
}
