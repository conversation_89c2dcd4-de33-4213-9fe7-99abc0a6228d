package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.List;

/**
 * 冥想主表对象 psy_meditation
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyMeditation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 冥想ID */
    @Excel(name = "冥想ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 冥想标题 */
    @Excel(name = "冥想标题")
    private String title;

    /** 冥想描述 */
    @Excel(name = "冥想描述")
    private String description;

    /** 分类id */
    @Excel(name = "分类id")
    private Integer categoryId;

    /** 音频文件URL */
    @Excel(name = "音频文件URL")
    private String audioUrl;

    /** 封面图片 */
    @Excel(name = "封面图片")
    private String coverImage;

    /** 时长（秒） */
    @Excel(name = "时长（秒）")
    private Integer duration;

    /** 难度等级（1=入门 2=进阶 3=高级） */
    @Excel(name = "难度等级", readConverterExp = "1=入门,2=进阶,3=高级")
    private Integer difficultyLevel;

    /** 引导师 */
    @Excel(name = "引导师")
    private String narrator;

    /** 价格 */
    @Excel(name = "价格")
    private BigDecimal price;

    /** 是否免费（0=付费 1=免费） */
    @Excel(name = "是否免费", readConverterExp = "0=付费,1=免费")
    private Integer isFree;

    /** 播放次数 */
    @Excel(name = "播放次数")
    private Integer playCount;

    /** 平均评分 */
    @Excel(name = "平均评分")
    private BigDecimal ratingAvg;

    /** 评分人数 */
    @Excel(name = "评分人数")
    private Integer ratingCount;

    /** 状态（0=未发布 1=已发布 2=下架） */
    @Excel(name = "状态", readConverterExp = "0=未发布,1=已发布,2=下架")
    private Integer status;

    /** 标签（JSON格式） */
    @Excel(name = "标签")
    private String tags;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    /** 搜索关键词 */
    @Excel(name = "搜索关键词")
    private String searchKeywords;

    /** 被搜索次数 */
    @Excel(name = "被搜索次数")
    private Integer searchCount;

    // 关联对象
    /** 分类ID列表（用于多分类关系） */
    private List<Long> categoryIds;

    /** 分类列表（用于多分类关系） */
    private List<PsyCategory> categories;

    /** 用户是否已购买 */
    private Boolean purchased;

    /** 用户播放记录 */
    private PsyUserMeditationRecord userRecord;
}
