package com.xihuan.system.domain.dto;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import com.xihuan.common.core.domain.entity.PsyCategory;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 心理咨询产品数据传输对象（含关联服务项）
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode()
@Accessors(chain = true)
public class PsyProductDTO extends BaseEntity {

    /** 产品ID */
//    @NotNull(message = "产品ID不能为空", groups = EditGroup.class)
    private Long productId;

    /** 产品名称 */
//    @NotBlank(message = "产品名称不能为空", groups = {AddGroup.class, EditGroup.class})
    @Size(min = 2, max = 50, message = "产品名称长度必须在{min}到{max}个字符之间")
    private String productName;

    /** 产品主图（OSS地址） */
//    @NotBlank(message = "产品主图不能为空", groups = AddGroup.class)
    private String productImage;

    /** 服务方式（1线上 2线下 3线上线下） */
    @NotNull(message = "服务方式不能为空")
    @Min(value = 1, message = "无效的服务方式")
    @Max(value = 3, message = "无效的服务方式")
    private Integer serviceMethod;

    /** 服务保障（JSON数组） */
    private String serviceGuarantee;

    /** 服务方向（心理疾病类型） */
    @Excel(name = "服务方向")
    private String serviceDirection;

    /** 服务方向类型（1个体咨询 2团体咨询） */
    private Integer serviceDirectionType;

    /** 咨询师等级要求 */
    @Min(value = 1, message = "无效的咨询师等级")
    @Max(value = 5, message = "无效的咨询师等级")
    private Integer consultantGrade;

    /** 服务时长（分钟） */
    @NotNull
    @Min(value = 30, message = "服务时长不能小于30分钟")
    private Integer serviceDuration;

    /** 图文详情（富文本HTML） */
//    @NotBlank(groups = AddGroup.class)
    private String graphicDetails;

    /** 补充说明 */
    @Size(max = 500, message = "补充说明不能超过500个字符")
    private String supplementInfo;

    /** 原价 */
    @NotNull
    @DecimalMin(value = "0.01", message = "价格必须大于0")
    private BigDecimal originalPrice;

    /** 折扣价 */
    @DecimalMin(value = "0.01", message = "折扣价必须大于0")
    private BigDecimal discountPrice;

    /** 折扣率（0-1） */
    @DecimalMin(value = "0.0", message = "折扣率不能小于0")
    @DecimalMax(value = "1.0", message = "折扣率不能大于1")
    private BigDecimal discountRate;

    /** 适用门店（JSON数组） */
    private String applicableStores;

    /** 有效期（天数） */
    @Min(value = 1, message = "有效期至少1天")
    private Integer validityPeriod;

    /** 不可用日期（JSON数组） */
    private String unavailableDates;

    /** 是否需要预约 */
    @NotNull
    private Boolean needAppointment;

    /** 单次购买限制 */
    @Min(value = 1, message = "单次购买限制至少1件")
    private Integer singlePurchaseLimit;

    /** 适用年龄范围（格式：18-60） */
    @Pattern(regexp = "^\\d+-\\d+$", message = "年龄范围格式不正确")
    private String ageRange;

    /** 禁用状态（0启用 1禁用） */
    private String disableFlag;

    /** 关联服务项目（包含服务内容） */
    @Valid  // 开启嵌套校验
//    @NotEmpty(message = "必须至少选择一个服务项目", groups = AddGroup.class)
    private List<ServiceItemWithContent> serviceItems;

    /** 分类ID列表 */
    private List<Long> categoryIds;

    /** 分类列表 */
    private List<PsyCategory> categories;

    /**
     * 服务项目关联对象（嵌套DTO）
     */
    @Data
    @Accessors(chain = true)
    public static class ServiceItemWithContent {

        /** 服务项目ID */
        @NotNull
        private Long itemId;

        /** 服务内容列表 */
        @Valid
        private List<ServiceContent> contents;
    }

    /**
     * 服务内容对象
     */
    @Data
    @Accessors(chain = true)
    public static class ServiceContent {

        /** 内容类型（1文字 2语音 3视频） */
        @NotNull
        @Min(1) @Max(3)
        private Integer contentType;

        /** 内容详情 */
        @NotBlank
        @Size(max = 2000)
        private String content;

        /** 排序序号 */
        @Min(1)
        private Integer sortOrder;
    }
}