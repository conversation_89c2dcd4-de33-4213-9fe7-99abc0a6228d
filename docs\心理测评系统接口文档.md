# 心理测评系统接口文档

## 概述

心理测评系统提供完整的心理量表测评功能，包括量表管理、测评流程、结果分析等。系统分为三个端：
- **后台管理端**：管理员使用，负责量表管理、数据统计等
- **小程序用户端**：普通用户使用，进行心理测评
- **小程序咨询师端**：咨询师使用，查看测评数据和分析

## 数据库表结构

### 主要表说明

| 表名 | 说明 | 备注 |
|------|------|------|
| psy_t_scale | 测评量表主表 | 存储量表基本信息 |
| psy_t_question | 测评题目表 | 存储量表题目 |
| psy_t_option | 题目选项表 | 存储题目选项 |
| psy_t_record | 测评记录表 | 存储用户测评记录 |
| psy_t_answer | 测评答案表 | 存储用户答题记录 |
| psy_t_order | 测评订单表 | 存储付费测评订单 |
| psy_t_review | 测评评价表 | 存储用户评价 |
| psy_t_interpretation | 测评结果解释表 | 存储结果解释规则 |

## 后台管理端接口

### 1. 量表管理

#### 1.1 查询量表列表
```
GET /system/assessment/scale/list
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| scaleName | String | 否 | 量表名称 |
| scaleCode | String | 否 | 量表编码 |
| categoryId | Long | 否 | 分类ID |
| status | Integer | 否 | 状态(0=未发布 1=已发布 2=下架) |
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "rows": [
    {
      "id": 1,
      "scaleName": "焦虑自评量表",
      "scaleCode": "SAS",
      "description": "用于评定焦虑状态的轻重程度",
      "categoryId": 101,
      "categoryName": "情绪测评",
      "questionCount": 20,
      "timeLimit": 15,
      "difficultyLevel": 1,
      "price": 0.00,
      "isFree": 1,
      "status": 1,
      "viewCount": 150,
      "testCount": 89,
      "ratingAvg": 4.5,
      "createTime": "2024-12-01 10:00:00"
    }
  ],
  "total": 1
}
```

#### 1.2 获取量表详情
```
GET /system/assessment/scale/{id}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "scaleName": "焦虑自评量表",
    "scaleCode": "SAS",
    "description": "焦虑自评量表详细描述...",
    "instruction": "请根据您最近一周的实际感受...",
    "questions": [
      {
        "id": 1,
        "questionNo": 1,
        "questionText": "我觉得比平常容易紧张和着急",
        "questionType": 1,
        "options": [
          {
            "id": 1,
            "optionText": "没有或很少时间",
            "optionValue": "A",
            "score": 1
          }
        ]
      }
    ],
    "interpretations": [
      {
        "id": 1,
        "dimension": null,
        "minScore": 20,
        "maxScore": 44,
        "levelName": "正常",
        "levelDescription": "您的焦虑水平在正常范围内"
      }
    ]
  }
}
```

#### 1.3 新增量表
```
POST /system/assessment/scale
```

**请求体：**
```json
{
  "scaleName": "新量表名称",
  "scaleCode": "NEW_SCALE",
  "description": "量表描述",
  "instruction": "测评说明",
  "categoryId": 101,
  "author": "作者",
  "questionCount": 20,
  "timeLimit": 15,
  "difficultyLevel": 1,
  "price": 0.00,
  "isFree": 1
}
```

#### 1.4 修改量表
```
PUT /system/assessment/scale
```

#### 1.5 删除量表
```
DELETE /system/assessment/scale/{ids}
```

#### 1.6 发布量表
```
PUT /system/assessment/scale/publish/{id}
```

#### 1.7 下架量表
```
PUT /system/assessment/scale/offline/{id}
```

### 2. 题目管理

#### 2.1 查询题目列表
```
GET /system/assessment/question/list
```

#### 2.2 新增题目
```
POST /system/assessment/question
```

**请求体：**
```json
{
  "scaleId": 1,
  "questionNo": 1,
  "questionText": "题目内容",
  "questionType": 1,
  "isRequired": 1,
  "dimension": "焦虑",
  "options": [
    {
      "optionText": "选项A",
      "optionValue": "A",
      "score": 1,
      "orderNum": 1
    }
  ]
}
```

### 3. 测评记录管理

#### 3.1 查询测评记录列表
```
GET /system/assessment/record/list
```

#### 3.2 查询测评统计
```
GET /system/assessment/record/stats
```

## 小程序用户端接口

### 1. 量表浏览

#### 1.1 查询量表列表
```
GET /miniapp/user/assessment/scale/list
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| categoryId | Long | 否 | 分类ID |
| difficultyLevel | Integer | 否 | 难度等级 |
| isFree | Integer | 否 | 是否免费 |
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |

#### 1.2 获取量表详情
```
GET /miniapp/user/assessment/scale/{id}
```

#### 1.3 搜索量表
```
GET /miniapp/user/assessment/scale/search
```

**请求参数：**
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 否 | 搜索关键词 |
| categoryId | Long | 否 | 分类ID |
| difficultyLevel | Integer | 否 | 难度等级 |
| isFree | Integer | 否 | 是否免费 |

#### 1.4 查询热门量表
```
GET /miniapp/user/assessment/scale/hot
```

#### 1.5 查询推荐量表
```
GET /miniapp/user/assessment/scale/recommend
```

### 2. 测评流程

#### 2.1 开始测评
```
POST /miniapp/user/assessment/test/start
```

**请求体：**
```json
{
  "scaleId": 1,
  "isAnonymous": 0
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "sessionId": "session_001_20241201_001",
    "scaleId": 1,
    "scaleName": "焦虑自评量表",
    "startTime": "2024-12-01 10:00:00",
    "timeLimit": 15,
    "questionCount": 20,
    "status": 0
  }
}
```

#### 2.2 提交答案
```
POST /miniapp/user/assessment/test/submit
```

**请求体：**
```json
{
  "sessionId": "session_001_20241201_001",
  "questionId": 1,
  "optionId": 2,
  "timeSpent": 30
}
```

#### 2.3 完成测评
```
POST /miniapp/user/assessment/test/complete
```

**请求体：**
```json
{
  "sessionId": "session_001_20241201_001"
}
```

**响应示例：**
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "id": 1,
    "sessionId": "session_001_20241201_001",
    "totalScore": 35,
    "maxScore": 80,
    "percentage": 43.75,
    "resultLevel": "正常",
    "resultDescription": "您的焦虑水平在正常范围内",
    "suggestions": "继续保持良好的心理状态",
    "dimensionScores": {
      "焦虑": 12,
      "躯体": 15,
      "睡眠": 8
    },
    "duration": 900,
    "endTime": "2024-12-01 10:15:00"
  }
}
```

#### 2.4 获取测评结果
```
GET /miniapp/user/assessment/test/result/{sessionId}
```

### 3. 测评记录

#### 3.1 查询用户测评记录
```
GET /miniapp/user/assessment/record/list
```

#### 3.2 查询测评记录详情
```
GET /miniapp/user/assessment/record/{id}
```

#### 3.3 查询测评历史
```
GET /miniapp/user/assessment/record/history/{scaleId}
```

### 4. 评价管理

#### 4.1 提交评价
```
POST /miniapp/user/assessment/review/submit
```

**请求体：**
```json
{
  "scaleId": 1,
  "recordId": 1,
  "rating": 5,
  "content": "这个测评很准确，帮助我了解了自己的焦虑状况",
  "isAnonymous": 0
}
```

#### 4.2 查询量表评价
```
GET /miniapp/user/assessment/review/list/{scaleId}
```

#### 4.3 查询用户评价
```
GET /miniapp/user/assessment/review/user
```

## 小程序咨询师端接口

### 1. 测评数据查看

#### 1.1 查询用户测评记录
```
GET /miniapp/consultant/assessment/record/list
```

#### 1.2 查询测评统计
```
GET /miniapp/consultant/assessment/stats
```

## 状态码说明

| 状态码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 测评流程说明

### 1. 完整测评流程

```mermaid
sequenceDiagram
    participant U as 用户
    participant A as 小程序
    participant S as 服务器
    participant D as 数据库

    U->>A: 选择量表
    A->>S: 获取量表详情
    S->>D: 查询量表信息
    D-->>S: 返回量表数据
    S-->>A: 返回量表详情
    A-->>U: 显示量表信息

    U->>A: 开始测评
    A->>S: 开始测评请求
    S->>D: 创建测评记录
    D-->>S: 返回会话ID
    S-->>A: 返回测评会话
    A-->>U: 开始答题

    loop 答题过程
        U->>A: 提交答案
        A->>S: 提交答案
        S->>D: 保存答案
        D-->>S: 保存成功
        S-->>A: 返回结果
        A-->>U: 显示下一题
    end

    U->>A: 完成测评
    A->>S: 完成测评请求
    S->>D: 计算得分和结果
    D-->>S: 返回测评结果
    S-->>A: 返回测评结果
    A-->>U: 显示测评报告
```

### 2. 测评结果计算

1. **总分计算**：所有题目得分之和
2. **维度得分**：按维度分组计算得分
3. **得分率**：(总分/满分) × 100%
4. **结果等级**：根据得分范围匹配解释规则
5. **建议生成**：根据结果等级提供相应建议

### 3. 权限控制

- **后台管理端**：需要管理员权限，使用 `@PreAuthorize` 注解控制
- **小程序用户端**：需要用户登录，只能访问自己的数据
- **小程序咨询师端**：需要咨询师身份验证

### 4. 数据安全

- 所有接口都需要身份验证
- 用户只能访问自己的测评数据
- 匿名测评不显示用户信息
- 敏感数据加密存储

## 注意事项

1. **测评会话管理**：每次测评都会生成唯一的会话ID，用于标识测评过程
2. **时间限制**：支持设置测评时间限制，超时自动提交
3. **断点续答**：支持测评过程中断后继续答题
4. **数据统计**：实时更新量表的查看次数、测试次数等统计数据
5. **评分机制**：支持多种计分方式，包括选项分值和自定义计分
6. **结果解释**：支持总分解释和维度解释，提供个性化建议
