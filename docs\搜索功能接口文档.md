# 搜索功能接口文档

## 📱 小程序端搜索接口

### 1. 全局搜索

**接口地址**: `GET /miniapp/search/global`

**接口描述**: 支持多模块统一搜索，包括咨询师、课程、冥想、测评

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| keyword | String | 是 | - | 搜索关键词，长度1-100字符 |
| type | String | 否 | all | 搜索类型：all/consultant/course/meditation/assessment |
| pageNum | Integer | 否 | 1 | 页码，从1开始 |
| pageSize | Integer | 否 | 20 | 每页数量，最大50 |

**请求示例**:
```javascript
GET /miniapp/search/global?keyword=心理咨询&type=all&pageNum=1&pageSize=20
```

**响应参数**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "keyword": "心理咨询",
    "searchType": "all",
    "totalCount": 25,
    "searchTime": 156,
    "categories": [
      {
        "type": "consultant",
        "typeName": "咨询师",
        "count": 10,
        "items": [
          {
            "id": 1,
            "type": "consultant",
            "title": "张三心理咨询师",
            "description": "专业心理咨询师，擅长焦虑症治疗",
            "coverImage": "https://example.com/avatar.jpg",
            "relevanceScore": 95.5,
            "price": "200",
            "rating": 4.8,
            "viewCount": 1250,
            "createTime": "2024-01-01T10:00:00",
            "highlightTitle": "张三<em>心理咨询</em>师",
            "highlightDescription": "专业<em>心理咨询</em>师，擅长焦虑症治疗"
          }
        ]
      },
      {
        "type": "course",
        "typeName": "课程",
        "count": 8,
        "items": [...]
      },
      {
        "type": "meditation",
        "typeName": "冥想",
        "count": 5,
        "items": [...]
      },
      {
        "type": "assessment",
        "typeName": "测评",
        "count": 2,
        "items": [...]
      }
    ],
    "suggestions": ["心理咨询师", "心理咨询费用", "心理咨询方法"]
  }
}
```

### 2. 搜索建议

**接口地址**: `GET /miniapp/search/suggestions`

**接口描述**: 根据输入关键词获取搜索建议

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| keyword | String | 是 | 关键词前缀，最少1个字符 |

**请求示例**:
```javascript
GET /miniapp/search/suggestions?keyword=心理
```

**响应参数**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    "心理咨询师",
    "心理测评",
    "心理学课程",
    "心理健康",
    "心理治疗"
  ]
}
```

### 3. 热门搜索

**接口地址**: `GET /miniapp/search/hot`

**接口描述**: 获取热门搜索关键词列表

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| type | String | 否 | all | 搜索类型：all/consultant/course/meditation/assessment |
| limit | Integer | 否 | 10 | 返回数量，最大50 |

**请求示例**:
```javascript
GET /miniapp/search/hot?type=all&limit=10
```

**响应参数**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    {
      "id": 1,
      "keyword": "心理咨询师",
      "searchType": "all",
      "searchCount": 150,
      "hotScore": 1500.00,
      "lastSearchTime": "2024-01-01T10:00:00"
    },
    {
      "id": 2,
      "keyword": "焦虑症",
      "searchType": "consultant",
      "searchCount": 120,
      "hotScore": 1200.00,
      "lastSearchTime": "2024-01-01T09:30:00"
    }
  ]
}
```

### 4. 搜索历史

**接口地址**: `GET /miniapp/search/history`

**接口描述**: 获取用户搜索历史记录（需登录）

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| limit | Integer | 否 | 10 | 返回数量，最大50 |

**请求示例**:
```javascript
GET /miniapp/search/history?limit=10
```

**响应参数**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    "心理咨询师",
    "焦虑症治疗",
    "睡眠冥想",
    "情绪测评"
  ]
}
```

### 5. 清除搜索历史

**接口地址**: `DELETE /miniapp/search/history`

**接口描述**: 清除用户搜索历史记录（需登录）

**请求示例**:
```javascript
DELETE /miniapp/search/history
```

**响应参数**:
```json
{
  "code": 200,
  "msg": "清除成功"
}
```

### 6. 快速搜索

**接口地址**: `GET /miniapp/search/quick`

**接口描述**: 简化版搜索，返回少量结果，适用于搜索建议场景

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| keyword | String | 是 | - | 搜索关键词 |
| type | String | 否 | all | 搜索类型 |
| limit | Integer | 否 | 5 | 返回数量，最大10 |

**请求示例**:
```javascript
GET /miniapp/search/quick?keyword=心理&type=all&limit=5
```

### 7. 搜索统计

**接口地址**: `GET /miniapp/search/statistics`

**接口描述**: 获取搜索相关统计信息

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| type | String | 否 | all | 搜索类型 |

**响应参数**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "hotSearches": [...],
    "totalHotSearches": 10
  }
}
```

### 8. 搜索类型列表

**接口地址**: `GET /miniapp/search/types`

**接口描述**: 获取支持的搜索类型列表

**响应参数**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "all": "全部",
    "consultant": "咨询师",
    "course": "课程",
    "meditation": "冥想",
    "assessment": "测评"
  }
}
```

### 9. 关键词联想

**接口地址**: `GET /miniapp/search/associate`

**接口描述**: 获取关键词联想建议

**请求参数**:
| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| keyword | String | 是 | - | 关键词 |
| limit | Integer | 否 | 5 | 返回数量 |

**响应参数**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": [
    "心理咨询师",
    "心理测评",
    "心理课程"
  ]
}
```

## 🖥️ 后台管理搜索接口

### 1. 搜索记录管理

#### 1.1 查询搜索记录列表

**接口地址**: `GET /system/search/record/list`

**权限要求**: `search:record:list`

**接口描述**: 查询搜索记录列表，支持分页和条件查询

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码，默认1 |
| pageSize | Integer | 否 | 每页数量，默认10 |
| userId | Long | 否 | 用户ID |
| keyword | String | 否 | 搜索关键词 |
| searchType | String | 否 | 搜索类型 |
| ipAddress | String | 否 | IP地址 |

**请求示例**:
```javascript
GET /system/search/record/list?pageNum=1&pageSize=10&keyword=心理咨询
```

**响应参数**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "userId": 100,
      "keyword": "心理咨询师",
      "searchType": "consultant",
      "resultCount": 15,
      "searchTime": "2024-01-01T10:00:00",
      "ipAddress": "***********",
      "userAgent": "Mozilla/5.0..."
    }
  ],
  "total": 100
}
```

#### 1.2 导出搜索记录

**接口地址**: `POST /system/search/record/export`

**权限要求**: `search:record:export`

**接口描述**: 导出搜索记录数据为Excel文件

#### 1.3 新增搜索记录

**接口地址**: `POST /system/search/record`

**权限要求**: `search:record:add`

**请求体**:
```json
{
  "userId": 100,
  "keyword": "心理咨询",
  "searchType": "all",
  "resultCount": 25,
  "ipAddress": "***********"
}
```

#### 1.4 修改搜索记录

**接口地址**: `PUT /system/search/record`

**权限要求**: `search:record:edit`

#### 1.5 删除搜索记录

**接口地址**: `DELETE /system/search/record/{ids}`

**权限要求**: `search:record:remove`

**路径参数**:
| 参数名 | 类型 | 说明 |
|--------|------|------|
| ids | Long[] | 搜索记录ID数组 |

### 2. 热门搜索管理

#### 2.1 查询热门搜索列表

**接口地址**: `GET /system/search/hot/list`

**权限要求**: `search:hot:list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |
| keyword | String | 否 | 关键词 |
| searchType | String | 否 | 搜索类型 |
| status | String | 否 | 状态：0正常，1停用 |

**响应参数**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "keyword": "心理咨询师",
      "searchType": "all",
      "searchCount": 150,
      "lastSearchTime": "2024-01-01T10:00:00",
      "hotScore": 1500.00,
      "status": "0",
      "createTime": "2024-01-01T08:00:00"
    }
  ],
  "total": 50
}
```

#### 2.2 导出热门搜索

**接口地址**: `POST /system/search/hot/export`

**权限要求**: `search:hot:export`

#### 2.3 新增热门搜索

**接口地址**: `POST /system/search/hot`

**权限要求**: `search:hot:add`

**请求体**:
```json
{
  "keyword": "新关键词",
  "searchType": "all",
  "searchCount": 1,
  "hotScore": 10.00,
  "status": "0"
}
```

#### 2.4 修改热门搜索

**接口地址**: `PUT /system/search/hot`

**权限要求**: `search:hot:edit`

#### 2.5 删除热门搜索

**接口地址**: `DELETE /system/search/hot/{ids}`

**权限要求**: `search:hot:remove`

### 3. 搜索建议管理

#### 3.1 查询搜索建议列表

**接口地址**: `GET /system/search/suggestion/list`

**权限要求**: `search:suggestion:list`

**请求参数**:
| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |
| keyword | String | 否 | 关键词 |
| suggestionType | String | 否 | 建议类型：auto/manual/hot |
| status | String | 否 | 状态：0正常，1停用 |

**响应参数**:
```json
{
  "code": 200,
  "msg": "查询成功",
  "rows": [
    {
      "id": 1,
      "keyword": "心理咨询师",
      "suggestionType": "manual",
      "searchCount": 50,
      "priority": 100,
      "status": "0",
      "createTime": "2024-01-01T08:00:00"
    }
  ],
  "total": 30
}
```

#### 3.2 导出搜索建议

**接口地址**: `POST /system/search/suggestion/export`

**权限要求**: `search:suggestion:export`

#### 3.3 新增搜索建议

**接口地址**: `POST /system/search/suggestion`

**权限要求**: `search:suggestion:add`

**请求体**:
```json
{
  "keyword": "新建议词",
  "suggestionType": "manual",
  "priority": 80,
  "status": "0"
}
```

#### 3.4 修改搜索建议

**接口地址**: `PUT /system/search/suggestion`

**权限要求**: `search:suggestion:edit`

#### 3.5 删除搜索建议

**接口地址**: `DELETE /system/search/suggestion/{ids}`

**权限要求**: `search:suggestion:remove`

## 🔧 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未登录或登录过期 |
| 403 | 无权限访问 |
| 500 | 服务器内部错误 |

## 📝 接口调用示例

### 前端JavaScript调用示例

```javascript
// 1. 全局搜索
const searchResults = await fetch('/miniapp/search/global?keyword=心理咨询&type=all')
  .then(response => response.json());

// 2. 获取搜索建议
const suggestions = await fetch('/miniapp/search/suggestions?keyword=心理')
  .then(response => response.json());

// 3. 获取热门搜索
const hotSearches = await fetch('/miniapp/search/hot?type=all&limit=10')
  .then(response => response.json());

// 4. 清除搜索历史
const clearResult = await fetch('/miniapp/search/history', {
  method: 'DELETE'
}).then(response => response.json());
```

### 后台管理调用示例

```javascript
// 1. 查询搜索记录
const records = await fetch('/system/search/record/list?pageNum=1&pageSize=10', {
  headers: {
    'Authorization': 'Bearer ' + token
  }
}).then(response => response.json());

// 2. 新增热门搜索
const result = await fetch('/system/search/hot', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  },
  body: JSON.stringify({
    keyword: '新关键词',
    searchType: 'all',
    hotScore: 100.00,
    status: '0'
  })
}).then(response => response.json());

// 3. 批量删除搜索建议
const deleteResult = await fetch('/system/search/suggestion/1,2,3', {
  method: 'DELETE',
  headers: {
    'Authorization': 'Bearer ' + token
  }
}).then(response => response.json());
```

## 🎯 接口特点

### 小程序端接口特点
- **无需权限验证**：支持匿名搜索
- **用户数据隔离**：登录用户只能访问自己的搜索历史
- **性能优化**：支持分页查询和快速搜索
- **智能建议**：提供搜索建议和关键词联想
- **多模块搜索**：支持咨询师、课程、冥想、测评统一搜索

### 后台管理接口特点
- **权限控制严格**：所有接口都需要相应权限
- **数据管理完整**：支持CRUD操作和数据导出
- **批量操作**：支持批量删除和批量导出
- **审计功能**：记录操作日志和数据变更
- **统计分析**：提供搜索数据分析和热度统计

## 🚀 使用建议

### 前端开发建议
1. **优先使用小程序端接口**：提供更好的用户体验
2. **合理使用分页**：避免一次性加载大量数据
3. **实现搜索建议**：提升用户搜索体验
4. **缓存热门搜索**：减少重复请求

### 后台管理建议
1. **定期清理数据**：清理过期的搜索记录
2. **监控搜索热度**：分析用户搜索行为
3. **优化搜索建议**：根据搜索数据调整建议词库
4. **权限管理**：严格控制数据访问权限

### 性能优化建议
1. **使用索引**：为搜索相关字段创建合适的索引
2. **分页查询**：避免全表扫描
3. **缓存机制**：缓存热门搜索和建议数据
4. **异步处理**：搜索记录可以异步写入

## 📊 接口总览

### 小程序端接口（9个）
- 全局搜索、搜索建议、热门搜索
- 搜索历史、清除历史、快速搜索
- 搜索统计、搜索类型、关键词联想

### 后台管理接口（15个）
- 搜索记录管理（5个接口）
- 热门搜索管理（5个接口）
- 搜索建议管理（5个接口）

### 搜索模块支持
- **咨询师搜索**：姓名、介绍、专长、地区
- **课程搜索**：标题、简介、标签、讲师
- **冥想搜索**：标题、描述、引导师、标签
- **测评搜索**：标题、描述、介绍、类型

现在搜索功能的接口文档已经完整，包含了前端和后台的所有接口！🎉
