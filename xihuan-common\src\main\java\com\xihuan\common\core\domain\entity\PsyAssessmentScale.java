package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 心理测评量表主表对象 psy_t_scale
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentScale extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表名称 */
    @Excel(name = "量表名称")
    @NotBlank(message = "量表名称不能为空")
    @Size(max = 100, message = "量表名称不能超过100个字符")
    private String scaleName;

    /** 量表编码 */
    @Excel(name = "量表编码")
    @NotBlank(message = "量表编码不能为空")
    @Size(max = 50, message = "量表编码不能超过50个字符")
    private String scaleCode;

    /** 量表描述 */
    @Excel(name = "量表描述")
    private String description;

    /** 测评说明 */
    @Excel(name = "测评说明")
    private String instruction;

    /** 分类ID */
    @Excel(name = "分类ID", cellType = Excel.ColumnType.NUMERIC)
    private Long categoryId;

    /** 量表作者 */
    @Excel(name = "量表作者")
    @Size(max = 100, message = "作者名称不能超过100个字符")
    private String author;

    /** 版本号 */
    @Excel(name = "版本号")
    @Size(max = 20, message = "版本号不能超过20个字符")
    private String version;

    /** 题目数量 */
    @Excel(name = "题目数量")
    @Min(value = 1, message = "题目数量不能少于1")
    private Integer questionCount;

    /** 时间限制(分钟) */
    @Excel(name = "时间限制")
    @Min(value = 1, message = "时间限制不能少于1分钟")
    private Integer timeLimit;

    /** 难度等级(1=简单 2=中等 3=困难) */
    @Excel(name = "难度等级", readConverterExp = "1=简单,2=中等,3=困难")
    @Min(value = 1, message = "难度等级最小值为1")
    @Max(value = 3, message = "难度等级最大值为3")
    private Integer difficultyLevel;

    /** 价格 */
    @Excel(name = "价格")
    @DecimalMin(value = "0.00", message = "价格不能为负数")
    private BigDecimal price;

    /** 是否免费(0=付费 1=免费) */
    @Excel(name = "是否免费", readConverterExp = "0=付费,1=免费")
    private Integer isFree;

    /** 封面图片 */
    @Excel(name = "封面图片")
    @Size(max = 500, message = "封面图片路径不能超过500个字符")
    private String coverImage;

    /** 标签(JSON格式) */
    @Excel(name = "标签")
    @Size(max = 500, message = "标签不能超过500个字符")
    private String tags;

    /** 状态(0=未发布 1=已发布 2=下架) */
    @Excel(name = "状态", readConverterExp = "0=未发布,1=已发布,2=下架")
    private Integer status;

    /** 查看次数 */
    @Excel(name = "查看次数")
    private Integer viewCount;

    /** 测试次数 */
    @Excel(name = "测试次数")
    private Integer testCount;

    /** 平均评分 */
    @Excel(name = "平均评分")
    private BigDecimal ratingAvg;

    /** 评分人数 */
    @Excel(name = "评分人数")
    private Integer ratingCount;

    /** 搜索关键词 */
    @Excel(name = "搜索关键词")
    @Size(max = 500, message = "搜索关键词不能超过500个字符")
    private String searchKeywords;

    /** 被搜索次数 */
    @Excel(name = "被搜索次数")
    private Integer searchCount;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 分类信息 */
    private PsyCategory category;

    /** 题目列表 */
    private List<PsyAssessmentQuestion> questions;

    /** 分类ID列表（用于多分类关系） */
    private List<Long> categoryIds;

    /** 分类列表（用于多分类关系） */
    private List<PsyCategory> categories;

    /** 结果解释列表 */
    private List<PsyAssessmentInterpretation> interpretations;

    /** 用户是否已购买 */
    private Boolean purchased;

    /** 用户测评记录 */
    private PsyAssessmentRecord userRecord;

    /** 用户评价 */
    private PsyAssessmentReview userReview;

    // 扩展字段
    /** 是否可以测评 */
    private Boolean canTest;

    /** 剩余测评次数 */
    private Integer remainingTests;

    /** 最近测评时间 */
    private String lastTestTime;

    /** 最高分数 */
    private BigDecimal bestScore;

    /** 测评次数 */
    private Integer userTestCount;

    // 统计字段
    /** 今日测试次数 */
    private Integer todayTestCount;

    /** 本周测试次数 */
    private Integer weekTestCount;

    /** 本月测试次数 */
    private Integer monthTestCount;

    /** 完成率 */
    private BigDecimal completionRate;

    /** 平均用时 */
    private Integer avgDuration;

    // 常量定义
    /** 状态：未发布 */
    public static final Integer STATUS_UNPUBLISHED = 0;
    
    /** 状态：已发布 */
    public static final Integer STATUS_PUBLISHED = 1;
    
    /** 状态：已下架 */
    public static final Integer STATUS_OFFLINE = 2;

    /** 难度：简单 */
    public static final Integer DIFFICULTY_EASY = 1;
    
    /** 难度：中等 */
    public static final Integer DIFFICULTY_MEDIUM = 2;
    
    /** 难度：困难 */
    public static final Integer DIFFICULTY_HARD = 3;

    /** 免费 */
    public static final Integer FREE_YES = 1;
    
    /** 付费 */
    public static final Integer FREE_NO = 0;

    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "未发布";
            case 1: return "已发布";
            case 2: return "已下架";
            default: return "未知";
        }
    }

    /**
     * 获取难度描述
     */
    public String getDifficultyDesc() {
        if (difficultyLevel == null) return "";
        switch (difficultyLevel) {
            case 1: return "简单";
            case 2: return "中等";
            case 3: return "困难";
            default: return "未知";
        }
    }

    /**
     * 获取价格描述
     */
    public String getPriceDesc() {
        if (isFree != null && isFree == 1) {
            return "免费";
        }
        if (price != null) {
            return "¥" + price.toString();
        }
        return "免费";
    }

    /**
     * 是否已发布
     */
    public boolean isPublished() {
        return STATUS_PUBLISHED.equals(status);
    }

    /**
     * 是否免费
     */
    public boolean isFreeScale() {
        return FREE_YES.equals(isFree);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }
}
