package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentOrder;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 心理测评订单Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentOrderMapper {
    
    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectOrderList(PsyAssessmentOrder order);

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    PsyAssessmentOrder selectOrderById(Long id);

    /**
     * 查询订单详情（包含用户、量表等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyAssessmentOrder selectOrderWithDetails(Long id);

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单信息
     */
    PsyAssessmentOrder selectOrderByOrderNo(String orderNo);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectOrdersByUserId(Long userId);

    /**
     * 根据量表ID查询订单列表
     * 
     * @param scaleId 量表ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectOrdersByScaleId(Long scaleId);

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int insertOrder(PsyAssessmentOrder order);

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    int updateOrder(PsyAssessmentOrder order);

    /**
     * 删除订单
     * 
     * @param id 订单ID
     * @return 结果
     */
    int deleteOrderById(Long id);

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 查询用户在指定量表的有效订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 订单信息
     */
    PsyAssessmentOrder selectValidOrderByUserAndScale(@Param("userId") Long userId, @Param("scaleId") Long scaleId);

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单编号
     * @param paymentStatus 支付状态
     * @param payTime 支付时间
     * @return 结果
     */
    int updatePaymentStatus(@Param("orderNo") String orderNo, 
                           @Param("paymentStatus") Integer paymentStatus, 
                           @Param("payTime") Date payTime);

    /**
     * 完成订单
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    int completeOrder(String orderNo);

    /**
     * 取消订单
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    int cancelOrder(String orderNo);

    /**
     * 申请退款
     * 
     * @param orderNo 订单编号
     * @param refundAmount 退款金额
     * @param refundReason 退款原因
     * @return 结果
     */
    int applyRefund(@Param("orderNo") String orderNo, 
                   @Param("refundAmount") BigDecimal refundAmount, 
                   @Param("refundReason") String refundReason);

    /**
     * 处理退款
     * 
     * @param orderNo 订单编号
     * @param refundTime 退款时间
     * @return 结果
     */
    int processRefund(@Param("orderNo") String orderNo, @Param("refundTime") Date refundTime);

    /**
     * 查询待支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectPendingOrders(Long userId);

    /**
     * 查询已支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectPaidOrders(Long userId);

    /**
     * 查询已完成订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectCompletedOrders(Long userId);

    /**
     * 查询已取消订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectCancelledOrders(Long userId);

    /**
     * 查询过期订单
     * 
     * @return 订单集合
     */
    List<PsyAssessmentOrder> selectExpiredOrders();

    /**
     * 查询订单统计信息
     * 
     * @return 统计信息
     */
    java.util.Map<String, Object> selectOrderStats();

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectUserOrderStats(Long userId);

    /**
     * 查询量表订单统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectScaleOrderStats(Long scaleId);

    /**
     * 查询订单收入统计
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectOrderIncomeStats(@Param("startDate") Date startDate, @Param("endDate") Date endDate);

    /**
     * 查询热销量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<java.util.Map<String, Object>> selectHotSalesScales(@Param("limit") Integer limit);

    /**
     * 检查订单编号唯一性
     * 
     * @param orderNo 订单编号
     * @return 数量
     */
    int checkOrderNoUnique(String orderNo);

    /**
     * 统计订单数量
     * 
     * @param order 查询条件
     * @return 数量
     */
    int countOrders(PsyAssessmentOrder order);

    /**
     * 查询订单趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectOrderTrend(@Param("days") Integer days);
}
