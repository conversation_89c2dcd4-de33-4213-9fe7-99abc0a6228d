package com.xihuan.common.core.domain.consultant;


import lombok.Data;

import java.util.Date;

/**
 * 咨询师督导经历表实体
 */
@Data
public class PsySupervision {
    private Long id;
    private Long consultantId;  // 咨询师ID
    private String type;        // 督导类型（个体/团体）
    private Integer hours;      // 督导小时数
    private String supervisor;  // 督导师姓名
    private Date startDate;     // 开始日期
    private Date endDate;       // 结束日期
    private String certificate; // 督导证明路径
}