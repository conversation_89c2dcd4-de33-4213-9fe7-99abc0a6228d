# 搜索功能数据库字段总结

## 📊 新增表结构

### 1. 搜索记录表 (`psy_search_record`)
```sql
CREATE TABLE `psy_search_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '搜索记录ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（可为空，支持匿名搜索）',
  `keyword` varchar(200) NOT NULL COMMENT '搜索关键词',
  `search_type` varchar(50) DEFAULT 'all' COMMENT '搜索类型：all/consultant/course/meditation/assessment',
  `result_count` int(11) DEFAULT 0 COMMENT '搜索结果数量',
  `search_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_search_time` (`search_time`),
  KEY `idx_search_type` (`search_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索记录表';
```

### 2. 热门搜索表 (`psy_hot_search`)
```sql
CREATE TABLE `psy_hot_search` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '热门搜索ID',
  `keyword` varchar(200) NOT NULL COMMENT '搜索关键词',
  `search_type` varchar(50) DEFAULT 'all' COMMENT '搜索类型',
  `search_count` int(11) DEFAULT 1 COMMENT '搜索次数',
  `last_search_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后搜索时间',
  `hot_score` decimal(10,2) DEFAULT 0.00 COMMENT '热度分数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_keyword_type` (`keyword`, `search_type`),
  KEY `idx_hot_score` (`hot_score`),
  KEY `idx_search_count` (`search_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='热门搜索表';
```

### 3. 搜索建议表 (`psy_search_suggestion`)
```sql
CREATE TABLE `psy_search_suggestion` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '搜索建议ID',
  `keyword` varchar(200) NOT NULL COMMENT '建议关键词',
  `suggestion_type` varchar(50) DEFAULT 'auto' COMMENT '建议类型：auto/manual/hot',
  `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数',
  `priority` int(11) DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_keyword` (`keyword`),
  KEY `idx_priority` (`priority`),
  KEY `idx_search_count` (`search_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索建议表';
```

## 🔧 现有表新增字段

### 1. 咨询师表 (`psy_consultants`)
```sql
ALTER TABLE `psy_consultants` 
ADD COLUMN `search_keywords` text COMMENT '搜索关键词（姓名、专长、地区、标签等）',
ADD COLUMN `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数',
ADD COLUMN `view_count` int(11) DEFAULT 0 COMMENT '查看次数';
```

**字段说明**：
- `search_keywords`：包含姓名、个人介绍、职称、地区、性别标签等搜索关键词
- `search_count`：记录该咨询师被搜索的次数
- `view_count`：记录该咨询师详情页被查看的次数

### 2. 课程表 (`psy_course`)
```sql
ALTER TABLE `psy_course` 
ADD COLUMN `search_keywords` text COMMENT '搜索关键词（课程名、讲师、标签、简介等）',
ADD COLUMN `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数';
```

**字段说明**：
- `search_keywords`：包含课程标题、简介、标签、难度等级、免费/付费标签等
- `search_count`：记录该课程被搜索的次数
- `view_count`：已存在，不需要添加

### 3. 冥想表 (`psy_meditation`)
```sql
ALTER TABLE `psy_meditation` 
ADD COLUMN `search_keywords` text COMMENT '搜索关键词（标题、引导师、标签、描述等）',
ADD COLUMN `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数';
```

**字段说明**：
- `search_keywords`：包含冥想标题、描述、引导师、标签、难度等级、时长分类等
- `search_count`：记录该冥想被搜索的次数
- `play_count`：已存在，相当于view_count



## 📈 搜索关键词内容规则

### 1. 咨询师搜索关键词包含
- 基础信息：姓名、个人介绍、职称
- 地理信息：省份、城市、区域
- 特征标签：性别标签（男咨询师/女咨询师）
- 能力标签：可讲课、可出差
- 状态标签：可预约、休息中

### 2. 课程搜索关键词包含
- 基础信息：课程标题、简介、标签
- 难度标签：入门课程、进阶课程、高级课程
- 价格标签：免费课程、付费课程
- 状态标签：已发布、未发布、已下架

### 3. 冥想搜索关键词包含
- 基础信息：冥想标题、描述、引导师、标签
- 难度标签：入门冥想、进阶冥想、高级冥想
- 价格标签：免费冥想、付费冥想
- 时长标签：短时冥想、中时冥想、长时冥想
- 状态标签：已发布、未发布、已下架



## 🔍 搜索索引优化

### 1. 搜索相关索引
```sql
CREATE INDEX idx_psy_consultants_search ON psy_consultants(search_count, view_count);
CREATE INDEX idx_psy_course_search ON psy_course(search_count, view_count);
CREATE INDEX idx_psy_meditation_search ON psy_meditation(search_count, play_count);

```

### 2. 搜索记录索引
```sql
CREATE INDEX idx_psy_search_record_keyword_time ON psy_search_record(keyword, search_time);
CREATE INDEX idx_psy_search_record_user_time ON psy_search_record(user_id, search_time);
CREATE INDEX idx_psy_hot_search_type_score ON psy_hot_search(search_type, hot_score);
CREATE INDEX idx_psy_search_suggestion_keyword_priority ON psy_search_suggestion(keyword, priority);
```

## 🎯 搜索类型支持

- `all` - 全部内容（咨询师、课程、冥想）
- `consultant` - 咨询师
- `course` - 课程
- `meditation` - 冥想

## 📝 数据初始化

### 1. 执行顺序
1. `sql/search_tables.sql` - 创建搜索相关表
2. `sql/add_search_fields.sql` - 为现有表添加搜索字段
3. `sql/init_search_keywords.sql` - 初始化现有数据的搜索关键词
4. `sql/search_init_data.sql` - 插入搜索建议和热门搜索数据

### 2. 注意事项
- 执行前请备份数据库
- 根据实际表名调整SQL语句
- 可选择性执行全文索引创建（需要MySQL 5.7+）
- 搜索关键词会根据现有数据自动生成

## 🚀 功能特点

1. **智能关键词生成**：根据实体字段自动生成搜索关键词
2. **多维度搜索**：支持按类型、状态、价格等多维度搜索
3. **搜索统计**：记录搜索行为和热度统计
4. **搜索建议**：提供智能搜索建议和热门搜索
5. **性能优化**：合理的索引设计保证搜索性能
