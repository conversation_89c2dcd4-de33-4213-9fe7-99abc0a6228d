package com.xihuan.common.utils.wechat;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xihuan.common.config.WxPayConfig;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.common.utils.http.HttpUtils;
import com.xihuan.common.utils.sign.Md5Utils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.crypto.Cipher;
import javax.crypto.Mac;
import javax.crypto.spec.GCMParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.security.cert.X509Certificate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 微信支付工具类
 */
@Component
public class WxPayUtils {
    private static final Logger log = LoggerFactory.getLogger(WxPayUtils.class);

    @Autowired
    private WxPayConfig wxPayConfig;

    /**
     * 统一下单接口 - 小程序支付
     *
     * @param orderNo 订单号
     * @param totalFee 总金额（单位：分）
     * @param description 商品描述
     * @param openid 用户openid
     * @param ip 客户端IP
     * @return 支付参数，用于小程序调起支付
     */
    public Map<String, Object> createOrder(String orderNo, int totalFee, String description, String openid, String ip) {
        if (wxPayConfig.getUseV3()) {
            return createOrderV3(orderNo, totalFee, description, openid, ip);
        } else {
            return createOrderV2(orderNo, totalFee, description, openid, ip);
        }
    }

    /**
     * 统一下单接口 - V3版本
     */
    private Map<String, Object> createOrderV3(String orderNo, int totalFee, String description, String openid, String ip) {
        try {
            // 构建请求参数
            Map<String, Object> requestData = new HashMap<>();
            requestData.put("appid", wxPayConfig.getAppId());
            requestData.put("mchid", wxPayConfig.getMchId());
            requestData.put("description", description);
            requestData.put("out_trade_no", orderNo);
            requestData.put("notify_url", wxPayConfig.getNotifyUrl());

            // 订单金额
            Map<String, Object> amount = new HashMap<>();
            amount.put("total", totalFee);
            amount.put("currency", "CNY");
            requestData.put("amount", amount);

            // 支付者
            Map<String, Object> payer = new HashMap<>();
            payer.put("openid", openid);
            requestData.put("payer", payer);

            // 附加数据
            Map<String, Object> scene_info = new HashMap<>();
            Map<String, Object> h5_info = new HashMap<>();
            h5_info.put("type", "Wap");
            scene_info.put("payer_client_ip", ip);
            scene_info.put("h5_info", h5_info);
            requestData.put("scene_info", scene_info);

            // 设置过期时间
            LocalDateTime expireTime = LocalDateTime.now().plusMinutes(wxPayConfig.getTimeoutMinutes());
            requestData.put("time_expire", expireTime.format(DateTimeFormatter.ISO_DATE_TIME));

            // 发送请求
            String url = "https://api.mch.weixin.qq.com/v3/pay/transactions/jsapi";
            String requestBody = JSON.toJSONString(requestData);
            
            // TODO: 实现V3接口的签名和请求逻辑
            // 这里需要使用V3接口的签名方式，涉及到私钥签名等复杂操作
            // 为简化示例，这里仅返回模拟数据
            log.info("微信支付V3下单请求参数: {}", requestBody);
            
            // 模拟返回数据
            String prepayId = "wx" + System.currentTimeMillis();
            
            // 构建小程序调起支付的参数
            Map<String, Object> payParams = new HashMap<>();
            payParams.put("appId", wxPayConfig.getAppId());
            payParams.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
            payParams.put("nonceStr", generateNonceStr());
            payParams.put("package", "prepay_id=" + prepayId);
            payParams.put("signType", "RSA");
            
            // TODO: 实现V3接口的签名
            payParams.put("paySign", "模拟的V3签名");
            
            return payParams;
        } catch (Exception e) {
            log.error("微信支付V3下单失败", e);
            throw new ServiceException("微信支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 统一下单接口 - V2版本
     */
    private Map<String, Object> createOrderV2(String orderNo, int totalFee, String description, String openid, String ip) {
        try {
            // 构建请求参数
            SortedMap<String, String> requestData = new TreeMap<>();
            requestData.put("appid", wxPayConfig.getAppId());
            requestData.put("mch_id", wxPayConfig.getMchId());
            requestData.put("nonce_str", generateNonceStr());
            requestData.put("body", description);
            requestData.put("out_trade_no", orderNo);
            requestData.put("total_fee", String.valueOf(totalFee));
            requestData.put("spbill_create_ip", ip);
            requestData.put("notify_url", wxPayConfig.getNotifyUrl());
            requestData.put("trade_type", "JSAPI");
            requestData.put("openid", openid);
            
            // 计算签名
            String sign = generateSignV2(requestData);
            requestData.put("sign", sign);
            
            // 将参数转换为XML
            String xmlData = mapToXml(requestData);
            
            // 发送请求
            String url = "https://api.mch.weixin.qq.com/pay/unifiedorder";
            if (wxPayConfig.getUseSandbox()) {
                url = "https://api.mch.weixin.qq.com/sandboxnew/pay/unifiedorder";
            }
            
            log.info("微信支付V2下单请求参数: {}", xmlData);
            String result = HttpUtils.sendPost(url, xmlData, "application/xml");
            log.info("微信支付V2下单响应: {}", result);
            
            // 解析返回结果
            Map<String, String> responseMap = xmlToMap(result);
            
            if ("SUCCESS".equals(responseMap.get("return_code")) && "SUCCESS".equals(responseMap.get("result_code"))) {
                String prepayId = responseMap.get("prepay_id");
                
                // 构建小程序调起支付的参数
                Map<String, Object> payParams = new HashMap<>();
                payParams.put("appId", wxPayConfig.getAppId());
                payParams.put("timeStamp", String.valueOf(System.currentTimeMillis() / 1000));
                payParams.put("nonceStr", generateNonceStr());
                payParams.put("package", "prepay_id=" + prepayId);
                payParams.put("signType", "MD5");
                
                // 计算签名
                SortedMap<String, String> signParams = new TreeMap<>();
                signParams.put("appId", wxPayConfig.getAppId());
                signParams.put("timeStamp", payParams.get("timeStamp").toString());
                signParams.put("nonceStr", payParams.get("nonceStr").toString());
                signParams.put("package", payParams.get("package").toString());
                signParams.put("signType", "MD5");
                
                String paySign = generateSignV2(signParams);
                payParams.put("paySign", paySign);
                
                return payParams;
            } else {
                log.error("微信支付V2下单失败: {}", responseMap.get("return_msg"));
                throw new ServiceException("微信支付下单失败: " + responseMap.get("return_msg"));
            }
        } catch (Exception e) {
            log.error("微信支付V2下单失败", e);
            throw new ServiceException("微信支付下单失败: " + e.getMessage());
        }
    }

    /**
     * 生成随机字符串
     */
    private String generateNonceStr() {
        return UUID.randomUUID().toString().replaceAll("-", "");
    }

    /**
     * 生成V2版本签名
     */
    private String generateSignV2(SortedMap<String, String> params) {
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (StringUtils.isNotEmpty(entry.getValue())) {
                sb.append(entry.getKey()).append("=").append(entry.getValue()).append("&");
            }
        }
        sb.append("key=").append(wxPayConfig.getMchKey());
        return Md5Utils.hash(sb.toString()).toUpperCase();
    }

    /**
     * Map转XML
     */
    private String mapToXml(Map<String, String> params) {
        StringBuilder sb = new StringBuilder();
        sb.append("<xml>");
        for (Map.Entry<String, String> entry : params.entrySet()) {
            sb.append("<").append(entry.getKey()).append(">");
            sb.append(entry.getValue());
            sb.append("</").append(entry.getKey()).append(">");
        }
        sb.append("</xml>");
        return sb.toString();
    }

    /**
     * XML转Map
     */
    private Map<String, String> xmlToMap(String xml) {
        Map<String, String> map = new HashMap<>();
        // 简单解析，实际项目中建议使用XML解析库
        String[] elements = xml.replaceAll("</?xml>", "").split("</");
        for (String element : elements) {
            if (element.contains(">")) {
                String[] keyValue = element.split(">");
                if (keyValue.length >= 2) {
                    String key = keyValue[0].trim();
                    String value = keyValue[1].trim();
                    map.put(key, value);
                }
            }
        }
        return map;
    }
}
