package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyCourseChapter;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyCourseChapterService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 课程章节内容表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/chapter")
public class PsyCourseChapterController extends BaseController {
    
    @Autowired
    private IPsyCourseChapterService chapterService;

    /**
     * 查询章节列表
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyCourseChapter chapter) {
        startPage();
        List<PsyCourseChapter> list = chapterService.selectChapterList(chapter);
        return getDataTable(list);
    }

    /**
     * 导出章节列表
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:export')")
    @Log(title = "章节", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyCourseChapter chapter) {
        List<PsyCourseChapter> list = chapterService.selectChapterList(chapter);
        ExcelUtil<PsyCourseChapter> util = new ExcelUtil<PsyCourseChapter>(PsyCourseChapter.class);
        util.exportExcel(response, list, "章节数据");
    }

    /**
     * 获取章节详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(chapterService.selectChapterById(id));
    }

    /**
     * 新增章节
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:add')")
    @Log(title = "章节", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyCourseChapter chapter) {
        return toAjax(chapterService.insertChapter(chapter));
    }

    /**
     * 修改章节
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:edit')")
    @Log(title = "章节", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyCourseChapter chapter) {
        return toAjax(chapterService.updateChapter(chapter));
    }

    /**
     * 删除章节
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:remove')")
    @Log(title = "章节", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(chapterService.deleteChapterByIds(ids));
    }

    /**
     * 根据课程ID查询章节列表
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:list')")
    @GetMapping("/course/{courseId}")
    public AjaxResult getChaptersByCourse(@PathVariable Long courseId) {
        List<PsyCourseChapter> list = chapterService.selectChaptersByCourseId(courseId);
        return success(list);
    }

    /**
     * 根据课程ID查询章节树结构
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:list')")
    @GetMapping("/tree/{courseId}")
    public AjaxResult getChapterTree(@PathVariable Long courseId) {
        List<PsyCourseChapter> list = chapterService.selectChapterTreeByCourseId(courseId);
        return success(list);
    }

    /**
     * 更新章节排序
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:edit')")
    @Log(title = "章节排序", businessType = BusinessType.UPDATE)
    @PutMapping("/order/{id}/{order}")
    public AjaxResult updateOrder(@PathVariable Long id, @PathVariable Integer order) {
        return toAjax(chapterService.updateChapterOrder(id, order));
    }

    /**
     * 获取所有章节（仅返回id、父id、名称）
     */
    @PreAuthorize("@ss.hasPermi('system:chapter:list')")
    @GetMapping("/all")
    public AjaxResult getAllChapters() {
        List<PsyCourseChapter> list = chapterService.selectAllChapters();
        return success(list);
    }
}
