-- 为系统公共时间槽表添加状态字段
-- 状态：0-可用，2-已过期

-- 添加状态字段
ALTER TABLE `psy_system_time_slot` 
ADD COLUMN `status` int DEFAULT '0' COMMENT '状态（0-可用，2-已过期）' 
AFTER `has_available`;

-- 更新现有数据的状态
-- 根据时间判断是否过期
UPDATE `psy_system_time_slot` 
SET `status` = CASE 
    WHEN STR_TO_DATE(CONCAT(date_key, ' ', end_time), '%Y-%m-%d %H:%i:%s') < NOW() THEN 2  -- 已过期
    ELSE 0  -- 可用
END
WHERE `del_flag` = 0;

-- 创建状态字段的索引
CREATE INDEX `idx_status` ON `psy_system_time_slot` (`status`);

-- 创建复合索引：状态+日期+中心
CREATE INDEX `idx_status_date_center` ON `psy_system_time_slot` (`status`, `date_key`, `center_id`);

-- 查看更新结果
SELECT 
    status,
    CASE status 
        WHEN 0 THEN '可用'
        WHEN 2 THEN '已过期'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count
FROM `psy_system_time_slot` 
WHERE `del_flag` = 0
GROUP BY status
ORDER BY status;

-- 查看今天的时间槽状态分布
SELECT 
    date_key,
    status,
    CASE status 
        WHEN 0 THEN '可用'
        WHEN 2 THEN '已过期'
        ELSE '未知'
    END as status_name,
    COUNT(*) as count
FROM `psy_system_time_slot` 
WHERE `del_flag` = 0 
  AND date_key = DATE_FORMAT(NOW(), '%Y-%m-%d')
GROUP BY date_key, status
ORDER BY date_key, status;
