package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.time.LocalTime;
import java.util.List;

/**
 * 预约记录表实体类
 * 对应数据库表：psy_time_appointment
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTimeAppointment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 预约ID（APT+时间戳） */
    private String id;

    /** 用户ID */
    private Long userId;

    /** 咨询中心ID */
    private Long centerId;

    /** 起始时间槽ID */
    private Long startSlotId;

    /** 预约日期 */
    private String dateKey;

    /** 预约开始时间 */
    private LocalTime startTime;

    /** 预约结束时间 */
    private LocalTime endTime;

    /** 咨询时长（分钟） */
    private Integer duration;

    /** 预约状态（0待确认 1已确认 2已完成 3已取消 4缺席） */
    private Integer status;

    /** 支付状态（0未支付 1已支付 2等待中 3取消支付） */
    private Integer paymentStatus;

    /** 支付单号 */
    private String paymentId;

    /** 删除标志 */
    private Integer delFlag;

    // 关联对象（非数据库字段）
    /** 起始时间槽信息 */
    private PsyTimeSlot startSlot;

    /** 关联的时间槽列表 */
    private List<PsyTimeSlot> timeSlots;

    /** 用户信息 */
    private com.xihuan.common.core.domain.entity.SysUser user;

    /** 咨询师信息 */
    private com.xihuan.common.core.domain.consultant.PsyConsultant consultant;
}
