package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTEnterprise;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 企业信息Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTEnterpriseMapper {
    
    /**
     * 查询企业列表
     * 
     * @param enterprise 企业信息
     * @return 企业集合
     */
    List<PsyTEnterprise> selectEnterpriseList(PsyTEnterprise enterprise);

    /**
     * 根据ID查询企业
     * 
     * @param id 企业ID
     * @return 企业信息
     */
    PsyTEnterprise selectEnterpriseById(Long id);

    /**
     * 查询企业详情（包含部门、测评计划等信息）
     * 
     * @param id 企业ID
     * @return 企业详情
     */
    PsyTEnterprise selectEnterpriseWithDetails(Long id);

    /**
     * 根据企业编码查询企业
     * 
     * @param enterpriseCode 企业编码
     * @return 企业信息
     */
    PsyTEnterprise selectEnterpriseByCode(String enterpriseCode);

    /**
     * 新增企业
     * 
     * @param enterprise 企业信息
     * @return 结果
     */
    int insertEnterprise(PsyTEnterprise enterprise);

    /**
     * 修改企业
     * 
     * @param enterprise 企业信息
     * @return 结果
     */
    int updateEnterprise(PsyTEnterprise enterprise);

    /**
     * 删除企业
     * 
     * @param id 企业ID
     * @return 结果
     */
    int deleteEnterpriseById(Long id);

    /**
     * 批量删除企业
     * 
     * @param ids 需要删除的企业ID
     * @return 结果
     */
    int deleteEnterpriseByIds(Long[] ids);

    /**
     * 检查企业编码唯一性
     * 
     * @param enterpriseCode 企业编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkEnterpriseCodeUnique(@Param("enterpriseCode") String enterpriseCode, 
                                 @Param("excludeId") Long excludeId);

    /**
     * 查询启用的企业列表
     * 
     * @return 企业集合
     */
    List<PsyTEnterprise> selectEnabledEnterprises();

    /**
     * 查询即将过期的企业列表
     * 
     * @param days 天数
     * @return 企业集合
     */
    List<PsyTEnterprise> selectExpiringEnterprises(@Param("days") Integer days);

    /**
     * 查询已过期的企业列表
     * 
     * @return 企业集合
     */
    List<PsyTEnterprise> selectExpiredEnterprises();

    /**
     * 更新企业测评使用次数
     * 
     * @param id 企业ID
     * @param count 使用次数
     * @return 结果
     */
    int updateAssessmentUsageCount(@Param("id") Long id, @Param("count") Integer count);

    /**
     * 增加企业测评使用次数
     * 
     * @param id 企业ID
     * @param increment 增量
     * @return 结果
     */
    int incrementAssessmentUsageCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 统计企业数量
     * 
     * @param enterprise 查询条件
     * @return 数量
     */
    int countEnterprises(PsyTEnterprise enterprise);

    /**
     * 查询企业统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectEnterpriseStats();

    /**
     * 查询企业类型统计
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> selectEnterpriseTypeStats();

    /**
     * 查询企业规模统计
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> selectEnterpriseScaleStats();

    /**
     * 查询企业测评使用统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    Map<String, Object> selectEnterpriseUsageStats(Long enterpriseId);

    /**
     * 查询企业测评趋势
     * 
     * @param enterpriseId 企业ID
     * @param days 天数
     * @return 趋势信息
     */
    List<Map<String, Object>> selectEnterpriseUsageTrend(@Param("enterpriseId") Long enterpriseId, 
                                                         @Param("days") Integer days);

    /**
     * 搜索企业
     * 
     * @param keyword 关键词
     * @param enterpriseType 企业类型
     * @param scale 企业规模
     * @param status 状态
     * @return 企业集合
     */
    List<PsyTEnterprise> searchEnterprises(@Param("keyword") String keyword,
                                          @Param("enterpriseType") Integer enterpriseType,
                                          @Param("scale") String scale,
                                          @Param("status") Integer status);

    /**
     * 批量更新企业状态
     * 
     * @param ids 企业ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateEnterpriseStatus(@Param("ids") Long[] ids, @Param("status") Integer status);

    /**
     * 查询企业合同到期提醒
     * 
     * @param days 提前天数
     * @return 企业集合
     */
    List<PsyTEnterprise> selectContractExpiryReminder(@Param("days") Integer days);

    /**
     * 查询企业测评额度预警
     * 
     * @param threshold 预警阈值(百分比)
     * @return 企业集合
     */
    List<PsyTEnterprise> selectAssessmentQuotaWarning(@Param("threshold") Integer threshold);

    /**
     * 查询企业行业分布
     * 
     * @return 分布信息
     */
    List<Map<String, Object>> selectEnterpriseIndustryDistribution();

    /**
     * 查询企业地区分布
     * 
     * @return 分布信息
     */
    List<Map<String, Object>> selectEnterpriseRegionDistribution();

    /**
     * 查询企业服务套餐统计
     * 
     * @return 统计信息
     */
    List<Map<String, Object>> selectServicePackageStats();

    /**
     * 查询企业活跃度统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<Map<String, Object>> selectEnterpriseActivityStats(@Param("days") Integer days);

    /**
     * 查询企业收入统计
     * 
     * @param year 年份
     * @param month 月份
     * @return 统计信息
     */
    Map<String, Object> selectEnterpriseRevenueStats(@Param("year") Integer year, 
                                                     @Param("month") Integer month);

    /**
     * 查询企业续费提醒
     * 
     * @param days 提前天数
     * @return 企业集合
     */
    List<PsyTEnterprise> selectRenewalReminder(@Param("days") Integer days);

    /**
     * 自动更新过期企业状态
     * 
     * @return 更新数量
     */
    int autoUpdateExpiredEnterpriseStatus();
}
