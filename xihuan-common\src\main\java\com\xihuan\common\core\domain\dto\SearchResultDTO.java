package com.xihuan.common.core.domain.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 搜索结果DTO
 * 
 * <AUTHOR>
 */
@Data
public class SearchResultDTO {
    
    /** 搜索关键词 */
    private String keyword;
    
    /** 搜索类型 */
    private String searchType;
    
    /** 总结果数量 */
    private Integer totalCount;
    
    /** 搜索耗时（毫秒） */
    private Long searchTime;
    
    /** 分类结果 */
    private List<CategoryResult> categories;
    
    /** 搜索建议 */
    private List<String> suggestions;
    
    @Data
    public static class CategoryResult {
        /** 分类类型 */
        private String type;
        
        /** 分类名称 */
        private String typeName;
        
        /** 该分类结果数量 */
        private Integer count;
        
        /** 结果列表 */
        private List<SearchItem> items;
    }
    
    @Data
    public static class SearchItem {
        /** 项目ID */
        private Long id;
        
        /** 项目类型 */
        private String type;
        
        /** 标题 */
        private String title;
        
        /** 描述/摘要 */
        private String description;
        
        /** 封面图片 */
        private String coverImage;
        
        /** 相关度分数 */
        private Double relevanceScore;
        
        /** 创建时间 */
        private Date createTime;
        
        /** 查看次数 */
        private Integer viewCount;
        
        /** 评分 */
        private Double rating;
        
        /** 价格 */
        private String price;
        
        /** 标签 */
        private List<String> tags;
        
        /** 高亮字段（用于前端高亮显示匹配的关键词） */
        private String highlightTitle;
        private String highlightDescription;
        
        /** 扩展字段 */
        private Object extraData;
    }
}
