# 新测评系统设计方案

## 📋 概述

基于之前的分析和优化建议，重新设计一个功能完善、架构清晰的心理测评系统。

**重要说明**：抗逆力(Kangnili)相关功能是独立的项目，不属于测评系统范围。本设计方案专门针对心理量表测评功能。

## 🗄️ 数据库表设计

### 1. 量表基础信息表

```sql
CREATE TABLE `psy_scale` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '量表唯一ID',
  `name` VARCHAR(100) NOT NULL COMMENT '量表名称',
  `code` VARCHAR(50) NOT NULL COMMENT '量表编码',
  `description` TEXT COMMENT '量表描述',
  `introduction` TEXT COMMENT '量表介绍',
  `question_count` INT(11) NOT NULL COMMENT '总题数',
  `scoring_type` ENUM('LIKERT','BINARY','COMPOSITE') NOT NULL COMMENT '计分类型',
  `duration` VARCHAR(20) COMMENT '预估时长',
  `norm_mean` DECIMAL(5,2) COMMENT '常模均值',
  `norm_sd` DECIMAL(5,2) COMMENT '常模标准差',
  `applicable_age` VARCHAR(50) COMMENT '适用年龄',
  `image_url` VARCHAR(500) COMMENT '量表封面图片',
  `price` DECIMAL(10,2) DEFAULT 0.00 COMMENT '价格',
  `pay_mode` TINYINT(1) DEFAULT 0 COMMENT '付费模式(0免费 1付费)',
  `pay_phase` TINYINT(1) DEFAULT 0 COMMENT '付费阶段(0测试 1报告)',
  `free_vip_level` INT(11) DEFAULT 0 COMMENT '免费VIP等级',
  `free_report_level` INT(11) DEFAULT 1 COMMENT '免费报告层级',
  `paid_report_level` INT(11) DEFAULT 3 COMMENT '付费报告层级',
  `enterprise_id` BIGINT(20) COMMENT '企业ID',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态(0停用 1启用)',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `search_keywords` TEXT COMMENT '搜索关键词',
  `search_count` INT(11) DEFAULT 0 COMMENT '被搜索次数',
  `view_count` INT(11) DEFAULT 0 COMMENT '查看次数',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_code` (`code`),
  KEY `idx_status_sort` (`status`, `sort`),
  KEY `idx_enterprise` (`enterprise_id`),
  KEY `idx_search` (`search_count`, `view_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量表基础信息表';
```

### 2. 题目表

```sql
CREATE TABLE `psy_question` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '题目唯一ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_scale.id',
  `question_no` INT(11) NOT NULL COMMENT '题号(从1开始)',
  `content` TEXT NOT NULL COMMENT '题目内容',
  `question_type` ENUM('SINGLE','MULTIPLE','TEXT','COMPOSITE') DEFAULT 'SINGLE' COMMENT '题目类型',
  `is_reverse` TINYINT(1) NOT NULL DEFAULT 0 COMMENT '是否反向计分',
  `reverse_value` INT(11) COMMENT '反向计分值',
  `options` JSON COMMENT '选项配置',
  `subscale_ref` VARCHAR(50) COMMENT '分量表关联标识',
  `is_required` TINYINT(1) DEFAULT 1 COMMENT '是否必答',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale_sort` (`scale_id`, `sort`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量表题目表';
```

### 3. 分量表定义表

```sql
CREATE TABLE `psy_subscale` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '分量表ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_scale.id',
  `name` VARCHAR(50) NOT NULL COMMENT '分量表名称',
  `alias` VARCHAR(20) COMMENT '缩写',
  `min_score` INT(11) NOT NULL COMMENT '最小分数',
  `max_score` INT(11) NOT NULL COMMENT '最大分数',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale` (`scale_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='量表分量表定义表';
```

### 4. 分量表题目关系表

```sql
CREATE TABLE `psy_subscale_question_rel` (
  `subscale_id` BIGINT(20) NOT NULL COMMENT '分量表ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '题目ID',
  `weight` DECIMAL(5,2) DEFAULT 1.00 COMMENT '权重',
  PRIMARY KEY (`subscale_id`, `question_id`),
  KEY `idx_question` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='分量表题目关系表';
```

### 5. 计分规则表

```sql
CREATE TABLE `psy_scoring_rule` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '规则ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_scale.id',
  `subscale_id` BIGINT(20) COMMENT '关联psy_subscale.id(为空时适用总分)',
  `rule_type` ENUM('RANGE','CUTOFF') NOT NULL COMMENT '规则类型',
  `min_value` DECIMAL(5,2) COMMENT '最小值',
  `max_value` DECIMAL(5,2) COMMENT '最大值',
  `cutoff_value` DECIMAL(5,2) COMMENT '临界值',
  `label` VARCHAR(50) NOT NULL COMMENT '结果标签',
  `description` TEXT NOT NULL COMMENT '解释说明',
  `suggestion` TEXT COMMENT '建议措施',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale` (`scale_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='计分规则表';
```

### 6. 复合题特殊计分表

```sql
CREATE TABLE `psy_composite_question` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '复合题ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '关联psy_question.id',
  `subitem_no` VARCHAR(20) NOT NULL COMMENT '子项编号',
  `content` VARCHAR(200) NOT NULL COMMENT '子项内容',
  `score_weight` INT(11) NOT NULL DEFAULT 1 COMMENT '计分权重',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_item` (`question_id`,`subitem_no`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='复合题特殊计分表';
```

### 7. 题目选项表

```sql
CREATE TABLE `psy_question_option` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '选项ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '关联psy_question.id',
  `option_text` VARCHAR(200) NOT NULL COMMENT '选项文本',
  `option_value` INT(11) NOT NULL COMMENT '选项分值',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  KEY `idx_question_sort` (`question_id`, `sort`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目选项表';
```

### 8. 功能损害评估表

```sql
CREATE TABLE `psy_function_impairment` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '关联psy_scale.id',
  `item_content` TEXT NOT NULL COMMENT '损害条目内容',
  `positive_score` INT(11) NOT NULL DEFAULT 1 COMMENT '阳性得分值',
  `impairment_level` ENUM('LOW','MEDIUM','HIGH') COMMENT '损害程度分级',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_scale` (`scale_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='功能损害评估条目表';
```

### 8. 测评记录表

```sql
CREATE TABLE `psy_assessment_record` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `scale_id` BIGINT(20) NOT NULL COMMENT '量表ID',
  `user_id` BIGINT(20) COMMENT '用户ID',
  `session_id` VARCHAR(64) NOT NULL COMMENT '会话ID',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `completion_time` DATETIME COMMENT '完成时间',
  `total_score` DECIMAL(8,2) COMMENT '总分',
  `result_level` VARCHAR(50) COMMENT '结果等级',
  `result_description` TEXT COMMENT '结果描述',
  `suggestions` TEXT COMMENT '建议',
  `status` TINYINT(1) DEFAULT 0 COMMENT '状态(0进行中 1已完成 2已放弃)',
  `ip_address` VARCHAR(50) COMMENT 'IP地址',
  `user_agent` VARCHAR(500) COMMENT '用户代理',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_session` (`session_id`),
  KEY `idx_scale_user` (`scale_id`, `user_id`),
  KEY `idx_completion_time` (`completion_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测评记录表';
```

### 9. 答题记录表

```sql
CREATE TABLE `psy_answer_record` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '答案ID',
  `record_id` BIGINT(20) NOT NULL COMMENT '测评记录ID',
  `question_id` BIGINT(20) NOT NULL COMMENT '题目ID',
  `answer_content` TEXT COMMENT '答案内容',
  `answer_score` INT(11) COMMENT '得分',
  `response_time` INT(11) COMMENT '答题耗时(秒)',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_record` (`record_id`),
  KEY `idx_question` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='答题记录表';
```

### 10. 测评订单表

```sql
CREATE TABLE `psy_assessment_order` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` VARCHAR(50) NOT NULL COMMENT '订单编号',
  `scale_id` BIGINT(20) NOT NULL COMMENT '量表ID',
  `user_id` BIGINT(20) NOT NULL COMMENT '用户ID',
  `original_price` DECIMAL(10,2) NOT NULL COMMENT '原价',
  `payment_amount` DECIMAL(10,2) NOT NULL COMMENT '实付金额',
  `payment_method` VARCHAR(20) COMMENT '支付方式',
  `payment_time` DATETIME COMMENT '支付时间',
  `transaction_id` VARCHAR(100) COMMENT '交易流水号',
  `refund_amount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `refund_time` DATETIME COMMENT '退款时间',
  `cancel_time` DATETIME COMMENT '取消时间',
  `coupon_id` BIGINT(20) COMMENT '优惠券ID',
  `coupon_discount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '优惠券折扣',
  `membership_discount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '会员折扣',
  `points_used` INT(11) DEFAULT 0 COMMENT '使用积分',
  `points_discount` DECIMAL(10,2) DEFAULT 0.00 COMMENT '积分抵扣',
  `is_membership_free` TINYINT(1) DEFAULT 0 COMMENT '会员免费',
  `status` TINYINT(1) DEFAULT 0 COMMENT '订单状态(0待付款 1已付款 2已取消 3已退款)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_order_no` (`order_no`),
  KEY `idx_user_scale` (`user_id`, `scale_id`),
  KEY `idx_status_time` (`status`, `create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='测评订单表';
```

## 🏢 企业测评相关表设计

### 11. 企业信息表

```sql
CREATE TABLE `psy_enterprise` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '企业ID',
  `enterprise_code` VARCHAR(50) NOT NULL COMMENT '企业编码',
  `enterprise_name` VARCHAR(200) NOT NULL COMMENT '企业名称',
  `enterprise_type` TINYINT(1) DEFAULT 1 COMMENT '企业类型(1普通企业 2政府机构 3学校 4医院)',
  `industry` VARCHAR(100) COMMENT '所属行业',
  `scale` ENUM('SMALL','MEDIUM','LARGE') DEFAULT 'MEDIUM' COMMENT '企业规模',
  `employee_count` INT(11) DEFAULT 0 COMMENT '员工数量',
  `contact_person` VARCHAR(50) COMMENT '联系人',
  `contact_phone` VARCHAR(20) COMMENT '联系电话',
  `contact_email` VARCHAR(100) COMMENT '联系邮箱',
  `address` VARCHAR(500) COMMENT '企业地址',
  `license_number` VARCHAR(100) COMMENT '营业执照号',
  `contract_start_date` DATE COMMENT '合同开始日期',
  `contract_end_date` DATE COMMENT '合同结束日期',
  `service_package` VARCHAR(50) COMMENT '服务套餐',
  `max_assessment_count` INT(11) DEFAULT 0 COMMENT '最大测评次数',
  `used_assessment_count` INT(11) DEFAULT 0 COMMENT '已使用测评次数',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态(0停用 1启用 2过期)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_enterprise_code` (`enterprise_code`),
  KEY `idx_status_type` (`status`, `enterprise_type`),
  KEY `idx_contract_date` (`contract_end_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业信息表';
```

### 12. 企业部门表

```sql
CREATE TABLE `psy_enterprise_department` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '部门ID',
  `enterprise_id` BIGINT(20) NOT NULL COMMENT '企业ID',
  `parent_id` BIGINT(20) DEFAULT 0 COMMENT '父部门ID',
  `department_code` VARCHAR(50) NOT NULL COMMENT '部门编码',
  `department_name` VARCHAR(100) NOT NULL COMMENT '部门名称',
  `department_level` INT(11) DEFAULT 1 COMMENT '部门层级',
  `manager_id` BIGINT(20) COMMENT '部门负责人ID',
  `employee_count` INT(11) DEFAULT 0 COMMENT '部门人数',
  `sort` INT(11) DEFAULT 0 COMMENT '排序',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_enterprise_dept_code` (`enterprise_id`, `department_code`),
  KEY `idx_parent` (`parent_id`),
  KEY `idx_manager` (`manager_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业部门表';
```

### 13. 企业员工表

```sql
CREATE TABLE `psy_enterprise_employee` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `enterprise_id` BIGINT(20) NOT NULL COMMENT '企业ID',
  `department_id` BIGINT(20) NOT NULL COMMENT '部门ID',
  `user_id` BIGINT(20) COMMENT '关联用户ID（如果员工已注册）',
  `employee_no` VARCHAR(50) NOT NULL COMMENT '员工工号',
  `name` VARCHAR(50) NOT NULL COMMENT '姓名',
  `gender` TINYINT(1) COMMENT '性别(0女 1男)',
  `birth_date` DATE COMMENT '出生日期',
  `phone` VARCHAR(20) COMMENT '手机号',
  `email` VARCHAR(100) COMMENT '邮箱',
  `position` VARCHAR(100) COMMENT '职位',
  `job_level` VARCHAR(50) COMMENT '职级',
  `entry_date` DATE COMMENT '入职日期',
  `education` VARCHAR(50) COMMENT '学历',
  `work_years` INT(11) COMMENT '工作年限',
  `is_manager` TINYINT(1) DEFAULT 0 COMMENT '是否管理者',
  `status` TINYINT(1) DEFAULT 1 COMMENT '状态(0离职 1在职 2休假)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_enterprise_employee_no` (`enterprise_id`, `employee_no`),
  KEY `idx_department` (`department_id`),
  KEY `idx_user` (`user_id`),
  KEY `idx_status_position` (`status`, `position`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业员工表';
```

### 14. 企业测评计划表

```sql
CREATE TABLE `psy_enterprise_assessment_plan` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '计划ID',
  `enterprise_id` BIGINT(20) NOT NULL COMMENT '企业ID',
  `plan_name` VARCHAR(200) NOT NULL COMMENT '计划名称',
  `plan_type` TINYINT(1) NOT NULL COMMENT '计划类型(1招聘测评 2定期筛查 3培训评估 4专项调研)',
  `scale_id` BIGINT(20) NOT NULL COMMENT '量表ID',
  `target_type` TINYINT(1) NOT NULL COMMENT '目标类型(1全员 2指定部门 3指定员工)',
  `target_departments` JSON COMMENT '目标部门ID列表',
  `target_employees` JSON COMMENT '目标员工ID列表',
  `start_time` DATETIME NOT NULL COMMENT '开始时间',
  `end_time` DATETIME NOT NULL COMMENT '结束时间',
  `is_anonymous` TINYINT(1) DEFAULT 0 COMMENT '是否匿名测评',
  `is_mandatory` TINYINT(1) DEFAULT 1 COMMENT '是否强制参与',
  `reminder_enabled` TINYINT(1) DEFAULT 1 COMMENT '是否启用提醒',
  `reminder_frequency` INT(11) DEFAULT 24 COMMENT '提醒频率(小时)',
  `description` TEXT COMMENT '计划描述',
  `completion_rate` DECIMAL(5,2) DEFAULT 0.00 COMMENT '完成率',
  `total_participants` INT(11) DEFAULT 0 COMMENT '总参与人数',
  `completed_participants` INT(11) DEFAULT 0 COMMENT '已完成人数',
  `status` TINYINT(1) DEFAULT 0 COMMENT '状态(0待开始 1进行中 2已结束 3已取消)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_by` VARCHAR(64) COMMENT '创建者',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_by` VARCHAR(64) COMMENT '更新者',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` VARCHAR(500) COMMENT '备注',
  PRIMARY KEY (`id`),
  KEY `idx_enterprise_time` (`enterprise_id`, `start_time`),
  KEY `idx_scale` (`scale_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业测评计划表';
```

### 15. 企业测评参与记录表

```sql
CREATE TABLE `psy_enterprise_assessment_participant` (
  `id` BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '参与记录ID',
  `plan_id` BIGINT(20) NOT NULL COMMENT '计划ID',
  `enterprise_id` BIGINT(20) NOT NULL COMMENT '企业ID',
  `employee_id` BIGINT(20) NOT NULL COMMENT '员工ID',
  `user_id` BIGINT(20) COMMENT '用户ID',
  `record_id` BIGINT(20) COMMENT '测评记录ID',
  `invitation_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '邀请时间',
  `start_time` DATETIME COMMENT '开始时间',
  `completion_time` DATETIME COMMENT '完成时间',
  `total_score` DECIMAL(8,2) COMMENT '总分',
  `result_level` VARCHAR(50) COMMENT '结果等级',
  `participation_status` TINYINT(1) DEFAULT 0 COMMENT '参与状态(0未开始 1进行中 2已完成 3已放弃)',
  `reminder_count` INT(11) DEFAULT 0 COMMENT '提醒次数',
  `last_reminder_time` DATETIME COMMENT '最后提醒时间',
  `completion_source` TINYINT(1) DEFAULT 1 COMMENT '完成方式(1主动完成 2提醒后完成)',
  `del_flag` CHAR(1) DEFAULT '0' COMMENT '删除标志',
  `create_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uniq_plan_employee` (`plan_id`, `employee_id`),
  KEY `idx_enterprise_plan` (`enterprise_id`, `plan_id`),
  KEY `idx_employee` (`employee_id`),
  KEY `idx_status_time` (`participation_status`, `completion_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业测评参与记录表';
```

## 🏢 企业测评业务逻辑

### 1. 企业测评流程

#### A. 测评计划创建流程

```
1. 企业管理员登录系统
2. 选择量表类型和测评目标
3. 设置测评参数（时间、匿名性、强制性等）
4. 选择参与人员（全员/部门/个人）
5. 系统自动生成邀请记录
6. 发送测评邀请通知
7. 启动测评计划
```

#### B. 员工测评参与流程

```
1. 员工收到测评邀请（短信/邮件/系统通知）
2. 点击链接进入测评页面
3. 身份验证（员工工号+手机号）
4. 阅读测评说明并开始答题
5. 提交测评结果
6. 查看个人报告（如果允许）
7. 系统更新参与状态
```

#### C. 数据统计分析流程

```
1. 系统定时统计测评数据
2. 按部门/岗位/时间维度汇总
3. 生成统计报表和图表
4. 识别高风险人群
5. 生成企业整体报告
6. 推送给企业管理员
```

### 2. 企业测评权限控制

#### A. 权限级别定义

```java
public enum EnterprisePermission {
    // 企业管理权限
    ENTERPRISE_ADMIN("enterprise:admin", "企业管理员"),
    ENTERPRISE_VIEW("enterprise:view", "企业查看"),

    // 部门管理权限
    DEPARTMENT_MANAGE("department:manage", "部门管理"),
    DEPARTMENT_VIEW("department:view", "部门查看"),

    // 员工管理权限
    EMPLOYEE_MANAGE("employee:manage", "员工管理"),
    EMPLOYEE_VIEW("employee:view", "员工查看"),

    // 测评计划权限
    PLAN_CREATE("plan:create", "创建测评计划"),
    PLAN_MANAGE("plan:manage", "管理测评计划"),
    PLAN_VIEW("plan:view", "查看测评计划"),

    // 数据统计权限
    STATISTICS_ALL("statistics:all", "查看所有统计"),
    STATISTICS_DEPARTMENT("statistics:department", "查看部门统计"),
    STATISTICS_EXPORT("statistics:export", "导出统计数据"),

    // 个人数据权限
    PERSONAL_DATA_VIEW("personal:view", "查看个人数据"),
    PERSONAL_DATA_EXPORT("personal:export", "导出个人数据");
}
```

#### B. 数据权限控制

```java
@Service
public class EnterpriseDataPermissionService {

    /**
     * 检查用户是否有权限访问指定部门数据
     */
    public boolean hasAccessToDepartment(Long userId, Long enterpriseId, Long departmentId) {
        // 1. 检查用户是否属于该企业
        // 2. 检查用户角色权限范围
        // 3. 检查部门权限范围
        return true;
    }

    /**
     * 获取用户可访问的部门列表
     */
    public List<Long> getAccessibleDepartments(Long userId, Long enterpriseId) {
        // 根据用户角色和权限范围返回可访问的部门ID列表
        return new ArrayList<>();
    }

    /**
     * 检查是否可以查看员工个人数据
     */
    public boolean canViewPersonalData(Long userId, Long enterpriseId, Long employeeId) {
        // 检查是否有个人数据查看权限
        return true;
    }
}
```

### 3. 企业测评数据隐私保护

#### A. 数据脱敏策略

```java
@Component
public class EnterpriseDataMaskingService {

    /**
     * 个人测评结果脱敏
     */
    public AssessmentResult maskPersonalResult(AssessmentResult result, String maskLevel) {
        switch (maskLevel) {
            case "ANONYMOUS":
                // 完全匿名：隐藏姓名、工号等个人信息
                result.setEmployeeName("匿名用户");
                result.setEmployeeNo("****");
                break;
            case "DEPARTMENT_ONLY":
                // 部门级：只显示部门信息，隐藏个人信息
                result.setEmployeeName(result.getDepartmentName() + "员工");
                break;
            case "STATISTICAL_ONLY":
                // 仅统计：只保留统计相关字段
                result = convertToStatisticalData(result);
                break;
        }
        return result;
    }

    /**
     * 批量数据脱敏
     */
    public List<AssessmentResult> maskBatchResults(List<AssessmentResult> results,
                                                  String maskLevel) {
        return results.stream()
                .map(result -> maskPersonalResult(result, maskLevel))
                .collect(Collectors.toList());
    }
}
```

## 🔄 下一步计划

1. **创建实体类**：根据表结构创建对应的Java实体类
2. **创建Mapper接口**：实现数据访问层
3. **创建Service层**：实现业务逻辑
4. **创建Controller层**：实现API接口
5. **前端界面开发**：管理后台和小程序界面
6. **测试与优化**：功能测试和性能优化

## 📝 重要说明

### 与现有系统的区别

1. **抗逆力系统**：独立的功能模块，保持现有实现
2. **匹配问题系统**：咨询师匹配相关，保持现有实现
3. **测评系统**：全新设计的心理量表测评功能

### 实施优先级

1. **第一阶段**：创建核心量表和题目管理功能
2. **第二阶段**：实现测评流程和计分系统
3. **第三阶段**：添加订单支付和商业化功能
4. **第四阶段**：扩展企业版功能

### 企业测评实施建议

#### A. 分阶段实施计划

##### 第一阶段：基础功能开发（4-6周）

```
Week 1-2: 数据库设计与创建
- 创建企业相关表结构
- 建立基础数据和索引
- 数据迁移脚本准备

Week 3-4: 核心功能开发
- 企业信息管理
- 部门员工管理
- 基础权限控制

Week 5-6: 测评计划功能
- 测评计划创建
- 参与者管理
- 基础统计功能
```

##### 第二阶段：高级功能开发（4-6周）

```
Week 7-8: 数据分析功能
- 统计报表生成
- 风险分析算法
- 数据可视化

Week 9-10: 通知系统
- 消息推送功能
- 提醒机制
- 通知模板管理

Week 11-12: 权限与安全
- 细粒度权限控制
- 数据脱敏处理
- 访问日志记录
```

## 📝 特性说明

### 1. 专业性

- 支持多种心理量表类型
- 完整的计分规则系统
- 专业的报告生成

### 2. 商业化

- 完整的订单支付系统
- 会员权益管理
- 优惠券和积分系统

### 3. 扩展性

- 支持企业版功能
- 灵活的权限控制
- 可配置的计分规则

### 4. 用户体验

- 简洁的答题界面
- 详细的结果报告
- 个性化建议推荐

## 📊 企业测评统计分析

### 1. 企业级统计报表

```sql
-- 企业整体测评概览
SELECT
    e.enterprise_name,
    COUNT(DISTINCT p.id) as total_plans,
    COUNT(DISTINCT pt.employee_id) as total_participants,
    COUNT(DISTINCT CASE WHEN pt.participation_status = 2 THEN pt.employee_id END) as completed_participants,
    ROUND(COUNT(DISTINCT CASE WHEN pt.participation_status = 2 THEN pt.employee_id END) * 100.0 /
          COUNT(DISTINCT pt.employee_id), 2) as completion_rate,
    AVG(pt.total_score) as avg_score,
    COUNT(DISTINCT CASE WHEN pt.result_level IN ('高风险', '中高风险') THEN pt.employee_id END) as risk_count
FROM psy_enterprise e
LEFT JOIN psy_enterprise_assessment_plan p ON e.id = p.enterprise_id
LEFT JOIN psy_enterprise_assessment_participant pt ON p.id = pt.plan_id
WHERE e.status = 1 AND p.status IN (1, 2)
GROUP BY e.id, e.enterprise_name;

-- 部门对比分析
SELECT
    d.department_name,
    COUNT(pt.employee_id) as participant_count,
    COUNT(CASE WHEN pt.participation_status = 2 THEN 1 END) as completed_count,
    ROUND(AVG(pt.total_score), 2) as avg_score,
    COUNT(CASE WHEN pt.result_level IN ('高风险', '中高风险') THEN 1 END) as risk_count,
    ROUND(COUNT(CASE WHEN pt.result_level IN ('高风险', '中高风险') THEN 1 END) * 100.0 /
          COUNT(CASE WHEN pt.participation_status = 2 THEN 1 END), 2) as risk_rate
FROM psy_enterprise_department d
LEFT JOIN psy_enterprise_employee emp ON d.id = emp.department_id
LEFT JOIN psy_enterprise_assessment_participant pt ON emp.id = pt.employee_id
WHERE d.enterprise_id = #{enterpriseId} AND pt.participation_status = 2
GROUP BY d.id, d.department_name
ORDER BY avg_score DESC;
```

### 2. 风险预警分析

```java
@Service
public class EnterpriseRiskAnalysisService {

    /**
     * 生成企业风险预警报告
     */
    public EnterpriseRiskReport generateRiskReport(Long enterpriseId) {
        EnterpriseRiskReport report = new EnterpriseRiskReport();

        // 1. 整体风险等级分布
        Map<String, Integer> riskDistribution = getRiskDistribution(enterpriseId);
        report.setRiskDistribution(riskDistribution);

        // 2. 高风险部门识别
        List<DepartmentRiskInfo> highRiskDepartments = getHighRiskDepartments(enterpriseId);
        report.setHighRiskDepartments(highRiskDepartments);

        // 3. 风险趋势分析
        List<RiskTrendData> riskTrend = getRiskTrend(enterpriseId);
        report.setRiskTrend(riskTrend);

        // 4. 预警建议
        List<String> recommendations = generateRecommendations(report);
        report.setRecommendations(recommendations);

        return report;
    }

    /**
     * 识别需要关注的员工群体
     */
    public List<EmployeeRiskProfile> identifyRiskEmployees(Long enterpriseId, String riskLevel) {
        // 根据测评结果识别高风险员工
        // 考虑隐私保护，只返回统计信息
        return employeeRiskMapper.selectRiskEmployees(enterpriseId, riskLevel);
    }
}
```

这个新的测评系统设计方案提供了完整的功能架构，可以满足个人用户和企业用户的不同需求。
