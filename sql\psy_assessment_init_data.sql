-- 心理测评系统初始化数据

-- 1. 插入测评分类数据（假设已有分类表）
INSERT INTO `psy_category` (`category_id`, `parent_id`, `category_name`, `order_num`, `status`, `create_by`, `create_time`) VALUES
(100, 0, '心理测评', 4, '0', 'admin', NOW()),
(101, 100, '情绪测评', 1, '0', 'admin', NOW()),
(102, 100, '人格测评', 2, '0', 'admin', NOW()),
(103, 100, '认知测评', 3, '0', 'admin', NOW()),
(104, 100, '行为测评', 4, '0', 'admin', NOW()),
(105, 100, '社交测评', 5, '0', 'admin', NOW());

-- 2. 插入测评量表数据
INSERT INTO `psy_t_scale` (`id`, `scale_name`, `scale_code`, `description`, `instruction`, `category_id`, `author`, `version`, `question_count`, `time_limit`, `difficulty_level`, `price`, `is_free`, `cover_image`, `tags`, `status`, `search_keywords`) VALUES
(1, '焦虑自评量表', 'SAS', '焦虑自评量表(Self-Rating Anxiety Scale, SAS)是由Zung于1971年编制的，用于评定焦虑状态的轻重程度。', '请根据您最近一周的实际感受，选择最符合您情况的答案。每题只能选择一个答案，请不要遗漏。', 101, 'Zung', '1.0', 20, 15, 1, 0.00, 1, '/images/scales/sas.jpg', '["焦虑", "自评", "情绪"]', 1, '焦虑,自评,情绪,心理测试'),
(2, '抑郁自评量表', 'SDS', '抑郁自评量表(Self-Rating Depression Scale, SDS)是由Zung于1965年编制的，用于评定抑郁状态的轻重程度。', '请根据您最近一周的实际感受，选择最符合您情况的答案。每题只能选择一个答案，请不要遗漏。', 101, 'Zung', '1.0', 20, 15, 1, 0.00, 1, '/images/scales/sds.jpg', '["抑郁", "自评", "情绪"]', 1, '抑郁,自评,情绪,心理测试'),
(3, '大五人格量表', 'BFI', '大五人格量表(Big Five Inventory, BFI)是基于大五人格理论编制的人格测评工具，测量五个主要人格维度。', '请根据您对自己的了解，选择最符合您情况的答案。没有对错之分，请诚实作答。', 102, 'John & Srivastava', '1.0', 44, 20, 2, 9.90, 0, '/images/scales/bfi.jpg', '["人格", "大五", "性格"]', 1, '人格,性格,大五,心理测试'),
(4, '职业兴趣测评', 'HOLLAND', '霍兰德职业兴趣测评基于霍兰德职业兴趣理论，帮助您了解自己的职业兴趣类型。', '请根据您的真实想法和兴趣，选择最符合您情况的答案。', 104, 'Holland', '1.0', 60, 25, 2, 19.90, 0, '/images/scales/holland.jpg', '["职业", "兴趣", "霍兰德"]', 1, '职业,兴趣,霍兰德,职业规划'),
(5, '社交焦虑量表', 'LSAS', '社交焦虑量表(Liebowitz Social Anxiety Scale, LSAS)用于评估社交焦虑的严重程度。', '请回想过去一周内，在以下情况中您的焦虑程度和回避行为。', 105, 'Liebowitz', '1.0', 24, 20, 2, 12.90, 0, '/images/scales/lsas.jpg', '["社交", "焦虑", "社交恐惧"]', 1, '社交焦虑,社交恐惧,人际关系');

-- 3. 插入焦虑自评量表题目
INSERT INTO `psy_t_question` (`id`, `scale_id`, `question_no`, `question_text`, `question_type`, `is_required`, `score_type`, `dimension`, `order_num`) VALUES
(1, 1, 1, '我觉得比平常容易紧张和着急', 1, 1, 1, '焦虑', 1),
(2, 1, 2, '我无缘无故地感到害怕', 1, 1, 1, '焦虑', 2),
(3, 1, 3, '我容易心里烦乱或感到惊恐', 1, 1, 1, '焦虑', 3),
(4, 1, 4, '我觉得我可能将要发疯', 1, 1, 1, '焦虑', 4),
(5, 1, 5, '我觉得一切都很好，也不会发生什么不幸', 1, 1, 1, '焦虑', 5),
(6, 1, 6, '我手脚发抖打颤', 1, 1, 1, '躯体', 6),
(7, 1, 7, '我因为头痛、颈痛和背痛而苦恼', 1, 1, 1, '躯体', 7),
(8, 1, 8, '我感觉容易衰弱和疲乏', 1, 1, 1, '躯体', 8),
(9, 1, 9, '我觉得心平气和，并且容易安静坐着', 1, 1, 1, '躯体', 9),
(10, 1, 10, '我觉得心跳得很快', 1, 1, 1, '躯体', 10),
(11, 1, 11, '我因为一阵阵头晕而苦恼', 1, 1, 1, '躯体', 11),
(12, 1, 12, '我有晕倒发作，或觉得要晕倒似的', 1, 1, 1, '躯体', 12),
(13, 1, 13, '我吸气呼气都感到很容易', 1, 1, 1, '躯体', 13),
(14, 1, 14, '我的手脚麻木和刺痛', 1, 1, 1, '躯体', 14),
(15, 1, 15, '我因为胃痛和消化不良而苦恼', 1, 1, 1, '躯体', 15),
(16, 1, 16, '我常常要小便', 1, 1, 1, '躯体', 16),
(17, 1, 17, '我的手脚常常是干燥温暖的', 1, 1, 1, '躯体', 17),
(18, 1, 18, '我脸红发热', 1, 1, 1, '躯体', 18),
(19, 1, 19, '我容易入睡并且一夜睡得很好', 1, 1, 1, '睡眠', 19),
(20, 1, 20, '我做恶梦', 1, 1, 1, '睡眠', 20);

-- 4. 插入焦虑自评量表选项
INSERT INTO `psy_t_option` (`id`, `question_id`, `option_text`, `option_value`, `score`, `order_num`) VALUES
-- 题目1的选项
(1, 1, '没有或很少时间', 'A', 1, 1),
(2, 1, '小部分时间', 'B', 2, 2),
(3, 1, '相当多时间', 'C', 3, 3),
(4, 1, '绝大部分或全部时间', 'D', 4, 4),
-- 题目2的选项
(5, 2, '没有或很少时间', 'A', 1, 1),
(6, 2, '小部分时间', 'B', 2, 2),
(7, 2, '相当多时间', 'C', 3, 3),
(8, 2, '绝大部分或全部时间', 'D', 4, 4),
-- 题目3的选项
(9, 3, '没有或很少时间', 'A', 1, 1),
(10, 3, '小部分时间', 'B', 2, 2),
(11, 3, '相当多时间', 'C', 3, 3),
(12, 3, '绝大部分或全部时间', 'D', 4, 4),
-- 题目4的选项
(13, 4, '没有或很少时间', 'A', 1, 1),
(14, 4, '小部分时间', 'B', 2, 2),
(15, 4, '相当多时间', 'C', 3, 3),
(16, 4, '绝大部分或全部时间', 'D', 4, 4),
-- 题目5的选项（反向计分）
(17, 5, '没有或很少时间', 'A', 4, 1),
(18, 5, '小部分时间', 'B', 3, 2),
(19, 5, '相当多时间', 'C', 2, 3),
(20, 5, '绝大部分或全部时间', 'D', 1, 4);

-- 5. 插入测评结果解释
INSERT INTO `psy_t_interpretation` (`id`, `scale_id`, `dimension`, `min_score`, `max_score`, `level_name`, `level_description`, `suggestions`, `color`, `order_num`) VALUES
-- 焦虑自评量表总分解释
(1, 1, NULL, 20, 44, '正常', '您的焦虑水平在正常范围内，能够较好地应对日常生活中的压力和挑战。', '继续保持良好的心理状态，适当进行放松活动。', '#28a745', 1),
(2, 1, NULL, 45, 59, '轻度焦虑', '您存在轻度的焦虑症状，可能在某些情况下感到紧张不安。', '建议学习一些放松技巧，如深呼吸、冥想等，必要时可寻求专业帮助。', '#ffc107', 2),
(3, 1, NULL, 60, 69, '中度焦虑', '您存在中度的焦虑症状，焦虑情绪对日常生活产生了一定影响。', '建议及时寻求专业心理咨询师的帮助，学习有效的应对策略。', '#fd7e14', 3),
(4, 1, NULL, 70, 80, '重度焦虑', '您存在重度的焦虑症状，严重影响了日常生活和工作。', '强烈建议立即寻求专业医生或心理咨询师的帮助，可能需要药物治疗配合心理治疗。', '#dc3545', 4),

-- 焦虑自评量表维度解释
(5, 1, '焦虑', 5, 11, '正常', '心理焦虑水平正常', '保持良好心态', '#28a745', 1),
(6, 1, '焦虑', 12, 15, '轻度', '存在轻度心理焦虑', '学习放松技巧', '#ffc107', 2),
(7, 1, '焦虑', 16, 20, '重度', '存在重度心理焦虑', '寻求专业帮助', '#dc3545', 3),

(8, 1, '躯体', 10, 22, '正常', '躯体焦虑症状正常', '保持健康生活方式', '#28a745', 1),
(9, 1, '躯体', 23, 30, '轻度', '存在轻度躯体焦虑症状', '注意身体放松', '#ffc107', 2),
(10, 1, '躯体', 31, 40, '重度', '存在重度躯体焦虑症状', '及时就医检查', '#dc3545', 3);

-- 6. 插入量表分类关联
INSERT INTO `psy_t_scale_category_rel` (`scale_id`, `category_id`) VALUES
(1, 101), -- 焦虑自评量表 -> 情绪测评
(2, 101), -- 抑郁自评量表 -> 情绪测评
(3, 102), -- 大五人格量表 -> 人格测评
(4, 104), -- 职业兴趣测评 -> 行为测评
(5, 105); -- 社交焦虑量表 -> 社交测评

-- 7. 插入示例测评记录（用于演示）
INSERT INTO `psy_t_record` (`id`, `user_id`, `scale_id`, `session_id`, `start_time`, `end_time`, `duration`, `total_score`, `max_score`, `percentage`, `status`, `result_level`, `result_description`, `suggestions`, `dimension_scores`, `is_anonymous`) VALUES
(1, 1, 1, 'session_001_20241201_001', '2024-12-01 10:00:00', '2024-12-01 10:15:00', 900, 35, 80, 43.75, 1, '正常', '您的焦虑水平在正常范围内', '继续保持良好的心理状态', '{"焦虑": 12, "躯体": 15, "睡眠": 8}', 0),
(2, 2, 1, 'session_002_20241201_002', '2024-12-01 14:30:00', '2024-12-01 14:45:00', 900, 52, 80, 65.00, 1, '轻度焦虑', '您存在轻度的焦虑症状', '建议学习一些放松技巧', '{"焦虑": 18, "躯体": 22, "睡眠": 12}', 0);

-- 8. 插入示例测评答案
INSERT INTO `psy_t_answer` (`record_id`, `question_id`, `option_id`, `score`, `answer_time`, `time_spent`) VALUES
-- 记录1的答案
(1, 1, 2, 2, '2024-12-01 10:01:00', 30),
(1, 2, 1, 1, '2024-12-01 10:01:30', 25),
(1, 3, 2, 2, '2024-12-01 10:02:00', 35),
(1, 4, 1, 1, '2024-12-01 10:02:30', 20),
(1, 5, 18, 3, '2024-12-01 10:03:00', 40),
-- 记录2的答案
(2, 1, 3, 3, '2024-12-01 14:31:00', 45),
(2, 2, 2, 2, '2024-12-01 14:31:45', 30),
(2, 3, 3, 3, '2024-12-01 14:32:15', 50),
(2, 4, 2, 2, '2024-12-01 14:33:05', 35),
(2, 5, 19, 2, '2024-12-01 14:33:40', 25);

-- 9. 插入示例评价
INSERT INTO `psy_t_review` (`id`, `user_id`, `scale_id`, `record_id`, `rating`, `content`, `is_anonymous`, `status`, `audit_by`, `audit_time`) VALUES
(1, 1, 1, 1, 5, '这个测评很准确，帮助我了解了自己的焦虑状况，非常有用！', 0, 1, 'admin', '2024-12-01 16:00:00'),
(2, 2, 1, 2, 4, '测评题目设计合理，结果分析详细，对我很有帮助。', 1, 1, 'admin', '2024-12-01 17:00:00');

-- 10. 更新量表统计数据
UPDATE `psy_t_scale` SET 
    `view_count` = 150,
    `test_count` = 89,
    `rating_avg` = 4.5,
    `rating_count` = 2
WHERE `id` = 1;

UPDATE `psy_t_scale` SET 
    `view_count` = 120,
    `test_count` = 65,
    `rating_avg` = 0.0,
    `rating_count` = 0
WHERE `id` = 2;

UPDATE `psy_t_scale` SET 
    `view_count` = 200,
    `test_count` = 45,
    `rating_avg` = 0.0,
    `rating_count` = 0
WHERE `id` = 3;
