package com.xihuan.common.core.domain.entity;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 服务项目表 psy_service_item
 */
@Data
public class PsyServiceItem extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 项目ID */
    private Long itemId;

    /** 产品ID */
    private Long productId;

    /** 项目名称 */
    private String itemName;

    /** 项目分类 */
    private String category;

    /** 门店售价 */
    private BigDecimal price;

    /** 数量 */
    private String quantity;

    /** 排序字段 */
    private Integer sortOrder;

    /** 删除标志 */
    private String delFlag;

    /** 包含的服务内容列表 */
    private List<PsyServiceContent> contents;

}