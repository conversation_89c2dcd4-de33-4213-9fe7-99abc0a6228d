<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeTemplateItemMapper">
    
    <resultMap type="PsyTimeTemplateItem" id="PsyTimeTemplateItemResult">
        <result property="id"           column="id"           />
        <result property="templateId"   column="template_id"  />
        <result property="dayOfWeek"    column="day_of_week"  />
        <result property="startTime"    column="start_time"   />
        <result property="endTime"      column="end_time"     />
        <result property="centerId"     column="center_id"    />
        <result property="delFlag"      column="del_flag"     />
        <result property="createBy"     column="create_by"    />
        <result property="createTime"   column="create_time"  />
        <result property="updateBy"     column="update_by"    />
        <result property="updateTime"   column="update_time"  />
        <result property="remark"       column="remark"       />
    </resultMap>

    <sql id="selectPsyTimeTemplateItemVo">
        select id, template_id, day_of_week, start_time, end_time, center_id, 
               del_flag, create_by, create_time, update_by, update_time, remark
        from psy_time_template_item
    </sql>

    <select id="selectTemplateItemList" parameterType="PsyTimeTemplateItem" resultMap="PsyTimeTemplateItemResult">
        <include refid="selectPsyTimeTemplateItemVo"/>
        <where>  
            del_flag = 0
            <if test="templateId != null"> and template_id = #{templateId}</if>
            <if test="dayOfWeek != null"> and day_of_week = #{dayOfWeek}</if>
            <if test="centerId != null"> and center_id = #{centerId}</if>
        </where>
        order by day_of_week, start_time
    </select>
    
    <select id="selectTemplateItemById" parameterType="Long" resultMap="PsyTimeTemplateItemResult">
        <include refid="selectPsyTimeTemplateItemVo"/>
        where id = #{id}
    </select>
    
    <select id="selectItemsByTemplateId" parameterType="Long" resultMap="PsyTimeTemplateItemResult">
        <include refid="selectPsyTimeTemplateItemVo"/>
        where template_id = #{templateId} and del_flag = 0
        order by day_of_week, start_time
    </select>
    
    <select id="selectItemsByTemplateAndDay" resultMap="PsyTimeTemplateItemResult">
        <include refid="selectPsyTimeTemplateItemVo"/>
        where template_id = #{templateId} and day_of_week = #{dayOfWeek} and del_flag = 0
        order by start_time
    </select>
        
    <insert id="insertTemplateItem" parameterType="PsyTimeTemplateItem" useGeneratedKeys="true" keyProperty="id">
        insert into psy_time_template_item
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateId != null">template_id,</if>
            <if test="dayOfWeek != null">day_of_week,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="centerId != null">center_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateId != null">#{templateId},</if>
            <if test="dayOfWeek != null">#{dayOfWeek},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="centerId != null">#{centerId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <insert id="batchInsertTemplateItems">
        insert into psy_time_template_item (template_id, day_of_week, start_time, end_time, center_id, del_flag, create_time)
        values
        <foreach collection="templateItems" item="item" separator=",">
            (#{item.templateId}, #{item.dayOfWeek}, #{item.startTime}, #{item.endTime}, 
             #{item.centerId}, #{item.delFlag}, #{item.createTime})
        </foreach>
    </insert>

    <update id="updateTemplateItem" parameterType="PsyTimeTemplateItem">
        update psy_time_template_item
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="dayOfWeek != null">day_of_week = #{dayOfWeek},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="centerId != null">center_id = #{centerId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTemplateItemById" parameterType="Long">
        update psy_time_template_item set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteItemsByTemplateId" parameterType="Long">
        update psy_time_template_item set del_flag = 1 where template_id = #{templateId}
    </delete>

    <delete id="deleteTemplateItemByIds" parameterType="String">
        update psy_time_template_item set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="checkTimeConflict" resultType="int">
        select count(1) from psy_time_template_item 
        where template_id = #{templateId} and day_of_week = #{dayOfWeek} and del_flag = 0
        and ((start_time &gt;= #{startTime} and start_time &lt; #{endTime})
             or (end_time &gt; #{startTime} and end_time &lt;= #{endTime})
             or (start_time &lt;= #{startTime} and end_time &gt;= #{endTime}))
        <if test="excludeId != null">and id != #{excludeId}</if>
    </select>
</mapper>
