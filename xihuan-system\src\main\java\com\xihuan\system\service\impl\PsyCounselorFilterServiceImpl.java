package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.system.service.IPsyCounselorFilterService;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.IPsyTimeSlotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 咨询师筛选Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyCounselorFilterServiceImpl implements IPsyCounselorFilterService {
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;
    
    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;

    /**
     * 根据时间段筛选可用的咨询师
     */
    @Override
    public List<Map<String, Object>> filterAvailableCounselors(
            LocalDate date, LocalTime startTime, LocalTime endTime, Long centerId) {
        
        // 查询指定时间段的可用时间槽
        List<PsyTimeSlot> availableSlots = timeSlotService.selectAvailableSlotsByDate(date, centerId, null);
        
        if (CollectionUtils.isEmpty(availableSlots)) {
            return new ArrayList<>();
        }
        
        // 筛选符合时间范围的时间槽
        List<PsyTimeSlot> matchingSlots = availableSlots.stream()
            .filter(slot -> !slot.getStartTime().isBefore(startTime) && !slot.getEndTime().isAfter(endTime))
            .collect(Collectors.toList());
        
        // 按咨询师分组
        Map<Long, List<PsyTimeSlot>> slotsByCounselor = matchingSlots.stream()
            .collect(Collectors.groupingBy(PsyTimeSlot::getCounselorId));
        
        List<Map<String, Object>> counselors = new ArrayList<>();
        
        for (Map.Entry<Long, List<PsyTimeSlot>> entry : slotsByCounselor.entrySet()) {
            Long counselorId = entry.getKey();
            List<PsyTimeSlot> counselorSlots = entry.getValue();
            
            // 检查是否有连续的时间槽覆盖整个时间段
            if (hasCompleteTimeRange(counselorSlots, startTime, endTime)) {
                Map<String, Object> counselorInfo = buildCounselorInfo(counselorId, counselorSlots, date, startTime, endTime);
                counselors.add(counselorInfo);
            }
        }
        
        // 按推荐度排序
        counselors.sort((a, b) -> {
            Double scoreA = (Double) a.getOrDefault("recommendationScore", 0.0);
            Double scoreB = (Double) b.getOrDefault("recommendationScore", 0.0);
            return scoreB.compareTo(scoreA);
        });
        
        return counselors;
    }

    /**
     * 根据时间段筛选可用的咨询师（支持连续时间段）
     */
    @Override
    public List<Map<String, Object>> filterAvailableCounselorsByDuration(
            LocalDate date, LocalTime startTime, Integer duration, Long centerId) {
        
        LocalTime endTime = startTime.plusMinutes(duration);
        return filterAvailableCounselors(date, startTime, endTime, centerId);
    }

    /**
     * 根据系统时间槽ID筛选可用的咨询师
     */
    @Override
    public List<Map<String, Object>> filterCounselorsBySystemSlot(Long systemSlotId) {
        PsySystemTimeSlot systemSlot = systemTimeSlotService.selectSystemTimeSlotById(systemSlotId);
        
        if (systemSlot == null) {
            return new ArrayList<>();
        }
        
        LocalDate date = LocalDate.parse(systemSlot.getDateKey());
        return filterAvailableCounselors(date, systemSlot.getStartTime(), systemSlot.getEndTime(), systemSlot.getCenterId());
    }

    /**
     * 根据多个时间段筛选可用的咨询师（取交集）
     */
    @Override
    public List<Map<String, Object>> filterCounselorsByMultipleTimeSlots(
            LocalDate date, List<Map<String, LocalTime>> timeSlots, Long centerId) {
        
        if (CollectionUtils.isEmpty(timeSlots)) {
            return new ArrayList<>();
        }
        
        List<List<Map<String, Object>>> counselorLists = new ArrayList<>();
        
        // 为每个时间段获取可用咨询师
        for (Map<String, LocalTime> timeSlot : timeSlots) {
            LocalTime startTime = timeSlot.get("startTime");
            LocalTime endTime = timeSlot.get("endTime");
            
            List<Map<String, Object>> counselors = filterAvailableCounselors(date, startTime, endTime, centerId);
            counselorLists.add(counselors);
        }
        
        // 取交集
        if (counselorLists.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<Map<String, Object>> result = counselorLists.get(0);
        for (int i = 1; i < counselorLists.size(); i++) {
            result = getIntersection(result, counselorLists.get(i));
        }
        
        return result;
    }

    /**
     * 获取咨询师在指定时间段的详细可用性信息
     */
    @Override
    public Map<String, Object> getCounselorAvailabilityDetail(
            Long counselorId, LocalDate date, LocalTime startTime, LocalTime endTime) {
        
        Map<String, Object> detail = new HashMap<>();
        
        // 查询咨询师在该时间段的所有时间槽
        List<PsyTimeSlot> counselorSlots = timeSlotService.selectSlotsByCounselorAndDateRange(
            counselorId, date, date
        ).stream()
        .filter(slot -> !slot.getStartTime().isBefore(startTime) && !slot.getEndTime().isAfter(endTime))
        .collect(Collectors.toList());
        
        detail.put("counselorId", counselorId);
        detail.put("date", date.toString());
        detail.put("startTime", startTime);
        detail.put("endTime", endTime);
        detail.put("totalSlots", counselorSlots.size());
        
        long availableSlots = counselorSlots.stream().filter(slot -> slot.getStatus() == 0).count();
        detail.put("availableSlots", availableSlots);
        detail.put("isFullyAvailable", availableSlots == counselorSlots.size() && counselorSlots.size() > 0);
        
        // 计算可用时间段
        List<Map<String, Object>> availableTimeRanges = calculateAvailableTimeRanges(counselorSlots);
        detail.put("availableTimeRanges", availableTimeRanges);
        
        return detail;
    }

    /**
     * 获取咨询师的推荐度评分
     */
    @Override
    public Map<String, Object> getCounselorRecommendationScore(
            Long counselorId, LocalDate date, LocalTime startTime) {
        
        Map<String, Object> score = new HashMap<>();
        score.put("counselorId", counselorId);
        
        // 基础评分（这里可以根据实际业务逻辑计算）
        double baseScore = 5.0; // 基础分
        
        // 时间偏好评分（例如：上午时段可能更受欢迎）
        double timePreferenceScore = calculateTimePreferenceScore(startTime);
        
        // 可用性评分（连续可用时间越长评分越高）
        double availabilityScore = calculateAvailabilityScore(counselorId, date, startTime);
        
        // 综合评分
        double totalScore = (baseScore + timePreferenceScore + availabilityScore) / 3;
        
        score.put("baseScore", baseScore);
        score.put("timePreferenceScore", timePreferenceScore);
        score.put("availabilityScore", availabilityScore);
        score.put("totalScore", Math.round(totalScore * 10.0) / 10.0);
        
        return score;
    }

    /**
     * 根据用户偏好筛选咨询师
     */
    @Override
    public List<Map<String, Object>> filterCounselorsByPreferences(
            LocalDate date, LocalTime startTime, LocalTime endTime, Long centerId,
            Map<String, Object> preferences) {
        
        // 先获取基础的可用咨询师列表
        List<Map<String, Object>> counselors = filterAvailableCounselors(date, startTime, endTime, centerId);
        
        if (CollectionUtils.isEmpty(counselors) || preferences == null || preferences.isEmpty()) {
            return counselors;
        }
        
        // 根据偏好进行筛选
        return counselors.stream()
            .filter(counselor -> matchesPreferences(counselor, preferences))
            .collect(Collectors.toList());
    }

    /**
     * 获取时间段内咨询师的统计信息
     */
    @Override
    public Map<String, Object> getCounselorStatistics(
            LocalDate date, LocalTime startTime, LocalTime endTime, Long centerId) {
        
        List<Map<String, Object>> counselors = filterAvailableCounselors(date, startTime, endTime, centerId);
        
        Map<String, Object> statistics = new HashMap<>();
        statistics.put("totalAvailableCounselors", counselors.size());
        statistics.put("date", date.toString());
        statistics.put("timeRange", startTime + "-" + endTime);
        
        if (!counselors.isEmpty()) {
            // 计算平均推荐度
            double avgScore = counselors.stream()
                .mapToDouble(c -> (Double) c.getOrDefault("recommendationScore", 0.0))
                .average()
                .orElse(0.0);
            statistics.put("averageRecommendationScore", Math.round(avgScore * 10.0) / 10.0);
            
            // 统计专业领域分布（如果有的话）
            Map<String, Long> specialtyDistribution = counselors.stream()
                .collect(Collectors.groupingBy(
                    c -> (String) c.getOrDefault("specialty", "未知"),
                    Collectors.counting()
                ));
            statistics.put("specialtyDistribution", specialtyDistribution);
        }
        
        return statistics;
    }

    /**
     * 检查是否有完整的时间范围覆盖
     */
    private boolean hasCompleteTimeRange(List<PsyTimeSlot> slots, LocalTime startTime, LocalTime endTime) {
        if (CollectionUtils.isEmpty(slots)) {
            return false;
        }
        
        // 按开始时间排序
        slots.sort(Comparator.comparing(PsyTimeSlot::getStartTime));
        
        LocalTime currentTime = startTime;
        for (PsyTimeSlot slot : slots) {
            if (slot.getStatus() != 0) { // 不可用
                continue;
            }
            
            if (slot.getStartTime().isAfter(currentTime)) {
                return false; // 有时间间隙
            }
            
            if (!slot.getEndTime().isAfter(currentTime)) {
                continue; // 时间槽在当前时间之前
            }
            
            currentTime = slot.getEndTime();
            
            if (!currentTime.isBefore(endTime)) {
                return true; // 已覆盖完整时间范围
            }
        }
        
        return false;
    }

    /**
     * 构建咨询师信息
     */
    private Map<String, Object> buildCounselorInfo(Long counselorId, List<PsyTimeSlot> slots, 
                                                   LocalDate date, LocalTime startTime, LocalTime endTime) {
        Map<String, Object> counselorInfo = new HashMap<>();
        
        counselorInfo.put("counselorId", counselorId);
        counselorInfo.put("availableSlots", slots.size());
        counselorInfo.put("timeRange", startTime + "-" + endTime);
        
        // 获取推荐度评分
        Map<String, Object> scoreInfo = getCounselorRecommendationScore(counselorId, date, startTime);
        counselorInfo.put("recommendationScore", scoreInfo.get("totalScore"));
        counselorInfo.put("scoreDetail", scoreInfo);
        
        // 这里可以添加更多咨询师信息，如姓名、专业、头像等
        // counselorInfo.put("name", "咨询师姓名");
        // counselorInfo.put("specialty", "专业领域");
        // counselorInfo.put("avatar", "头像URL");
        
        return counselorInfo;
    }

    /**
     * 计算时间偏好评分
     */
    private double calculateTimePreferenceScore(LocalTime startTime) {
        int hour = startTime.getHour();
        
        // 上午时段评分较高
        if (hour >= 9 && hour < 12) {
            return 8.0;
        }
        // 下午时段
        else if (hour >= 14 && hour < 17) {
            return 7.0;
        }
        // 晚上时段
        else if (hour >= 18 && hour < 21) {
            return 6.0;
        }
        // 其他时段
        else {
            return 5.0;
        }
    }

    /**
     * 计算可用性评分
     */
    private double calculateAvailabilityScore(Long counselorId, LocalDate date, LocalTime startTime) {
        // 查询咨询师当天的可用时间槽数量
        List<PsyTimeSlot> daySlots = timeSlotService.selectSlotsByCounselorAndDateRange(counselorId, date, date);
        
        if (CollectionUtils.isEmpty(daySlots)) {
            return 0.0;
        }
        
        long availableCount = daySlots.stream().filter(slot -> slot.getStatus() == 0).count();
        double availabilityRatio = (double) availableCount / daySlots.size();
        
        return availabilityRatio * 10.0; // 转换为10分制
    }

    /**
     * 计算可用时间段
     */
    private List<Map<String, Object>> calculateAvailableTimeRanges(List<PsyTimeSlot> slots) {
        List<Map<String, Object>> ranges = new ArrayList<>();
        
        List<PsyTimeSlot> availableSlots = slots.stream()
            .filter(slot -> slot.getStatus() == 0)
            .sorted(Comparator.comparing(PsyTimeSlot::getStartTime))
            .collect(Collectors.toList());
        
        if (availableSlots.isEmpty()) {
            return ranges;
        }
        
        LocalTime rangeStart = availableSlots.get(0).getStartTime();
        LocalTime rangeEnd = availableSlots.get(0).getEndTime();
        
        for (int i = 1; i < availableSlots.size(); i++) {
            PsyTimeSlot slot = availableSlots.get(i);
            
            if (slot.getStartTime().equals(rangeEnd)) {
                // 连续时间槽，扩展范围
                rangeEnd = slot.getEndTime();
            } else {
                // 不连续，保存当前范围并开始新范围
                Map<String, Object> range = new HashMap<>();
                range.put("startTime", rangeStart);
                range.put("endTime", rangeEnd);
                range.put("duration", java.time.Duration.between(rangeStart, rangeEnd).toMinutes());
                ranges.add(range);
                
                rangeStart = slot.getStartTime();
                rangeEnd = slot.getEndTime();
            }
        }
        
        // 添加最后一个范围
        Map<String, Object> range = new HashMap<>();
        range.put("startTime", rangeStart);
        range.put("endTime", rangeEnd);
        range.put("duration", java.time.Duration.between(rangeStart, rangeEnd).toMinutes());
        ranges.add(range);
        
        return ranges;
    }

    /**
     * 获取两个咨询师列表的交集
     */
    private List<Map<String, Object>> getIntersection(List<Map<String, Object>> list1, List<Map<String, Object>> list2) {
        Set<Long> counselorIds2 = list2.stream()
            .map(c -> (Long) c.get("counselorId"))
            .collect(Collectors.toSet());
        
        return list1.stream()
            .filter(c -> counselorIds2.contains((Long) c.get("counselorId")))
            .collect(Collectors.toList());
    }

    /**
     * 检查咨询师是否匹配用户偏好
     */
    private boolean matchesPreferences(Map<String, Object> counselor, Map<String, Object> preferences) {
        // 这里可以根据实际的偏好字段进行匹配
        // 例如：性别、专业领域、经验年限等
        
        // 示例：性别偏好
        if (preferences.containsKey("gender")) {
            String preferredGender = (String) preferences.get("gender");
            String counselorGender = (String) counselor.get("gender");
            if (preferredGender != null && !preferredGender.equals(counselorGender)) {
                return false;
            }
        }
        
        // 示例：专业领域偏好
        if (preferences.containsKey("specialty")) {
            String preferredSpecialty = (String) preferences.get("specialty");
            String counselorSpecialty = (String) counselor.get("specialty");
            if (preferredSpecialty != null && !preferredSpecialty.equals(counselorSpecialty)) {
                return false;
            }
        }
        
        return true;
    }
}
