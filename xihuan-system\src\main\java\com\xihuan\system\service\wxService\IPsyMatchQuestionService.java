package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyMatchQuestion;
import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.dto.MatchQuestionDTO;

import java.util.List;

/**
 * 极速匹配问题Service接口
 */
public interface IPsyMatchQuestionService {
    
    /**
     * 查询问题列表（包含选项）
     */
    List<PsyMatchQuestion> selectQuestionList();

    /**
     * 查询问题列表（包含选项，支持搜索）
     */
    List<PsyMatchQuestion> selectQuestionList(String title);
    
    /**
     * 新增问题
     */
    boolean addQuestion(PsyMatchQuestion question);
    
    /**
     * 修改问题
     */
    boolean updateQuestion(PsyMatchQuestion question);
    
    /**
     * 删除问题
     */
    boolean deleteQuestion(Long questionId);

    /**
     * 批量删除问题
     */
    boolean deleteQuestions(Long[] questionIds);
    
    /**
     * 根据ID查询问题
     */
    PsyMatchQuestion getQuestion(Long questionId);

    /**
     * 根据选项筛选匹配的咨询师
     */
    List<PsyConsultant> matchConsultants(List<MatchQuestionDTO> questions);

    /**
     * 更新选项关联的咨询师
     */
    boolean updateOptionConsultants(Long optionId, List<Long> consultantIds);

    /**
     * 获取选项关联的咨询师ID列表
     */
    List<Long> getOptionConsultants(Long optionId);
} 