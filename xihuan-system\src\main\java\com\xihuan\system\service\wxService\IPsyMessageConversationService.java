package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyMessageConversation;

import java.util.List;

/**
 * 消息会话服务接口
 */
public interface IPsyMessageConversationService {
    
    /**
     * 创建会话
     * 
     * @param userId 用户ID
     * @param consultantId 咨询师ID
     * @return 会话对象
     */
    PsyMessageConversation createConversation(Long userId, Long consultantId);
    
    /**
     * 获取会话列表
     * 
     * @param userId 用户ID
     * @return 会话列表
     */
    List<PsyMessageConversation> getUserConversations(Long userId);
    
    /**
     * 获取咨询师会话列表
     * 
     * @param consultantId 咨询师ID
     * @return 会话列表
     */
    List<PsyMessageConversation> getConsultantConversations(Long consultantId);
    
    /**
     * 获取所有会话列表(管理员使用)
     * 
     * @return 会话列表
     */
    List<PsyMessageConversation> getAllConversations();
    
    /**
     * 获取会话详情
     * 
     * @param conversationId 会话ID
     * @return 会话对象
     */
    PsyMessageConversation getConversation(Long conversationId);
    
    /**
     * 更新会话最后消息
     * 
     * @param conversationId 会话ID
     * @param lastSenderId 最后发送者ID
     * @param lastMessage 最后消息内容
     * @return 操作结果
     */
    boolean updateLastMessage(Long conversationId, Long lastSenderId, String lastMessage);
    
    /**
     * 增加未读消息计数
     * 
     * @param conversationId 会话ID
     * @param isUser 是否为用户（true为用户，false为咨询师）
     * @return 操作结果
     */
    boolean incrementUnreadCount(Long conversationId, boolean isUser);
    
    /**
     * 重置未读消息计数
     * 
     * @param conversationId 会话ID
     * @param isUser 是否为用户（true为用户，false为咨询师）
     * @return 操作结果
     */
    boolean resetUnreadCount(Long conversationId, boolean isUser);
} 