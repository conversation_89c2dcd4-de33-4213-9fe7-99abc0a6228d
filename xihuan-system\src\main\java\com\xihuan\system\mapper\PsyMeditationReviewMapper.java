package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyMeditationReview;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 冥想评价表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyMeditationReviewMapper {
    
    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    List<PsyMeditationReview> selectReviewList(PsyMeditationReview review);

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    PsyMeditationReview selectReviewById(Long id);

    /**
     * 查询评价详情（包含冥想和用户信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    PsyMeditationReview selectReviewWithDetails(Long id);

    /**
     * 根据冥想ID查询评价列表
     * 
     * @param meditationId 冥想ID
     * @return 评价集合
     */
    List<PsyMeditationReview> selectReviewsByMeditationId(Long meditationId);

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    List<PsyMeditationReview> selectReviewsByUserId(Long userId);

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int insertReview(PsyMeditationReview review);

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int updateReview(PsyMeditationReview review);

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    int deleteReviewById(Long id);

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    int deleteReviewByIds(Long[] ids);

    /**
     * 根据冥想ID删除评价
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    int deleteReviewByMeditationId(Long meditationId);

    /**
     * 批量根据冥想ID删除评价
     * 
     * @param meditationIds 冥想ID数组
     * @return 结果
     */
    int deleteReviewByMeditationIds(Long[] meditationIds);

    /**
     * 计算冥想平均评分
     * 
     * @param meditationId 冥想ID
     * @return 平均评分
     */
    BigDecimal calculateAverageRating(Long meditationId);

    /**
     * 统计冥想评价数量
     * 
     * @param meditationId 冥想ID
     * @return 评价数量
     */
    int countReviewsByMeditationId(Long meditationId);

    /**
     * 检查用户是否已评价冥想
     * 
     * @param userId 用户ID
     * @param meditationId 冥想ID
     * @return 评价数量
     */
    int checkUserReviewed(@Param("userId") Long userId, @Param("meditationId") Long meditationId);
}
