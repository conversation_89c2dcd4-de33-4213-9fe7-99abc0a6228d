package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTCompositeQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 复合题特殊计分Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTCompositeQuestionMapper {
    
    /**
     * 查询复合题列表
     * 
     * @param composite 复合题信息
     * @return 复合题集合
     */
    List<PsyTCompositeQuestion> selectCompositeList(PsyTCompositeQuestion composite);

    /**
     * 根据ID查询复合题
     * 
     * @param id 复合题ID
     * @return 复合题信息
     */
    PsyTCompositeQuestion selectCompositeById(Long id);

    /**
     * 根据题目ID查询复合题列表
     * 
     * @param questionId 题目ID
     * @return 复合题集合
     */
    List<PsyTCompositeQuestion> selectCompositesByQuestionId(Long questionId);

    /**
     * 新增复合题
     * 
     * @param composite 复合题信息
     * @return 结果
     */
    int insertComposite(PsyTCompositeQuestion composite);

    /**
     * 批量新增复合题
     * 
     * @param composites 复合题列表
     * @return 结果
     */
    int batchInsertComposites(List<PsyTCompositeQuestion> composites);

    /**
     * 修改复合题
     * 
     * @param composite 复合题信息
     * @return 结果
     */
    int updateComposite(PsyTCompositeQuestion composite);

    /**
     * 删除复合题
     * 
     * @param id 复合题ID
     * @return 结果
     */
    int deleteCompositeById(Long id);

    /**
     * 批量删除复合题
     * 
     * @param ids 需要删除的复合题ID
     * @return 结果
     */
    int deleteCompositeByIds(Long[] ids);

    /**
     * 根据题目ID删除复合题
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    int deleteCompositesByQuestionId(Long questionId);

    /**
     * 检查子项编号唯一性
     * 
     * @param questionId 题目ID
     * @param subitemNo 子项编号
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkSubitemNoUnique(@Param("questionId") Long questionId, 
                            @Param("subitemNo") String subitemNo, 
                            @Param("excludeId") Long excludeId);

    /**
     * 统计题目复合题数量
     * 
     * @param questionId 题目ID
     * @return 数量
     */
    int countCompositesByQuestionId(Long questionId);

    /**
     * 查询复合题权重统计
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    Map<String, Object> selectCompositeWeightStats(Long questionId);

    /**
     * 复制复合题到新题目
     * 
     * @param sourceQuestionId 源题目ID
     * @param targetQuestionId 目标题目ID
     * @return 结果
     */
    int copyCompositesToQuestion(@Param("sourceQuestionId") Long sourceQuestionId, 
                                @Param("targetQuestionId") Long targetQuestionId);
}
