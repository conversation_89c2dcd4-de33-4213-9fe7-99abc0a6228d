package com.xihuan.system.domain.wechat;

public class WxUserInfoRequest {
//    @NotBlank(message = "加密数据不能为空")
    private String encryptedData;

//    @NotBlank(message = "加密向量不能为空")
    private String iv;

//    @NotBlank(message = "登录凭证不能为空")
    private String code;

    public String getEncryptedData() {
        return encryptedData;
    }

    public void setEncryptedData(String encryptedData) {
        this.encryptedData = encryptedData;
    }

    public String getIv() {
        return iv;
    }

    public void setIv(String iv) {
        this.iv = iv;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    // 无参构造函数（必须显式添加）
    public WxUserInfoRequest() {
    }

    public WxUserInfoRequest(String encryptedData, String iv, String code) {
        this.encryptedData = encryptedData;
        this.iv = iv;
        this.code = code;
    }
}