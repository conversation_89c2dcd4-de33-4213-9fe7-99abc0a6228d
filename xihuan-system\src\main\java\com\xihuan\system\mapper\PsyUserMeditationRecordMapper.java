package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyUserMeditationRecord;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户冥想记录表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyUserMeditationRecordMapper {
    
    /**
     * 查询冥想记录列表
     * 
     * @param record 冥想记录信息
     * @return 冥想记录集合
     */
    List<PsyUserMeditationRecord> selectRecordList(PsyUserMeditationRecord record);

    /**
     * 根据ID查询冥想记录
     * 
     * @param id 冥想记录ID
     * @return 冥想记录信息
     */
    PsyUserMeditationRecord selectRecordById(Long id);

    /**
     * 根据用户ID和冥想ID查询最新记录
     * 
     * @param userId 用户ID
     * @param meditationId 冥想ID
     * @return 冥想记录信息
     */
    PsyUserMeditationRecord selectLatestRecordByUserAndMeditation(@Param("userId") Long userId, @Param("meditationId") Long meditationId);

    /**
     * 根据用户ID查询冥想记录列表
     * 
     * @param userId 用户ID
     * @return 冥想记录集合
     */
    List<PsyUserMeditationRecord> selectRecordsByUserId(Long userId);

    /**
     * 根据冥想ID查询冥想记录列表
     * 
     * @param meditationId 冥想ID
     * @return 冥想记录集合
     */
    List<PsyUserMeditationRecord> selectRecordsByMeditationId(Long meditationId);

    /**
     * 新增冥想记录
     * 
     * @param record 冥想记录信息
     * @return 结果
     */
    int insertRecord(PsyUserMeditationRecord record);

    /**
     * 修改冥想记录
     * 
     * @param record 冥想记录信息
     * @return 结果
     */
    int updateRecord(PsyUserMeditationRecord record);

    /**
     * 删除冥想记录
     * 
     * @param id 冥想记录ID
     * @return 结果
     */
    int deleteRecordById(Long id);

    /**
     * 批量删除冥想记录
     * 
     * @param ids 需要删除的冥想记录ID
     * @return 结果
     */
    int deleteRecordByIds(Long[] ids);

    /**
     * 根据用户ID删除冥想记录
     * 
     * @param userId 用户ID
     * @return 结果
     */
    int deleteRecordByUserId(Long userId);

    /**
     * 根据冥想ID删除冥想记录
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    int deleteRecordByMeditationId(Long meditationId);

    /**
     * 统计用户冥想总时长
     * 
     * @param userId 用户ID
     * @return 总时长（秒）
     */
    int sumUserMeditationDuration(Long userId);

    /**
     * 统计用户冥想次数
     * 
     * @param userId 用户ID
     * @return 冥想次数
     */
    int countUserMeditationTimes(Long userId);

    /**
     * 统计用户完成的冥想次数
     * 
     * @param userId 用户ID
     * @return 完成次数
     */
    int countUserCompletedMeditations(Long userId);

    /**
     * 统计冥想的播放次数
     * 
     * @param meditationId 冥想ID
     * @return 播放次数
     */
    int countMeditationPlayTimes(Long meditationId);
}
