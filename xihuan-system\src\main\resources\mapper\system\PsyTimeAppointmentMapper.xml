<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyTimeAppointmentMapper">
    
    <resultMap type="PsyTimeAppointment" id="PsyTimeAppointmentResult">
        <result property="id"               column="id"               />
        <result property="userId"           column="user_id"          />
        <result property="centerId"         column="center_id"        />
        <result property="startSlotId"      column="start_slot_id"    />
        <result property="dateKey"          column="date_key"         />
        <result property="startTime"        column="start_time"       />
        <result property="endTime"          column="end_time"         />
        <result property="duration"         column="duration"         />
        <result property="status"           column="status"           />
        <result property="paymentStatus"    column="payment_status"   />
        <result property="paymentId"        column="payment_id"       />
        <result property="delFlag"          column="del_flag"         />
        <result property="createBy"         column="create_by"        />
        <result property="createTime"       column="create_time"      />
        <result property="updateBy"         column="update_by"        />
        <result property="updateTime"       column="update_time"      />
        <result property="remark"           column="remark"           />
    </resultMap>

    <sql id="selectPsyTimeAppointmentVo">
        select id, user_id, center_id, start_slot_id, date_key, start_time, end_time, 
               duration, status, payment_status, payment_id, del_flag, 
               create_by, create_time, update_by, update_time, remark
        from psy_time_appointment
    </sql>

    <select id="selectAppointmentList" parameterType="PsyTimeAppointment" resultMap="PsyTimeAppointmentResult">
        <include refid="selectPsyTimeAppointmentVo"/>
        <where>  
            del_flag = 0
            <if test="userId != null"> and user_id = #{userId}</if>
            <if test="centerId != null"> and center_id = #{centerId}</if>
            <if test="dateKey != null and dateKey != ''"> and date_key = #{dateKey}</if>
            <if test="status != null"> and status = #{status}</if>
            <if test="paymentStatus != null"> and payment_status = #{paymentStatus}</if>
        </where>
        order by create_time desc
    </select>
    
    <select id="selectAppointmentById" parameterType="String" resultMap="PsyTimeAppointmentResult">
        <include refid="selectPsyTimeAppointmentVo"/>
        where id = #{id}
    </select>
    
    <select id="selectAppointmentsByUserId" resultMap="PsyTimeAppointmentResult">
        <include refid="selectPsyTimeAppointmentVo"/>
        where user_id = #{userId} and del_flag = 0
        <if test="status != null"> and status = #{status}</if>
        order by create_time desc
    </select>
    
    <select id="selectAppointmentsByCounselor" resultMap="PsyTimeAppointmentResult">
        <include refid="selectPsyTimeAppointmentVo"/>
        where del_flag = 0
        <if test="startDate != null and startDate != ''"> and date_key &gt;= #{startDate}</if>
        <if test="endDate != null and endDate != ''"> and date_key &lt;= #{endDate}</if>
        order by date_key, start_time
    </select>
        
    <insert id="insertAppointment" parameterType="PsyTimeAppointment">
        insert into psy_time_appointment
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">id,</if>
            <if test="userId != null">user_id,</if>
            <if test="centerId != null">center_id,</if>
            <if test="startSlotId != null">start_slot_id,</if>
            <if test="dateKey != null and dateKey != ''">date_key,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="duration != null">duration,</if>
            <if test="status != null">status,</if>
            <if test="paymentStatus != null">payment_status,</if>
            <if test="paymentId != null">payment_id,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null and id != ''">#{id},</if>
            <if test="userId != null">#{userId},</if>
            <if test="centerId != null">#{centerId},</if>
            <if test="startSlotId != null">#{startSlotId},</if>
            <if test="dateKey != null and dateKey != ''">#{dateKey},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="duration != null">#{duration},</if>
            <if test="status != null">#{status},</if>
            <if test="paymentStatus != null">#{paymentStatus},</if>
            <if test="paymentId != null">#{paymentId},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateAppointment" parameterType="PsyTimeAppointment">
        update psy_time_appointment
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="centerId != null">center_id = #{centerId},</if>
            <if test="startSlotId != null">start_slot_id = #{startSlotId},</if>
            <if test="dateKey != null and dateKey != ''">date_key = #{dateKey},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="status != null">status = #{status},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="paymentId != null">payment_id = #{paymentId},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateAppointmentStatus">
        update psy_time_appointment set status = #{status}, update_time = now()
        where id = #{id}
    </update>

    <update id="updatePaymentStatus">
        update psy_time_appointment 
        set payment_status = #{paymentStatus}, payment_id = #{paymentId}, update_time = now()
        where id = #{id}
    </update>

    <delete id="deleteAppointmentById" parameterType="String">
        update psy_time_appointment set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteAppointmentByIds" parameterType="String">
        update psy_time_appointment set del_flag = 1 where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="checkSlotBooked" parameterType="Long" resultType="int">
        select count(1) from psy_time_appointment a
        join psy_time_appointment_slot s on a.id = s.appointment_id
        where s.slot_id = #{slotId} and a.del_flag = 0 and a.status in (0, 1)
    </select>
    
    <select id="generateAppointmentId" resultType="String">
        select concat('APT', date_format(now(), '%Y%m%d%H%i%s'), lpad(floor(rand() * 1000), 3, '0'))
    </select>
</mapper>
