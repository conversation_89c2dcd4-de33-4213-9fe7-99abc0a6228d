package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.mapper.PsyTimeRangeMapper;
import com.xihuan.system.service.IPsyTimeRangeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 时间段定义Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeRangeServiceImpl implements IPsyTimeRangeService {
    
    @Autowired
    private PsyTimeRangeMapper timeRangeMapper;

    /**
     * 查询时间段定义列表
     * 
     * @param timeRange 时间段定义
     * @return 时间段定义
     */
    @Override
    public List<PsyTimeRange> selectTimeRangeList(PsyTimeRange timeRange) {
        return timeRangeMapper.selectTimeRangeList(timeRange);
    }

    /**
     * 查询所有有效的时间段定义
     * 
     * @return 时间段定义集合
     */
    @Override
    public List<PsyTimeRange> selectAllActiveTimeRanges() {
        return timeRangeMapper.selectAllActiveTimeRanges();
    }

    /**
     * 根据ID查询时间段定义
     * 
     * @param id 时间段定义主键
     * @return 时间段定义
     */
    @Override
    public PsyTimeRange selectTimeRangeById(Long id) {
        return timeRangeMapper.selectTimeRangeById(id);
    }

    /**
     * 根据时间查询所属时间段
     * 
     * @param hour 小时（0-23）
     * @return 时间段定义
     */
    @Override
    public PsyTimeRange selectTimeRangeByHour(Integer hour) {
        return timeRangeMapper.selectTimeRangeByHour(hour);
    }

    /**
     * 新增时间段定义
     * 
     * @param timeRange 时间段定义
     * @return 结果
     */
    @Override
    public int insertTimeRange(PsyTimeRange timeRange) {
        timeRange.setCreateTime(DateUtils.getNowDate());
        return timeRangeMapper.insertTimeRange(timeRange);
    }

    /**
     * 修改时间段定义
     * 
     * @param timeRange 时间段定义
     * @return 结果
     */
    @Override
    public int updateTimeRange(PsyTimeRange timeRange) {
        timeRange.setUpdateTime(DateUtils.getNowDate());
        return timeRangeMapper.updateTimeRange(timeRange);
    }

    /**
     * 批量删除时间段定义
     * 
     * @param ids 需要删除的时间段定义主键
     * @return 结果
     */
    @Override
    public int deleteTimeRangeByIds(Long[] ids) {
        return timeRangeMapper.deleteTimeRangeByIds(ids);
    }

    /**
     * 删除时间段定义信息
     * 
     * @param id 时间段定义主键
     * @return 结果
     */
    @Override
    public int deleteTimeRangeById(Long id) {
        return timeRangeMapper.deleteTimeRangeById(id);
    }

    /**
     * 校验时间段名称是否唯一
     * 
     * @param timeRange 时间段定义信息
     * @return 结果
     */
    @Override
    public String checkTimeRangeNameUnique(PsyTimeRange timeRange) {
        Long id = StringUtils.isNull(timeRange.getId()) ? -1L : timeRange.getId();
        PsyTimeRange info = timeRangeMapper.checkTimeRangeNameUnique(timeRange.getName(), id) > 0 ? new PsyTimeRange() : null;
        if (StringUtils.isNotNull(info)) {
            return "时间段名称已存在";
        }
        return "0";
    }

    /**
     * 初始化默认时间段
     * 
     * @return 结果
     */
    @Override
    @Transactional
    public int initDefaultTimeRanges() {
        // 检查是否已经初始化
        List<PsyTimeRange> existing = selectAllActiveTimeRanges();
        if (!existing.isEmpty()) {
            return 0;
        }

        // 创建默认时间段
        PsyTimeRange[] defaultRanges = {
            createTimeRange("上午", "https://example.com/icons/morning.png", 9, 12),
            createTimeRange("中午", "https://example.com/icons/noon.png", 12, 14),
            createTimeRange("下午", "https://example.com/icons/afternoon.png", 14, 18),
            createTimeRange("晚上", "https://example.com/icons/evening.png", 18, 21)
        };

        int result = 0;
        for (PsyTimeRange range : defaultRanges) {
            result += insertTimeRange(range);
        }
        return result;
    }

    /**
     * 创建时间段对象
     */
    private PsyTimeRange createTimeRange(String name, String iconUrl, int startHour, int endHour) {
        PsyTimeRange range = new PsyTimeRange();
        range.setName(name);
        range.setIconUrl(iconUrl);
        range.setStartHour(startHour);
        range.setEndHour(endHour);
        range.setDelFlag(0);
        return range;
    }
}
