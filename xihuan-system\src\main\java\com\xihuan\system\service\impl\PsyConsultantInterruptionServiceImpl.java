package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyConsultantInterruption;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyConsultantInterruptionMapper;
import com.xihuan.system.service.IPsyConsultantInterruptionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 咨询中断记录表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyConsultantInterruptionServiceImpl implements IPsyConsultantInterruptionService {
    
    @Autowired
    private PsyConsultantInterruptionMapper interruptionMapper;

    /**
     * 查询中断记录列表
     * 
     * @param interruption 中断记录信息
     * @return 中断记录集合
     */
    @Override
    public List<PsyConsultantInterruption> selectInterruptionList(PsyConsultantInterruption interruption) {
        return interruptionMapper.selectInterruptionList(interruption);
    }

    /**
     * 根据ID查询中断记录
     * 
     * @param id 中断记录ID
     * @return 中断记录信息
     */
    @Override
    public PsyConsultantInterruption selectInterruptionById(Long id) {
        return interruptionMapper.selectInterruptionById(id);
    }

    /**
     * 根据咨询记录ID查询中断记录列表
     * 
     * @param recordId 咨询记录ID
     * @return 中断记录集合
     */
    @Override
    public List<PsyConsultantInterruption> selectInterruptionsByRecordId(Long recordId) {
        return interruptionMapper.selectInterruptionsByRecordId(recordId);
    }

    /**
     * 新增中断记录
     * 
     * @param interruption 中断记录信息
     * @return 结果
     */
    @Override
    public int insertInterruption(PsyConsultantInterruption interruption) {
        interruption.setCreateTime(DateUtils.getNowDate());
        
        // 设置默认中断时长为0，实际时长需要在恢复时计算
        if (interruption.getDuration() == null) {
            interruption.setDuration(0);
        }
        
        return interruptionMapper.insertInterruption(interruption);
    }

    /**
     * 修改中断记录
     * 
     * @param interruption 中断记录信息
     * @return 结果
     */
    @Override
    public int updateInterruption(PsyConsultantInterruption interruption) {
        return interruptionMapper.updateInterruption(interruption);
    }

    /**
     * 删除中断记录
     * 
     * @param id 中断记录ID
     * @return 结果
     */
    @Override
    public int deleteInterruptionById(Long id) {
        return interruptionMapper.deleteInterruptionById(id);
    }

    /**
     * 批量删除中断记录
     * 
     * @param ids 需要删除的中断记录ID
     * @return 结果
     */
    @Override
    public int deleteInterruptionByIds(Long[] ids) {
        return interruptionMapper.deleteInterruptionByIds(ids);
    }

    /**
     * 根据咨询记录ID删除中断记录
     * 
     * @param recordId 咨询记录ID
     * @return 结果
     */
    @Override
    public int deleteInterruptionByRecordId(Long recordId) {
        return interruptionMapper.deleteInterruptionByRecordId(recordId);
    }

    /**
     * 统计咨询记录的中断次数
     * 
     * @param recordId 咨询记录ID
     * @return 中断次数
     */
    @Override
    public int countInterruptionsByRecordId(Long recordId) {
        return interruptionMapper.countInterruptionsByRecordId(recordId);
    }

    /**
     * 统计咨询记录的总中断时长
     * 
     * @param recordId 咨询记录ID
     * @return 总中断时长(分钟)
     */
    @Override
    public int sumInterruptionDuration(Long recordId) {
        return interruptionMapper.sumInterruptionDuration(recordId);
    }

    /**
     * 根据中断类型统计
     * 
     * @param recordId 咨询记录ID
     * @param interruptType 中断类型
     * @return 中断次数
     */
    @Override
    public int countInterruptionsByType(Long recordId, String interruptType) {
        return interruptionMapper.countInterruptionsByType(recordId, interruptType);
    }

    /**
     * 获取咨询记录的中断统计
     * 
     * @param recordId 咨询记录ID
     * @return 中断统计数据
     */
    @Override
    public Map<String, Object> getInterruptionStats(Long recordId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 总中断次数
        int totalCount = countInterruptionsByRecordId(recordId);
        stats.put("totalCount", totalCount);
        
        // 总中断时长
        int totalDuration = sumInterruptionDuration(recordId);
        stats.put("totalDuration", totalDuration);
        
        // 各类型中断次数
        int userInterruptions = countInterruptionsByType(recordId, "用户中断");
        stats.put("userInterruptions", userInterruptions);
        
        int consultantInterruptions = countInterruptionsByType(recordId, "咨询师中断");
        stats.put("consultantInterruptions", consultantInterruptions);
        
        int technicalInterruptions = countInterruptionsByType(recordId, "技术问题");
        stats.put("technicalInterruptions", technicalInterruptions);
        
        // 平均中断时长
        int avgDuration = totalCount > 0 ? totalDuration / totalCount : 0;
        stats.put("avgDuration", avgDuration);
        
        return stats;
    }
}
