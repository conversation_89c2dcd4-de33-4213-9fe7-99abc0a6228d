<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyStoreBusinessHoursMapper">
    
    <resultMap type="PsyStoreBusinessHours" id="PsyStoreBusinessHoursResult">
        <id     property="id"       column="id"        />
        <result property="storeId"  column="store_id"  />
        <result property="startTime" column="start_time"/>
        <result property="endTime"  column="end_time"  />
        <result property="is24h"    column="is_24h"    />
    </resultMap>

    <sql id="selectPsyStoreBusinessHoursVo">
        select id, store_id, start_time, end_time, is_24h
        from psy_store_business_hours
    </sql>

    <select id="selectPsyStoreBusinessHoursList" parameterType="PsyStoreBusinessHours" resultMap="PsyStoreBusinessHoursResult">
        <include refid="selectPsyStoreBusinessHoursVo"/>
        <where>
            <if test="storeId != null "> and store_id = #{storeId}</if>
            <if test="is24h != null "> and is_24h = #{is24h}</if>
        </where>
    </select>
    
    <select id="selectPsyStoreBusinessHoursById" parameterType="Long" resultMap="PsyStoreBusinessHoursResult">
        <include refid="selectPsyStoreBusinessHoursVo"/>
        where id = #{id}
    </select>
    
    <select id="selectPsyStoreBusinessHoursByStoreId" parameterType="Long" resultMap="PsyStoreBusinessHoursResult">
        <include refid="selectPsyStoreBusinessHoursVo"/>
        where store_id = #{storeId}
    </select>
        
    <insert id="insertPsyStoreBusinessHours" parameterType="PsyStoreBusinessHours" useGeneratedKeys="true" keyProperty="id">
        insert into psy_store_business_hours
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeId != null">store_id,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="is24h != null">is_24h,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="storeId != null">#{storeId},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="is24h != null">#{is24h},</if>
        </trim>
    </insert>

    <update id="updatePsyStoreBusinessHours" parameterType="PsyStoreBusinessHours">
        update psy_store_business_hours
        <trim prefix="SET" suffixOverrides=",">
            <if test="storeId != null">store_id = #{storeId},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="is24h != null">is_24h = #{is24h},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePsyStoreBusinessHoursById" parameterType="Long">
        delete from psy_store_business_hours where id = #{id}
    </delete>

    <delete id="deletePsyStoreBusinessHoursByIds" parameterType="String">
        delete from psy_store_business_hours where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <delete id="deletePsyStoreBusinessHoursByStoreId" parameterType="Long">
        delete from psy_store_business_hours where store_id = #{storeId}
    </delete>
</mapper> 