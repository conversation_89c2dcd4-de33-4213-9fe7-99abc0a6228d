package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentParticipant;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.system.mapper.PsyTEnterpriseAssessmentParticipantMapper;
import com.xihuan.system.service.IPsyTEnterpriseAssessmentParticipantService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业测评参与者Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTEnterpriseAssessmentParticipantServiceImpl implements IPsyTEnterpriseAssessmentParticipantService {
    
    @Autowired
    private PsyTEnterpriseAssessmentParticipantMapper participantMapper;

    /**
     * 查询企业测评参与者列表
     * 
     * @param participant 企业测评参与者
     * @return 企业测评参与者集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectParticipantList(PsyTEnterpriseAssessmentParticipant participant) {
        return participantMapper.selectParticipantList(participant);
    }

    /**
     * 根据ID查询企业测评参与者
     * 
     * @param id 企业测评参与者ID
     * @return 企业测评参与者
     */
    @Override
    public PsyTEnterpriseAssessmentParticipant selectParticipantById(Long id) {
        return participantMapper.selectParticipantById(id);
    }

    /**
     * 查询企业测评参与者详情
     * 
     * @param id 企业测评参与者ID
     * @return 企业测评参与者详情
     */
    @Override
    public PsyTEnterpriseAssessmentParticipant selectParticipantWithDetails(Long id) {
        return participantMapper.selectParticipantWithDetails(id);
    }

    /**
     * 根据计划ID查询参与者列表
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectParticipantsByPlanId(Long planId) {
        return participantMapper.selectParticipantsByPlanId(planId);
    }

    /**
     * 查询计划未开始参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectNotStartedParticipants(Long planId) {
        return participantMapper.selectNotStartedParticipants(planId);
    }

    /**
     * 查询计划进行中参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectInProgressParticipants(Long planId) {
        return participantMapper.selectInProgressParticipants(planId);
    }

    /**
     * 查询计划已完成参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectCompletedParticipants(Long planId) {
        return participantMapper.selectCompletedParticipants(planId);
    }

    /**
     * 查询计划已放弃参与者
     * 
     * @param planId 计划ID
     * @return 参与者集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectAbandonedParticipants(Long planId) {
        return participantMapper.selectAbandonedParticipants(planId);
    }

    /**
     * 新增企业测评参与者
     * 
     * @param participant 企业测评参与者
     * @return 结果
     */
    @Override
    @Transactional
    public int insertParticipant(PsyTEnterpriseAssessmentParticipant participant) {
        // 设置默认值
        if (participant.getParticipationStatus() == null) {
            participant.setParticipationStatus(PsyTEnterpriseAssessmentParticipant.STATUS_NOT_STARTED);
        }
        if (participant.getIsReminded() == null) {
            participant.setIsReminded(0);
        }
        if (participant.getReminderCount() == null) {
            participant.setReminderCount(0);
        }
        if (participant.getDelFlag() == null) {
            participant.setDelFlag("0");
        }
        
        return participantMapper.insertParticipant(participant);
    }

    /**
     * 修改企业测评参与者
     * 
     * @param participant 企业测评参与者
     * @return 结果
     */
    @Override
    @Transactional
    public int updateParticipant(PsyTEnterpriseAssessmentParticipant participant) {
        return participantMapper.updateParticipant(participant);
    }

    /**
     * 删除企业测评参与者
     * 
     * @param ids 需要删除的企业测评参与者ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteParticipantByIds(Long[] ids) {
        // 检查参与者是否可以删除
        for (Long id : ids) {
            PsyTEnterpriseAssessmentParticipant participant = selectParticipantById(id);
            if (participant != null && 
                PsyTEnterpriseAssessmentParticipant.STATUS_IN_PROGRESS.equals(participant.getParticipationStatus())) {
                throw new ServiceException("进行中的参与者不能删除");
            }
        }
        
        return participantMapper.deleteParticipantByIds(ids);
    }

    /**
     * 批量新增参与者
     * 
     * @param participants 参与者列表
     * @return 结果
     */
    @Override
    @Transactional
    public int batchInsertParticipants(List<PsyTEnterpriseAssessmentParticipant> participants) {
        if (participants == null || participants.isEmpty()) {
            return 0;
        }
        
        // 设置默认值
        for (PsyTEnterpriseAssessmentParticipant participant : participants) {
            if (participant.getParticipationStatus() == null) {
                participant.setParticipationStatus(PsyTEnterpriseAssessmentParticipant.STATUS_NOT_STARTED);
            }
            if (participant.getIsReminded() == null) {
                participant.setIsReminded(0);
            }
            if (participant.getReminderCount() == null) {
                participant.setReminderCount(0);
            }
            if (participant.getDelFlag() == null) {
                participant.setDelFlag("0");
            }
        }
        
        return participantMapper.batchInsertParticipants(participants);
    }

    /**
     * 更新参与者状态
     * 
     * @param participantId 参与者ID
     * @param status 状态
     * @return 结果
     */
    @Override
    @Transactional
    public int updateParticipantStatus(Long participantId, Integer status) {
        return participantMapper.updateParticipantStatus(participantId, status);
    }

    /**
     * 批量更新参与者状态
     * 
     * @param participantIds 参与者ID数组
     * @param status 状态
     * @return 结果
     */
    @Override
    @Transactional
    public int batchUpdateParticipantStatus(Long[] participantIds, Integer status) {
        return participantMapper.batchUpdateParticipantStatus(participantIds, status);
    }

    /**
     * 查询员工参与历史
     * 
     * @param employeeId 员工ID
     * @return 参与历史
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectEmployeeParticipationHistory(Long employeeId) {
        return participantMapper.selectEmployeeParticipationHistory(employeeId);
    }

    /**
     * 查询参与统计信息
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectParticipationStats(Long planId) {
        return participantMapper.selectParticipationStats(planId);
    }

    /**
     * 查询部门参与统计
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectDepartmentParticipationStats(Long planId) {
        return participantMapper.selectDepartmentParticipationStats(planId);
    }

    /**
     * 查询参与趋势统计
     * 
     * @param planId 计划ID
     * @param days 天数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> selectParticipationTrendStats(Long planId, Integer days) {
        return participantMapper.selectParticipationTrendStats(planId, days);
    }

    /**
     * 查询参与度排行
     * 
     * @param enterpriseId 企业ID
     * @param limit 限制数量
     * @return 排行数据
     */
    @Override
    public List<Map<String, Object>> selectParticipationRanking(Long enterpriseId, Integer limit) {
        return participantMapper.selectParticipationRanking(enterpriseId, limit);
    }

    /**
     * 发送参与提醒
     * 
     * @param participantIds 参与者ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int sendParticipationReminder(Long[] participantIds) {
        // TODO: 实现发送提醒逻辑
        
        // 更新提醒信息
        for (Long participantId : participantIds) {
            updateReminderInfo(participantId);
        }
        
        return participantIds.length;
    }

    /**
     * 查询需要提醒的参与者
     * 
     * @param planId 计划ID
     * @return 参与者列表
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectParticipantsNeedReminder(Long planId) {
        return participantMapper.selectParticipantsNeedReminder(planId);
    }

    /**
     * 更新提醒信息
     * 
     * @param participantId 参与者ID
     * @return 结果
     */
    @Override
    @Transactional
    public int updateReminderInfo(Long participantId) {
        PsyTEnterpriseAssessmentParticipant participant = new PsyTEnterpriseAssessmentParticipant();
        participant.setId(participantId);
        participant.setIsReminded(1);
        participant.setLastReminderTime(new Date());
        
        // 增加提醒次数
        participantMapper.incrementReminderCount(participantId);
        
        return updateParticipant(participant);
    }

    /**
     * 检查员工是否已参与计划
     * 
     * @param planId 计划ID
     * @param employeeId 员工ID
     * @return 是否已参与
     */
    @Override
    public boolean checkEmployeeParticipated(Long planId, Long employeeId) {
        int count = participantMapper.checkEmployeeParticipated(planId, employeeId);
        return count > 0;
    }

    /**
     * 查询员工当前参与的计划
     * 
     * @param employeeId 员工ID
     * @return 参与记录列表
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectEmployeeCurrentParticipations(Long employeeId) {
        return participantMapper.selectEmployeeCurrentParticipations(employeeId);
    }

    /**
     * 查询超时未完成的参与者
     * 
     * @param planId 计划ID
     * @return 参与者列表
     */
    @Override
    public List<PsyTEnterpriseAssessmentParticipant> selectTimeoutParticipants(Long planId) {
        return participantMapper.selectTimeoutParticipants(planId);
    }

    /**
     * 自动更新超时参与者状态
     * 
     * @param planId 计划ID
     * @return 更新数量
     */
    @Override
    @Transactional
    public int autoUpdateTimeoutParticipants(Long planId) {
        return participantMapper.autoUpdateTimeoutParticipants(planId);
    }

    /**
     * 查询参与者完成率分布
     * 
     * @param enterpriseId 企业ID
     * @return 分布数据
     */
    @Override
    public List<Map<String, Object>> selectCompletionRateDistribution(Long enterpriseId) {
        return participantMapper.selectCompletionRateDistribution(enterpriseId);
    }

    /**
     * 查询参与者测评时长统计
     * 
     * @param planId 计划ID
     * @return 统计数据
     */
    @Override
    public Map<String, Object> selectDurationStats(Long planId) {
        return participantMapper.selectDurationStats(planId);
    }

    /**
     * 导出参与者数据
     * 
     * @param planId 计划ID
     * @return 导出数据
     */
    @Override
    public List<Map<String, Object>> exportParticipantData(Long planId) {
        return participantMapper.exportParticipantData(planId);
    }

    /**
     * 查询参与者测评结果分布
     * 
     * @param planId 计划ID
     * @return 结果分布
     */
    @Override
    public List<Map<String, Object>> selectResultDistribution(Long planId) {
        return participantMapper.selectResultDistribution(planId);
    }
}
