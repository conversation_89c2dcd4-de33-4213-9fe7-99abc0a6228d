package com.xihuan.common.core.domain.kangnili;

import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

@Data
public class KangniliQuestion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    /** 题干标题 */
    @NotBlank(message = "标题不能为空")
    @Size(min = 0, max = 200, message = "标题长度不能超过200个字符")
    private String title;

    /** 情景描述 */
    @NotBlank(message = "情景描述不能为空")
    private String scenario;

    /** 测量维度 */
    @NotBlank(message = "测量维度不能为空")
    @Size(min = 0, max = 20, message = "维度长度不能超过20个字符")
    private String dimension;

    /** 显示顺序 */
    @NotNull(message = "显示顺序不能为空")
    @Min(value = 0, message = "显示顺序最小值为0")
    private Integer orderNum;

    private List<KangniliOption> options; // 新增关联字段
}

