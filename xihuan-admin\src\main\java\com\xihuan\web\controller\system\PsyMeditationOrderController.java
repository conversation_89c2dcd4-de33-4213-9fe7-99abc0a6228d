package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyMeditationOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * 冥想订单表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/meditationOrder")
public class PsyMeditationOrderController extends BaseController {
    
    @Autowired
    private IPsyMeditationOrderService orderService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyMeditationOrder order) {
        startPage();
        List<PsyMeditationOrder> list = orderService.selectOrderList(order);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:export')")
    @Log(title = "冥想订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyMeditationOrder order) {
        List<PsyMeditationOrder> list = orderService.selectOrderList(order);
        ExcelUtil<PsyMeditationOrder> util = new ExcelUtil<PsyMeditationOrder>(PsyMeditationOrder.class);
        util.exportExcel(response, list, "冥想订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(orderService.selectOrderById(id));
    }

    /**
     * 获取订单详细信息（包含冥想和用户信息）
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(orderService.selectOrderWithDetails(id));
    }

    /**
     * 根据订单号查询订单
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:query')")
    @GetMapping(value = "/orderNo/{orderNo}")
    public AjaxResult getByOrderNo(@PathVariable("orderNo") String orderNo) {
        return success(orderService.selectOrderByOrderNo(orderNo));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:add')")
    @Log(title = "冥想订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyMeditationOrder order) {
        return toAjax(orderService.insertOrder(order));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:edit')")
    @Log(title = "冥想订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyMeditationOrder order) {
        return toAjax(orderService.updateOrder(order));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:remove')")
    @Log(title = "冥想订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(orderService.deleteOrderByIds(ids));
    }

    /**
     * 更新订单支付状态
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:edit')")
    @Log(title = "冥想订单支付", businessType = BusinessType.UPDATE)
    @PutMapping("/payment/{orderNo}")
    public AjaxResult updatePaymentStatus(@PathVariable String orderNo, 
                                        @RequestParam Integer status,
                                        @RequestParam(required = false) String paymentMethod,
                                        @RequestParam(required = false) String transactionId) {
        return toAjax(orderService.updateOrderPaymentStatus(orderNo, status, paymentMethod, transactionId, DateUtils.getNowDate()));
    }

    /**
     * 订单退款
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:edit')")
    @Log(title = "冥想订单退款", businessType = BusinessType.UPDATE)
    @PutMapping("/refund/{orderNo}")
    public AjaxResult refund(@PathVariable String orderNo, @RequestParam BigDecimal refundAmount) {
        return toAjax(orderService.updateOrderRefund(orderNo, refundAmount, DateUtils.getNowDate()));
    }

    /**
     * 生成订单号
     */
    @PreAuthorize("@ss.hasPermi('system:meditationOrder:add')")
    @GetMapping("/generateOrderNo")
    public AjaxResult generateOrderNo() {
        String orderNo = orderService.generateOrderNo();
        return success(orderNo);
    }
}
