<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentOrderMapper">

    <!-- 结果映射 -->
    <resultMap id="OrderResultMap" type="PsyAssessmentOrder">
        <id property="id" column="id"/>
        <result property="orderNo" column="order_no"/>
        <result property="userId" column="user_id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="scaleName" column="scale_name"/>
        <result property="originalPrice" column="original_price"/>
        <result property="actualPrice" column="actual_price"/>
        <result property="discountAmount" column="discount_amount"/>
        <result property="couponId" column="coupon_id"/>
        <result property="paymentMethod" column="payment_method"/>
        <result property="paymentStatus" column="payment_status"/>
        <result property="orderStatus" column="order_status"/>
        <result property="payTime" column="pay_time"/>
        <result property="expireTime" column="expire_time"/>
        <result property="refundAmount" column="refund_amount"/>
        <result property="refundTime" column="refund_time"/>
        <result property="refundReason" column="refund_reason"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <!-- 带详情的结果映射 -->
    <resultMap id="OrderWithDetailsMap" type="PsyAssessmentOrder" extends="OrderResultMap">
        <association property="user" javaType="SysUser">
            <id property="userId" column="u_user_id"/>
            <result property="userName" column="u_user_name"/>
            <result property="nickName" column="u_nick_name"/>
            <result property="email" column="u_email"/>
            <result property="phonenumber" column="u_phonenumber"/>
            <result property="avatar" column="u_avatar"/>
        </association>
        <association property="scale" javaType="PsyAssessmentScale">
            <id property="id" column="s_id"/>
            <result property="scaleName" column="s_scale_name"/>
            <result property="scaleCode" column="s_scale_code"/>
            <result property="description" column="s_description"/>
            <result property="price" column="s_price"/>
        </association>
    </resultMap>

    <!-- 查询订单列表 -->
    <select id="selectOrderList" parameterType="PsyAssessmentOrder" resultMap="OrderWithDetailsMap">
        SELECT o.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.email as u_email, u.phonenumber as u_phonenumber, u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name, s.scale_code as s_scale_code,
               s.description as s_description, s.price as s_price
        FROM psy_t_order o
        LEFT JOIN sys_user u ON o.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON o.scale_id = s.id
        WHERE o.del_flag = 0
        <if test="userId != null">
            AND o.user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND o.scale_id = #{scaleId}
        </if>
        <if test="orderNo != null and orderNo != ''">
            AND o.order_no = #{orderNo}
        </if>
        <if test="paymentStatus != null">
            AND o.payment_status = #{paymentStatus}
        </if>
        <if test="orderStatus != null">
            AND o.order_status = #{orderStatus}
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            AND o.create_time >= #{params.beginTime}
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            AND o.create_time &lt;= #{params.endTime}
        </if>
        ORDER BY o.create_time DESC
    </select>

    <!-- 根据ID查询订单 -->
    <select id="selectOrderById" parameterType="Long" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询订单详情 -->
    <select id="selectOrderWithDetails" parameterType="Long" resultMap="OrderWithDetailsMap">
        SELECT o.*, 
               u.user_id as u_user_id, u.user_name as u_user_name, u.nick_name as u_nick_name,
               u.email as u_email, u.phonenumber as u_phonenumber, u.avatar as u_avatar,
               s.id as s_id, s.scale_name as s_scale_name, s.scale_code as s_scale_code,
               s.description as s_description, s.price as s_price
        FROM psy_t_order o
        LEFT JOIN sys_user u ON o.user_id = u.user_id
        LEFT JOIN psy_t_scale s ON o.scale_id = s.id
        WHERE o.id = #{id} AND o.del_flag = 0
    </select>

    <!-- 根据订单编号查询订单 -->
    <select id="selectOrderByOrderNo" parameterType="String" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order WHERE order_no = #{orderNo} AND del_flag = 0
    </select>

    <!-- 根据用户ID查询订单列表 -->
    <select id="selectOrdersByUserId" parameterType="Long" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 根据量表ID查询订单列表 -->
    <select id="selectOrdersByScaleId" parameterType="Long" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE scale_id = #{scaleId} AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 新增订单 -->
    <insert id="insertOrder" parameterType="PsyAssessmentOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_order (
            order_no, user_id, scale_id, scale_name, original_price, actual_price, discount_amount,
            coupon_id, payment_method, payment_status, order_status, pay_time, expire_time,
            refund_amount, refund_time, refund_reason, del_flag, create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{orderNo}, #{userId}, #{scaleId}, #{scaleName}, #{originalPrice}, #{actualPrice}, #{discountAmount},
            #{couponId}, #{paymentMethod}, #{paymentStatus}, #{orderStatus}, #{payTime}, #{expireTime},
            #{refundAmount}, #{refundTime}, #{refundReason}, #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate(), #{remark}
        )
    </insert>

    <!-- 修改订单 -->
    <update id="updateOrder" parameterType="PsyAssessmentOrder">
        UPDATE psy_t_order
        <set>
            <if test="orderNo != null">order_no = #{orderNo},</if>
            <if test="userId != null">user_id = #{userId},</if>
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="scaleName != null">scale_name = #{scaleName},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="actualPrice != null">actual_price = #{actualPrice},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="couponId != null">coupon_id = #{couponId},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentStatus != null">payment_status = #{paymentStatus},</if>
            <if test="orderStatus != null">order_status = #{orderStatus},</if>
            <if test="payTime != null">pay_time = #{payTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="refundReason != null">refund_reason = #{refundReason},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除订单 -->
    <update id="deleteOrderById" parameterType="Long">
        UPDATE psy_t_order SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除订单 -->
    <update id="deleteOrderByIds" parameterType="Long">
        UPDATE psy_t_order SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询用户在指定量表的有效订单 -->
    <select id="selectValidOrderByUserAndScale" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE user_id = #{userId} AND scale_id = #{scaleId} 
        AND payment_status = 1 AND order_status IN (1, 2) AND del_flag = 0
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <!-- 更新订单支付状态 -->
    <update id="updatePaymentStatus">
        UPDATE psy_t_order 
        SET payment_status = #{paymentStatus}, pay_time = #{payTime}, order_status = 1, update_time = sysdate()
        WHERE order_no = #{orderNo}
    </update>

    <!-- 完成订单 -->
    <update id="completeOrder" parameterType="String">
        UPDATE psy_t_order 
        SET order_status = 2, update_time = sysdate()
        WHERE order_no = #{orderNo}
    </update>

    <!-- 取消订单 -->
    <update id="cancelOrder" parameterType="String">
        UPDATE psy_t_order 
        SET order_status = 3, update_time = sysdate()
        WHERE order_no = #{orderNo}
    </update>

    <!-- 申请退款 -->
    <update id="applyRefund">
        UPDATE psy_t_order 
        SET order_status = 4, refund_amount = #{refundAmount}, refund_reason = #{refundReason}, update_time = sysdate()
        WHERE order_no = #{orderNo}
    </update>

    <!-- 处理退款 -->
    <update id="processRefund">
        UPDATE psy_t_order 
        SET order_status = 5, refund_time = #{refundTime}, update_time = sysdate()
        WHERE order_no = #{orderNo}
    </update>

    <!-- 查询待支付订单 -->
    <select id="selectPendingOrders" parameterType="Long" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE user_id = #{userId} AND payment_status = 0 AND order_status = 0 AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询已支付订单 -->
    <select id="selectPaidOrders" parameterType="Long" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE user_id = #{userId} AND payment_status = 1 AND order_status = 1 AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询已完成订单 -->
    <select id="selectCompletedOrders" parameterType="Long" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE user_id = #{userId} AND order_status = 2 AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询已取消订单 -->
    <select id="selectCancelledOrders" parameterType="Long" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE user_id = #{userId} AND order_status = 3 AND del_flag = 0
        ORDER BY create_time DESC
    </select>

    <!-- 查询过期订单 -->
    <select id="selectExpiredOrders" resultMap="OrderResultMap">
        SELECT * FROM psy_t_order 
        WHERE payment_status = 0 AND order_status = 0 AND expire_time &lt; NOW() AND del_flag = 0
    </select>

    <!-- 查询订单统计信息 -->
    <select id="selectOrderStats" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            COUNT(CASE WHEN payment_status = 1 THEN 1 END) as paid_count,
            COUNT(CASE WHEN order_status = 0 THEN 1 END) as pending_count,
            COUNT(CASE WHEN order_status = 2 THEN 1 END) as completed_count,
            COUNT(CASE WHEN order_status = 3 THEN 1 END) as cancelled_count,
            COUNT(CASE WHEN order_status = 4 THEN 1 END) as refund_pending_count,
            COUNT(CASE WHEN order_status = 5 THEN 1 END) as refunded_count,
            SUM(CASE WHEN payment_status = 1 THEN actual_price ELSE 0 END) as total_income,
            COUNT(CASE WHEN DATE(create_time) = CURDATE() THEN 1 END) as today_count
        FROM psy_t_order
        WHERE del_flag = 0
    </select>

    <!-- 查询用户订单统计 -->
    <select id="selectUserOrderStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            COUNT(CASE WHEN payment_status = 1 THEN 1 END) as paid_count,
            SUM(CASE WHEN payment_status = 1 THEN actual_price ELSE 0 END) as total_amount
        FROM psy_t_order
        WHERE user_id = #{userId} AND del_flag = 0
    </select>

    <!-- 查询量表订单统计 -->
    <select id="selectScaleOrderStats" parameterType="Long" resultType="java.util.Map">
        SELECT
            COUNT(1) as total_count,
            COUNT(CASE WHEN payment_status = 1 THEN 1 END) as paid_count,
            COUNT(DISTINCT user_id) as user_count,
            SUM(CASE WHEN payment_status = 1 THEN actual_price ELSE 0 END) as total_income
        FROM psy_t_order
        WHERE scale_id = #{scaleId} AND del_flag = 0
    </select>

    <!-- 查询订单收入统计 -->
    <select id="selectOrderIncomeStats" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(1) as order_count,
            COUNT(CASE WHEN payment_status = 1 THEN 1 END) as paid_count,
            SUM(CASE WHEN payment_status = 1 THEN actual_price ELSE 0 END) as income
        FROM psy_t_order
        WHERE create_time BETWEEN #{startDate} AND #{endDate} AND del_flag = 0
        GROUP BY DATE(create_time)
        ORDER BY DATE(create_time)
    </select>

    <!-- 查询热销量表 -->
    <select id="selectHotSalesScales" resultType="java.util.Map">
        SELECT 
            o.scale_id,
            o.scale_name,
            COUNT(1) as order_count,
            COUNT(CASE WHEN o.payment_status = 1 THEN 1 END) as paid_count,
            SUM(CASE WHEN o.payment_status = 1 THEN o.actual_price ELSE 0 END) as total_income
        FROM psy_t_order o
        WHERE o.del_flag = 0
        GROUP BY o.scale_id, o.scale_name
        ORDER BY paid_count DESC, total_income DESC
        LIMIT #{limit}
    </select>

    <!-- 检查订单编号唯一性 -->
    <select id="checkOrderNoUnique" parameterType="String" resultType="int">
        SELECT COUNT(1) FROM psy_t_order WHERE order_no = #{orderNo}
    </select>

    <!-- 统计订单数量 -->
    <select id="countOrders" parameterType="PsyAssessmentOrder" resultType="int">
        SELECT COUNT(1) FROM psy_t_order
        WHERE del_flag = 0
        <if test="userId != null">
            AND user_id = #{userId}
        </if>
        <if test="scaleId != null">
            AND scale_id = #{scaleId}
        </if>
        <if test="paymentStatus != null">
            AND payment_status = #{paymentStatus}
        </if>
        <if test="orderStatus != null">
            AND order_status = #{orderStatus}
        </if>
    </select>

    <!-- 查询订单趋势统计 -->
    <select id="selectOrderTrend" parameterType="Integer" resultType="java.util.Map">
        SELECT 
            DATE(create_time) as date,
            COUNT(1) as total_count,
            COUNT(CASE WHEN payment_status = 1 THEN 1 END) as paid_count,
            SUM(CASE WHEN payment_status = 1 THEN actual_price ELSE 0 END) as income
        FROM psy_t_order
        WHERE create_time >= DATE_SUB(CURDATE(), INTERVAL #{days} DAY) AND del_flag = 0
        GROUP BY DATE(create_time)
        ORDER BY DATE(create_time)
    </select>
</mapper>
