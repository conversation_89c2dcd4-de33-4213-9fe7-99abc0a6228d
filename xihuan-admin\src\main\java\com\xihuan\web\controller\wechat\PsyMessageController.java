package com.xihuan.web.controller.wechat;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyMessage;
import com.xihuan.common.core.domain.entity.PsyMessageConversation;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.system.service.wxService.IPsyMessageConversationService;
import com.xihuan.system.service.wxService.IPsyMessageService;
import com.xihuan.system.service.wxService.IPsyMessageStatusService;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 消息 控制器
 */
@RestController
@RequestMapping("/system/message")
public class PsyMessageController extends BaseController {
    
    @Autowired
    private IPsyMessageService messageService;
    
    @Autowired
    private IPsyMessageConversationService conversationService;
    
    @Autowired
    private IPsyMessageStatusService messageStatusService;
    
    /**
     * 获取会话列表
     */
    @GetMapping("/conversations")
    public AjaxResult listConversations() {
        Long userId = getUserId();
        List<PsyMessageConversation> list = conversationService.getUserConversations(userId);
        return AjaxResult.success(list);
    }
    
    /**
     * 获取咨询师会话列表
     */
    @GetMapping("/consultant/conversations")
    public AjaxResult listConsultantConversations() {
        Long userId = getUserId();
        List<PsyMessageConversation> list = conversationService.getConsultantConversations(userId);
        return AjaxResult.success(list);
    }
    
    /**
     * 管理员获取所有会话列表
     */
    @PreAuthorize("@ss.hasPermi('system:message:admin')")
    @GetMapping("/admin/conversations")
    public AjaxResult listAllConversations() {
        List<PsyMessageConversation> list = conversationService.getAllConversations();
        return AjaxResult.success(list);
    }
    
    /**
     * 创建会话
     */
    @PostMapping("/conversation/{consultantId}")
    public AjaxResult createConversation(@PathVariable Long consultantId) {
        Long userId = getUserId();
        PsyMessageConversation conversation = conversationService.createConversation(userId, consultantId);
        return AjaxResult.success(conversation);
    }
    
    /**
     * 获取会话消息列表
     */
    @GetMapping("/list/{conversationId}")
    public TableDataInfo listMessages(@PathVariable Long conversationId) {
        startPage();
        List<PsyMessage> list = messageService.getMessageList(conversationId);
        return getDataTable(list);
    }
    
    /**
     * 发送消息
     */
    @PostMapping("/send")
    public AjaxResult sendMessage(@RequestBody PsyMessage message) {
        message.setSenderId(getUserId());
        PsyMessage result = messageService.sendMessage(message);
        return AjaxResult.success(result);
    }
    
    /**
     * 管理员代替咨询师发送消息
     */
    @PreAuthorize("@ss.hasPermi('system:message:admin')")
    @PostMapping("/admin/send")
    public AjaxResult sendMessageAsConsultant(@RequestBody AdminMessageRequest request) {
        PsyMessage message = request.getMessage();
        // 保留原始咨询师ID为发送者
        Long consultantId = request.getConsultantId();
        message.setSenderId(consultantId);
        PsyMessage result = messageService.sendMessageAsAdmin(message, getUserId());
        return AjaxResult.success(result);
    }
    
    /**
     * 撤回消息
     */
    @PutMapping("/withdraw/{messageId}")
    public AjaxResult withdrawMessage(@PathVariable Long messageId) {
        return messageService.withdrawMessage(messageId, getUserId());
    }
    
    /**
     * 标记消息为已读
     */
    @PutMapping("/read/{messageId}")
    public AjaxResult markAsRead(@PathVariable Long messageId) {
        boolean result = messageService.markAsRead(messageId, getUserId());
        return toAjax(result);
    }
    
    /**
     * 标记会话所有消息为已读
     */
    @PutMapping("/read/all/{conversationId}")
    public AjaxResult markAllAsRead(@PathVariable Long conversationId, @RequestBody MarkReadRequest request) {
        boolean result = conversationService.resetUnreadCount(conversationId,request.getIsUser());
        return toAjax(result);
    }

    // 参数封装对象
    @Data  // Lombok 自动生成 getter/setter
    public static class MarkReadRequest {
        private Boolean isUser;
    }
    
    // 管理员发送消息请求对象
    @Data
    public static class AdminMessageRequest {
        private PsyMessage message;
        private Long consultantId;
    }
    
    /**
     * 获取未读消息数量
     */
    @GetMapping("/unread/count")
    public AjaxResult getUnreadCount() {
        int count = messageStatusService.getUnreadCount(getUserId());
        return AjaxResult.success(count);
    }
    
    /**
     * 获取会话详情
     */
    @GetMapping("/conversation/{conversationId}")
    public AjaxResult getConversation(@PathVariable Long conversationId) {
        PsyMessageConversation conversation = conversationService.getConversation(conversationId);
        return AjaxResult.success(conversation);
    }
    
    /**
     * 管理员获取会话详情
     */
    @PreAuthorize("@ss.hasPermi('system:message:admin')")
    @GetMapping("/admin/conversation/{conversationId}")
    public AjaxResult getConversationAsAdmin(@PathVariable Long conversationId) {
        PsyMessageConversation conversation = conversationService.getConversation(conversationId);
        return AjaxResult.success(conversation);
    }
} 