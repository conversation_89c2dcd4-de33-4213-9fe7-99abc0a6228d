package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAnswerRecord;

import java.util.List;
import java.util.Map;

/**
 * 答题记录Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAnswerRecordService {
    
    /**
     * 查询答题记录列表
     * 
     * @param answer 答题记录信息
     * @return 答题记录集合
     */
    List<PsyTAnswerRecord> selectAnswerList(PsyTAnswerRecord answer);

    /**
     * 根据ID查询答题记录
     * 
     * @param id 答题记录ID
     * @return 答题记录信息
     */
    PsyTAnswerRecord selectAnswerById(Long id);

    /**
     * 根据测评记录ID查询答题记录列表
     * 
     * @param recordId 测评记录ID
     * @return 答题记录集合
     */
    List<PsyTAnswerRecord> selectAnswersByRecordId(Long recordId);

    /**
     * 根据题目ID查询答题记录列表
     * 
     * @param questionId 题目ID
     * @return 答题记录集合
     */
    List<PsyTAnswerRecord> selectAnswersByQuestionId(Long questionId);

    /**
     * 查询用户答题记录
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @return 答题记录信息
     */
    PsyTAnswerRecord selectAnswerByRecordAndQuestion(Long recordId, Long questionId);

    /**
     * 新增答题记录
     * 
     * @param answer 答题记录信息
     * @return 结果
     */
    int insertAnswer(PsyTAnswerRecord answer);

    /**
     * 批量新增答题记录
     * 
     * @param answers 答题记录列表
     * @return 结果
     */
    int batchInsertAnswers(List<PsyTAnswerRecord> answers);

    /**
     * 修改答题记录
     * 
     * @param answer 答题记录信息
     * @return 结果
     */
    int updateAnswer(PsyTAnswerRecord answer);

    /**
     * 删除答题记录
     * 
     * @param ids 需要删除的答题记录ID
     * @return 结果
     */
    int deleteAnswerByIds(Long[] ids);

    /**
     * 根据测评记录ID删除答题记录
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    int deleteAnswersByRecordId(Long recordId);

    /**
     * 保存用户答案
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @param answerContent 答案内容
     * @param responseTime 答题耗时
     * @return 结果
     */
    int saveUserAnswer(Long recordId, Long questionId, String answerContent, Integer responseTime);

    /**
     * 批量保存用户答案
     * 
     * @param answers 答题记录列表
     * @return 结果
     */
    int batchSaveUserAnswers(List<PsyTAnswerRecord> answers);

    /**
     * 计算答题得分
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @return 得分
     */
    Integer calculateAnswerScore(Long recordId, Long questionId);

    /**
     * 统计测评记录答题数量
     * 
     * @param recordId 测评记录ID
     * @return 数量
     */
    int countAnswersByRecordId(Long recordId);

    /**
     * 统计题目答题数量
     * 
     * @param questionId 题目ID
     * @return 数量
     */
    int countAnswersByQuestionId(Long questionId);

    /**
     * 查询答题统计信息
     * 
     * @param recordId 测评记录ID
     * @return 统计信息
     */
    Map<String, Object> selectAnswerStats(Long recordId);

    /**
     * 查询题目答题分布
     * 
     * @param questionId 题目ID
     * @return 分布信息
     */
    List<Map<String, Object>> selectQuestionAnswerDistribution(Long questionId);

    /**
     * 查询用户答题进度
     * 
     * @param recordId 测评记录ID
     * @return 进度信息
     */
    Map<String, Object> selectAnswerProgress(Long recordId);

    /**
     * 查询答题时长统计
     * 
     * @param recordId 测评记录ID
     * @return 时长统计
     */
    Map<String, Object> selectAnswerTimeStats(Long recordId);

    /**
     * 查询题目平均答题时间
     * 
     * @param questionId 题目ID
     * @return 平均时间
     */
    Integer selectQuestionAvgResponseTime(Long questionId);

    /**
     * 查询用户答题历史
     * 
     * @param userId 用户ID
     * @param questionId 题目ID
     * @return 答题历史
     */
    List<PsyTAnswerRecord> selectUserAnswerHistory(Long userId, Long questionId);

    /**
     * 计算测评记录总分
     * 
     * @param recordId 测评记录ID
     * @return 总分
     */
    Integer calculateTotalScore(Long recordId);

    /**
     * 计算分量表得分
     * 
     * @param recordId 测评记录ID
     * @param subscaleId 分量表ID
     * @return 分量表得分
     */
    Integer calculateSubscaleScore(Long recordId, Long subscaleId);

    /**
     * 查询答题正确率统计
     * 
     * @param recordId 测评记录ID
     * @return 正确率统计
     */
    Map<String, Object> selectAnswerAccuracyStats(Long recordId);

    /**
     * 查询答题模式分析
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 模式分析
     */
    Map<String, Object> selectAnswerPatternAnalysis(Long userId, Long scaleId);

    /**
     * 验证答案有效性
     * 
     * @param questionId 题目ID
     * @param answerContent 答案内容
     * @return 验证结果
     */
    boolean validateAnswer(Long questionId, String answerContent);

    /**
     * 获取下一题
     * 
     * @param recordId 测评记录ID
     * @param currentQuestionNo 当前题目序号
     * @return 下一题信息
     */
    Map<String, Object> getNextQuestion(Long recordId, Integer currentQuestionNo);

    /**
     * 获取上一题
     * 
     * @param recordId 测评记录ID
     * @param currentQuestionNo 当前题目序号
     * @return 上一题信息
     */
    Map<String, Object> getPreviousQuestion(Long recordId, Integer currentQuestionNo);

    /**
     * 检查是否可以提交测评
     * 
     * @param recordId 测评记录ID
     * @return 检查结果
     */
    Map<String, Object> checkCanSubmitAssessment(Long recordId);

    /**
     * 自动保存答题进度
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    int autoSaveProgress(Long recordId);
}
