package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * 收藏分组关系实体 psy_favorite_group_rel
 */
@Data
public class PsyFavoriteGroupRel {
    private static final long serialVersionUID = 1L;

    /** 收藏ID */
    @Excel(name = "收藏ID")
    private Long favoriteId;

    /** 分组ID */
    @Excel(name = "分组ID")
    private Long groupId;

    /** 在分组中的排序 */
    @Excel(name = "排序")
    private Integer sort;

    /** 创建时间 */
    @Excel(name = "创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
}
