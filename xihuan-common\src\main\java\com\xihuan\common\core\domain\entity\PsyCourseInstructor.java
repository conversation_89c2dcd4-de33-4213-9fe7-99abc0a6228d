package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 讲师信息表对象 psy_course_instructor
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyCourseInstructor extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 讲师ID */
    @Excel(name = "讲师ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 讲师姓名 */
    @Excel(name = "讲师姓名")
    private String name;

    /** 讲师头衔 */
    @Excel(name = "讲师头衔")
    private String title;

    /** 讲师资质 */
    @Excel(name = "讲师资质")
    private String qualifications;

    /** 删除标志（0:正常 1:删除） */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    /** 讲师头像图片资源列表 */
    private List<PsyCourseImageResource> avatarImages;

    /** 讲师授课的课程列表 */
    private List<PsyCourse> courses;
}
