package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * 心理测评评价表对象 psy_t_review
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentReview extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 评价ID */
    @Excel(name = "评价ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 测评记录ID */
    @Excel(name = "测评记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long recordId;

    /** 评分(1-5分) */
    @Excel(name = "评分")
    @NotNull(message = "评分不能为空")
    @Min(value = 1, message = "评分最低为1分")
    @Max(value = 5, message = "评分最高为5分")
    private Integer rating;

    /** 评价内容 */
    @Excel(name = "评价内容")
    @Size(max = 1000, message = "评价内容不能超过1000个字符")
    private String content;

    /** 是否匿名(0=否 1=是) */
    @Excel(name = "是否匿名", readConverterExp = "0=否,1=是")
    private Integer isAnonymous;

    /** 审核状态(0=待审核 1=已通过 2=已拒绝) */
    @Excel(name = "审核状态", readConverterExp = "0=待审核,1=已通过,2=已拒绝")
    private Integer status;

    /** 审核人 */
    @Excel(name = "审核人")
    @Size(max = 64, message = "审核人不能超过64个字符")
    private String auditBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date auditTime;

    /** 审核备注 */
    @Excel(name = "审核备注")
    @Size(max = 200, message = "审核备注不能超过200个字符")
    private String auditRemark;

    /** 点赞数 */
    @Excel(name = "点赞数")
    @Min(value = 0, message = "点赞数不能为负数")
    private Integer likeCount;

    /** 回复数 */
    @Excel(name = "回复数")
    @Min(value = 0, message = "回复数不能为负数")
    private Integer replyCount;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 用户信息 */
    private SysUser user;

    /** 量表信息 */
    private PsyAssessmentScale scale;

    /** 测评记录信息 */
    private PsyAssessmentRecord record;

    // 扩展字段
    /** 用户昵称 */
    private String nickName;

    /** 用户头像 */
    private String avatar;

    /** 量表名称 */
    private String scaleName;

    /** 是否已点赞 */
    private Boolean liked;

    /** 评价时间格式化 */
    private String createTimeFormatted;

    // 常量定义
    /** 匿名：否 */
    public static final Integer ANONYMOUS_NO = 0;
    
    /** 匿名：是 */
    public static final Integer ANONYMOUS_YES = 1;

    /** 审核状态：待审核 */
    public static final Integer STATUS_PENDING = 0;
    
    /** 审核状态：已通过 */
    public static final Integer STATUS_APPROVED = 1;
    
    /** 审核状态：已拒绝 */
    public static final Integer STATUS_REJECTED = 2;

    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 获取审核状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "待审核";
            case 1: return "已通过";
            case 2: return "已拒绝";
            default: return "未知";
        }
    }

    /**
     * 获取匿名描述
     */
    public String getAnonymousDesc() {
        if (isAnonymous == null) return "";
        return isAnonymous == 1 ? "是" : "否";
    }

    /**
     * 获取评分星级显示
     */
    public String getRatingStars() {
        if (rating == null) return "";
        StringBuilder stars = new StringBuilder();
        for (int i = 1; i <= 5; i++) {
            if (i <= rating) {
                stars.append("★");
            } else {
                stars.append("☆");
            }
        }
        return stars.toString();
    }

    /**
     * 是否匿名
     */
    public boolean isAnonymousReview() {
        return ANONYMOUS_YES.equals(isAnonymous);
    }

    /**
     * 是否待审核
     */
    public boolean isPending() {
        return STATUS_PENDING.equals(status);
    }

    /**
     * 是否已通过
     */
    public boolean isApproved() {
        return STATUS_APPROVED.equals(status);
    }

    /**
     * 是否已拒绝
     */
    public boolean isRejected() {
        return STATUS_REJECTED.equals(status);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 获取显示的用户名
     */
    public String getDisplayName() {
        if (isAnonymousReview()) {
            return "匿名用户";
        }
        if (nickName != null && !nickName.trim().isEmpty()) {
            return nickName;
        }
        if (user != null && user.getNickName() != null) {
            return user.getNickName();
        }
        return "用户" + userId;
    }
}
