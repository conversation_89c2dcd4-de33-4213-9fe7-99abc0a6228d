package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentScale;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 心理测评量表Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentScaleMapper {
    
    /**
     * 查询量表列表
     * 
     * @param scale 量表信息
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectScaleList(PsyAssessmentScale scale);

    /**
     * 根据ID查询量表
     * 
     * @param id 量表ID
     * @return 量表信息
     */
    PsyAssessmentScale selectScaleById(Long id);

    /**
     * 查询量表详情（包含题目、分类等信息）
     * 
     * @param id 量表ID
     * @return 量表详情
     */
    PsyAssessmentScale selectScaleWithDetails(Long id);

    /**
     * 根据编码查询量表
     * 
     * @param scaleCode 量表编码
     * @return 量表信息
     */
    PsyAssessmentScale selectScaleByCode(String scaleCode);

    /**
     * 新增量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    int insertScale(PsyAssessmentScale scale);

    /**
     * 修改量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    int updateScale(PsyAssessmentScale scale);

    /**
     * 删除量表
     * 
     * @param id 量表ID
     * @return 结果
     */
    int deleteScaleById(Long id);

    /**
     * 批量删除量表
     * 
     * @param ids 需要删除的量表ID
     * @return 结果
     */
    int deleteScaleByIds(Long[] ids);

    /**
     * 根据分类ID查询量表列表
     * 
     * @param categoryId 分类ID
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectScalesByCategoryId(Long categoryId);

    /**
     * 查询用户已购买的量表列表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectPurchasedScalesByUserId(Long userId);

    /**
     * 查询热门量表列表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectHotScales(@Param("limit") Integer limit);

    /**
     * 查询推荐量表列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectRecommendScales(@Param("userId") Long userId, @Param("limit") Integer limit);

    /**
     * 查询免费量表列表
     * 
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectFreeScales();

    /**
     * 更新量表查看次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    int updateViewCount(Long id);

    /**
     * 更新量表测试次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    int updateTestCount(Long id);

    /**
     * 更新量表搜索次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    int updateSearchCount(Long id);

    /**
     * 更新量表评分统计
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int updateRatingStats(Long scaleId);

    /**
     * 检查量表编码唯一性
     * 
     * @param scaleCode 量表编码
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkScaleCodeUnique(@Param("scaleCode") String scaleCode, @Param("excludeId") Long excludeId);

    /**
     * 检查量表是否被使用
     * 
     * @param id 量表ID
     * @return 数量
     */
    int checkScaleInUse(Long id);

    /**
     * 搜索量表
     * 
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param difficultyLevel 难度等级
     * @param isFree 是否免费
     * @return 量表集合
     */
    List<PsyAssessmentScale> searchScales(@Param("keyword") String keyword, 
                                         @Param("categoryId") Long categoryId,
                                         @Param("difficultyLevel") Integer difficultyLevel,
                                         @Param("isFree") Integer isFree);

    /**
     * 统计量表数量
     * 
     * @param scale 查询条件
     * @return 数量
     */
    int countScales(PsyAssessmentScale scale);

    /**
     * 查询量表统计信息
     * 
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectScaleStats();

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectUserTestStats(Long userId);

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectScaleTestStats(Long scaleId);

    /**
     * 查询今日热门量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectTodayHotScales(@Param("limit") Integer limit);

    /**
     * 查询最新量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectLatestScales(@Param("limit") Integer limit);

    /**
     * 查询用户收藏的量表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectFavoriteScalesByUserId(Long userId);

    /**
     * 查询相似量表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 量表集合
     */
    List<PsyAssessmentScale> selectSimilarScales(@Param("scaleId") Long scaleId, @Param("limit") Integer limit);
}
