<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyProductMapper">
    <!-- PsyProductMapper.xml -->
    <!-- 基础产品字段映射 -->
    <resultMap id="BaseResultMap" type="PsyProduct">
        <id column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="product_image" property="productImage"/>
        <result column="service_method" property="serviceMethod"/>
        <result column="service_guarantee" property="serviceGuarantee"/>
        <result column="service_direction" property="serviceDirection"/>
        <result column="service_direction_type" property="serviceDirectionType"/>
        <result column="consultant_grade" property="consultantGrade"/>
        <result column="service_duration" property="serviceDuration"/>
        <result column="graphic_details" property="graphicDetails"/>
        <result column="supplement_info" property="supplementInfo"/>
        <result column="original_price" property="originalPrice"/>
        <result column="discount_price" property="discountPrice"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="applicable_stores" property="applicableStores"/>
        <result column="validity_period" property="validityPeriod"/>
        <result column="unavailable_dates" property="unavailableDates"/>
        <result column="need_appointment" property="needAppointment"/>
        <result column="single_purchase_limit" property="singlePurchaseLimit"/>
        <result column="age_range" property="ageRange"/>
        <result column="disable_flag" property="disableFlag"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <collection property="categories" ofType="PsyCategory">
            <id column="category_id" property="categoryId"/>
            <result column="category_name" property="categoryName"/>
            <result column="parent_id" property="parentId"/>
            <result column="order_num" property="orderNum"/>
            <result column="status" property="status"/>
        </collection>
    </resultMap>

    <!-- 服务项目映射 -->
    <resultMap id="ServiceItemResultMap" type="PsyServiceItem">
        <id column="item_id" property="itemId"/>
        <result column="item_name" property="itemName"/>
        <result column="category" property="category"/>
        <result column="price" property="price"/>
        <result column="quantity" property="quantity"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="del_flag" property="delFlag"/>
        <result column="product_id" property="productId"/>
        <collection property="contents" 
                  ofType="PsyServiceContent" 
                  resultMap="ServiceContentResultMap"
                  notNullColumn="content_id"/>
    </resultMap>

    <!-- 服务内容映射 -->
    <resultMap id="ServiceContentResultMap" type="PsyServiceContent">
        <id column="content_id" property="contentId"/>
        <result column="content" property="content"/>
        <result column="duration" property="duration"/>
        <result column="del_flag" property="delFlag"/>
    </resultMap>
    <!-- 完整三级关联映射 -->
    <resultMap id="ProductWithDetailsMap" type="PsyProduct" extends="BaseResultMap">
        <collection property="serviceItems"
                  ofType="PsyServiceItem"
                  resultMap="ServiceItemResultMap"/>
    </resultMap>

    <resultMap id="ServiceItemWithContentsMap" type="PsyServiceItem" extends="ServiceItemResultMap">
        <collection property="contents" ofType="PsyServiceContent" resultMap="ServiceContentResultMap"/>
    </resultMap>

    <select id="selectProductList" parameterType="PsyProduct" resultMap="BaseResultMap">
        SELECT DISTINCT
            p.product_id,
            p.product_name,
            p.product_image,
            p.service_direction,
            p.service_direction_type,
            p.service_method,
            p.consultant_grade,
            p.service_duration,
            p.original_price,
            p.discount_price,
            p.validity_period,
            p.disable_flag,
            p.create_time,
            p.graphic_details,
            c.category_id,
            c.category_name,
            c.parent_id,
            c.order_num,
            c.status
        FROM psy_product p
        LEFT JOIN psy_product_category pc ON p.product_id = pc.product_id
        LEFT JOIN psy_category c ON pc.category_id = c.category_id
        <where>
            p.del_flag = '0'
            <if test="productName != null and productName != ''">
                AND p.product_name LIKE CONCAT('%', #{productName}, '%')
            </if>
            <if test="serviceMethod != null and serviceMethod != ''">
                AND p.service_method = #{serviceMethod}
            </if>
            <if test="params.beginPrice != null and params.endPrice != null">
                AND p.original_price BETWEEN #{params.beginPrice} AND #{params.endPrice}
            </if>
            <if test="disableFlag != null and disableFlag != ''">
                AND p.disable_flag = #{disableFlag}
            </if>
            <if test="categoryId != null">
                AND pc.category_id = #{categoryId}
            </if>
        </where>
        ORDER BY p.product_id ASC
    </select>

    <!-- 三级关联查询SQL -->
    <select id="selectProductWithDetails" resultMap="ProductWithDetailsMap">
        SELECT
            p.*,
            i.item_id,
            i.item_name,
            i.category,
            i.price,
            i.quantity,
            i.sort_order AS item_sort_order,
            i.del_flag AS item_del_flag,
            c.content_id,
            c.content,
            c.duration,
            c.del_flag AS content_del_flag,
            cat.category_id,
            cat.category_name,
            cat.parent_id,
            cat.order_num,
            cat.status
        FROM psy_product p
        LEFT JOIN psy_product_service ps ON p.product_id = ps.product_id
        LEFT JOIN psy_service_item i ON ps.item_id = i.item_id AND (i.del_flag = '0' OR i.del_flag IS NULL)
        LEFT JOIN psy_service_item_relation r ON i.item_id = r.item_id
        LEFT JOIN psy_service_content c ON r.content_id = c.content_id AND (c.del_flag = '0' OR c.del_flag IS NULL)
        LEFT JOIN psy_product_category pc ON p.product_id = pc.product_id
        LEFT JOIN psy_category cat ON pc.category_id = cat.category_id
        WHERE p.product_id = #{productId}
        AND p.del_flag = '0'
        ORDER BY i.sort_order ASC
    </select>

    <insert id="insertProduct" useGeneratedKeys="true" keyProperty="productId">
        INSERT INTO psy_product (product_name, product_image, service_method,
                                 service_guarantee, service_direction, service_direction_type,
                                 consultant_grade, service_duration, graphic_details,
                                 supplement_info, original_price, discount_price,
                                 discount_rate, applicable_stores, validity_period,
                                 unavailable_dates, need_appointment, single_purchase_limit,
                                 age_range, disable_flag, del_flag,
                                 create_by, create_time, remark)
        VALUES (#{productName}, #{productImage}, #{serviceMethod},
                #{serviceGuarantee}, #{serviceDirection}, #{serviceDirectionType},
                #{consultantGrade}, #{serviceDuration}, #{graphicDetails},
                #{supplementInfo}, #{originalPrice}, #{discountPrice},
                #{discountRate}, #{applicableStores}, #{validityPeriod},
                #{unavailableDates}, #{needAppointment}, #{singlePurchaseLimit},
                #{ageRange}, #{disableFlag}, #{delFlag},
                #{createBy}, #{createTime}, #{remark})
    </insert>

    <!-- 批量删除关联关系 -->
    <delete id="deleteByProductIds">
        DELETE FROM psy_product_service
        WHERE product_id IN
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>

    <!-- 批量插入关联关系 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO psy_product_service (product_id, item_id) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.productId}, #{item.itemId})
        </foreach>
    </insert>

    <!-- 批量插入产品分类关联 -->
    <insert id="batchInsertProductCategories">
        INSERT INTO psy_product_category (product_id, category_id) VALUES
        <foreach collection="categoryIds" item="categoryId" separator=",">
            (#{productId}, #{categoryId})
        </foreach>
    </insert>

    <!-- 删除产品分类关联 -->
    <delete id="deleteProductCategories">
        DELETE FROM psy_product_category WHERE product_id = #{productId}
    </delete>

    <!-- 批量删除产品分类关联 -->
    <delete id="deleteProductCategoriesByIds">
        DELETE FROM psy_product_category
        WHERE product_id IN
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </delete>

    <!-- 更新产品 -->
    <update id="updateProduct" parameterType="PsyProduct">
        UPDATE psy_product
        <set>
            <if test="productName != null">product_name = #{productName},</if>
            <if test="productImage != null">product_image = #{productImage},</if>
            <if test="serviceMethod != null">service_method = #{serviceMethod},</if>
            <if test="serviceGuarantee != null">service_guarantee = #{serviceGuarantee},</if>
            <if test="serviceDirection != null">service_direction = #{serviceDirection},</if>
            <if test="serviceDirectionType != null">service_direction_type = #{serviceDirectionType},</if>
            <if test="consultantGrade != null">consultant_grade = #{consultantGrade},</if>
            <if test="serviceDuration != null">service_duration = #{serviceDuration},</if>
            <if test="graphicDetails != null">graphic_details = #{graphicDetails},</if>
            <if test="supplementInfo != null">supplement_info = #{supplementInfo},</if>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="discountPrice != null">discount_price = #{discountPrice},</if>
            <if test="discountRate != null">discount_rate = #{discountRate},</if>
            <if test="applicableStores != null">applicable_stores = #{applicableStores},</if>
            <if test="validityPeriod != null">validity_period = #{validityPeriod},</if>
            <if test="unavailableDates != null">unavailable_dates = #{unavailableDates},</if>
            <if test="needAppointment != null">need_appointment = #{needAppointment},</if>
            <if test="singlePurchaseLimit != null">single_purchase_limit = #{singlePurchaseLimit},</if>
            <if test="ageRange != null">age_range = #{ageRange},</if>
            <if test="disableFlag != null">disable_flag = #{disableFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        WHERE product_id = #{productId}
    </update>

    <!-- 批量删除产品 -->
    <update id="deleteProductByIds">
        UPDATE psy_product SET del_flag = '1'
        WHERE product_id IN
        <foreach item="productId" collection="array" open="(" separator="," close=")">
            #{productId}
        </foreach>
    </update>
</mapper>