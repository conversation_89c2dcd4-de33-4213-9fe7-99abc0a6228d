<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyStoresMapper">
    
    <resultMap type="PsyStores" id="PsyStoresResult">
        <id     property="id"       column="id"        />
        <result property="name"     column="name"      />
        <result property="branchName" column="branch_name"/>
        <result property="mapName"  column="map_name"   />
        <result property="mapAddress" column="map_address"/>
        <result property="address"  column="address"   />
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"  />
        <result property="remark"   column="remark"    />
        <result property="status"   column="status"    />
        <result property="createBy" column="create_by"  />
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"  />
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPsyStoresVo">
        select id, name, branch_name, map_name, map_address, address, longitude, latitude, remark, status, 
        create_by, create_time, update_by, update_time
        from psy_stores
    </sql>

    <select id="selectPsyStoresList" parameterType="PsyStores" resultMap="PsyStoresResult">
        <include refid="selectPsyStoresVo"/>
        <where>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="branchName != null  and branchName != ''"> and branch_name like concat('%', #{branchName}, '%')</if>
            <if test="mapName != null  and mapName != ''"> and map_name like concat('%', #{mapName}, '%')</if>
            <if test="address != null  and address != ''"> and address like concat('%', #{address}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectPsyStoresById" parameterType="Long" resultMap="PsyStoresResult">
        <include refid="selectPsyStoresVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertPsyStores" parameterType="PsyStores" useGeneratedKeys="true" keyProperty="id">
        insert into psy_stores
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="branchName != null">branch_name,</if>
            <if test="mapName != null">map_name,</if>
            <if test="mapAddress != null">map_address,</if>
            <if test="address != null">address,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="remark != null">remark,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="branchName != null">#{branchName},</if>
            <if test="mapName != null">#{mapName},</if>
            <if test="mapAddress != null">#{mapAddress},</if>
            <if test="address != null">#{address},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="remark != null">#{remark},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updatePsyStores" parameterType="PsyStores">
        update psy_stores
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="branchName != null">branch_name = #{branchName},</if>
            <if test="mapName != null">map_name = #{mapName},</if>
            <if test="mapAddress != null">map_address = #{mapAddress},</if>
            <if test="address != null">address = #{address},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePsyStoresById" parameterType="Long">
        delete from psy_stores where id = #{id}
    </delete>

    <delete id="deletePsyStoresByIds" parameterType="String">
        delete from psy_stores where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper> 