# 心理测评系统数据库设计文档

## 概述

本文档描述了新心理测评系统（psy_t_*表）的数据库设计，包括表结构、字段说明、索引设计和关系说明。

## 数据库表概览

| 表名 | 说明 | 主要功能 |
|------|------|----------|
| psy_t_scale | 量表基础信息 | 存储心理测评量表的基本信息 |
| psy_t_question | 题目信息 | 存储量表中的题目内容 |
| psy_t_question_option | 题目选项 | 存储题目的选项信息 |
| psy_t_subscale | 分量表定义 | 存储量表的分量表信息 |
| psy_t_scoring_rule | 计分规则 | 存储量表的计分和解释规则 |
| psy_t_composite_question | 复合题特殊计分 | 存储复合题的特殊计分规则 |
| psy_t_function_impairment | 功能损害评估 | 存储功能损害评估信息 |
| psy_t_assessment_record | 测评记录 | 存储用户的测评记录 |
| psy_t_answer_record | 答题记录 | 存储用户的答题详情 |
| psy_t_assessment_order | 测评订单 | 存储测评付费订单信息 |
| psy_t_enterprise | 企业信息 | 存储企业客户信息 |
| psy_t_enterprise_department | 企业部门 | 存储企业的部门结构 |
| psy_t_enterprise_assessment_plan | 企业测评计划 | 存储企业的测评计划 |
| psy_t_enterprise_assessment_participant | 企业测评参与记录 | 存储企业测评的参与情况 |

## 核心表结构设计

### 1. 量表基础信息表 (psy_t_scale)

```sql
CREATE TABLE psy_t_scale (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '量表编码',
    name VARCHAR(200) NOT NULL COMMENT '量表名称',
    alias VARCHAR(50) COMMENT '量表简称/缩写',
    description TEXT COMMENT '量表描述',
    instruction TEXT COMMENT '测评说明',
    author VARCHAR(100) COMMENT '量表作者',
    version VARCHAR(20) COMMENT '版本号',
    scoring_type VARCHAR(20) NOT NULL DEFAULT 'sum' COMMENT '计分类型：sum求和,avg平均,weighted加权,custom自定义',
    pay_mode TINYINT NOT NULL DEFAULT 0 COMMENT '付费模式：0免费,1付费,2部分付费',
    pay_phase TINYINT NOT NULL DEFAULT 0 COMMENT '付费阶段：0测评前,1结果后',
    original_price DECIMAL(10,2) COMMENT '原价',
    current_price DECIMAL(10,2) COMMENT '现价',
    free_report_level TINYINT DEFAULT 1 COMMENT '免费报告层级',
    paid_report_level TINYINT DEFAULT 3 COMMENT '付费报告层级',
    time_limit INT COMMENT '时间限制(秒)',
    question_count INT DEFAULT 0 COMMENT '题目数量',
    cover_image VARCHAR(500) COMMENT '封面图片',
    tags VARCHAR(500) COMMENT '标签',
    search_keywords VARCHAR(500) COMMENT '搜索关键词',
    search_count INT DEFAULT 0 COMMENT '搜索次数',
    view_count INT DEFAULT 0 COMMENT '查看次数',
    test_count INT DEFAULT 0 COMMENT '测评次数',
    rating_avg DECIMAL(3,2) COMMENT '平均评分',
    rating_count INT DEFAULT 0 COMMENT '评分人数',
    sort INT DEFAULT 0 COMMENT '排序',
    status TINYINT NOT NULL DEFAULT 0 COMMENT '状态：0禁用,1启用',
    del_flag CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0正常,2删除',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_code (code),
    INDEX idx_status (status),
    INDEX idx_pay_mode (pay_mode),
    INDEX idx_sort (sort),
    INDEX idx_create_time (create_time)
) COMMENT='量表基础信息表';
```

### 2. 题目信息表 (psy_t_question)

```sql
CREATE TABLE psy_t_question (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    scale_id BIGINT NOT NULL COMMENT '量表ID',
    question_no INT NOT NULL COMMENT '题目序号',
    content TEXT NOT NULL COMMENT '题目内容',
    type VARCHAR(20) NOT NULL DEFAULT 'single_choice' COMMENT '题目类型：single_choice单选,multiple_choice多选,text文本,scale量表,matrix矩阵',
    is_required TINYINT NOT NULL DEFAULT 1 COMMENT '是否必答：0否,1是',
    is_reverse TINYINT NOT NULL DEFAULT 0 COMMENT '是否反向计分：0否,1是',
    is_composite TINYINT NOT NULL DEFAULT 0 COMMENT '是否复合题：0否,1是',
    subscale_ref VARCHAR(100) COMMENT '分量表关联标识',
    weight DECIMAL(5,2) DEFAULT 1.00 COMMENT '权重',
    time_limit INT COMMENT '答题时间限制(秒)',
    sort INT DEFAULT 0 COMMENT '排序',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0禁用,1启用',
    del_flag CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0正常,2删除',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_scale_id (scale_id),
    INDEX idx_question_no (scale_id, question_no),
    INDEX idx_subscale_ref (scale_id, subscale_ref),
    INDEX idx_sort (sort),
    FOREIGN KEY (scale_id) REFERENCES psy_t_scale(id) ON DELETE CASCADE
) COMMENT='题目信息表';
```

### 3. 测评记录表 (psy_t_assessment_record)

```sql
CREATE TABLE psy_t_assessment_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    scale_id BIGINT NOT NULL COMMENT '量表ID',
    user_id BIGINT NOT NULL COMMENT '用户ID',
    session_id VARCHAR(64) NOT NULL UNIQUE COMMENT '会话ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    completion_time DATETIME COMMENT '完成时间',
    total_score DECIMAL(10,2) COMMENT '总分',
    result_level VARCHAR(50) COMMENT '结果等级',
    result_description TEXT COMMENT '结果描述',
    current_question_no INT DEFAULT 1 COMMENT '当前题目序号',
    answered_count INT DEFAULT 0 COMMENT '已答题数',
    total_questions INT DEFAULT 0 COMMENT '总题数',
    progress DECIMAL(5,2) DEFAULT 0.00 COMMENT '进度百分比',
    duration INT COMMENT '测评时长(秒)',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1进行中,2已完成,3已放弃',
    source VARCHAR(20) DEFAULT 'personal' COMMENT '测评来源：personal个人,enterprise企业',
    report_level TINYINT DEFAULT 1 COMMENT '报告层级',
    del_flag CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0正常,2删除',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_scale_id (scale_id),
    INDEX idx_user_id (user_id),
    INDEX idx_session_id (session_id),
    INDEX idx_status (status),
    INDEX idx_source (source),
    INDEX idx_create_time (create_time),
    FOREIGN KEY (scale_id) REFERENCES psy_t_scale(id),
    FOREIGN KEY (user_id) REFERENCES sys_user(user_id)
) COMMENT='测评记录表';
```

### 4. 答题记录表 (psy_t_answer_record)

```sql
CREATE TABLE psy_t_answer_record (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    record_id BIGINT NOT NULL COMMENT '测评记录ID',
    question_id BIGINT NOT NULL COMMENT '题目ID',
    answer_content TEXT COMMENT '答案内容',
    score DECIMAL(10,2) COMMENT '得分',
    response_time INT COMMENT '答题耗时(秒)',
    is_marked TINYINT DEFAULT 0 COMMENT '是否标记：0否,1是',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_record_id (record_id),
    INDEX idx_question_id (question_id),
    INDEX idx_create_time (create_time),
    UNIQUE KEY uk_record_question (record_id, question_id),
    FOREIGN KEY (record_id) REFERENCES psy_t_assessment_record(id) ON DELETE CASCADE,
    FOREIGN KEY (question_id) REFERENCES psy_t_question(id)
) COMMENT='答题记录表';
```

### 5. 企业信息表 (psy_t_enterprise)

```sql
CREATE TABLE psy_t_enterprise (
    id BIGINT PRIMARY KEY AUTO_INCREMENT COMMENT '主键ID',
    enterprise_code VARCHAR(50) NOT NULL UNIQUE COMMENT '企业编码',
    enterprise_name VARCHAR(200) NOT NULL COMMENT '企业名称',
    enterprise_type TINYINT COMMENT '企业类型：1国企,2民企,3外企,4事业单位,5政府机关',
    industry VARCHAR(100) COMMENT '所属行业',
    scale VARCHAR(20) COMMENT '企业规模：small小型,medium中型,large大型',
    employee_count INT COMMENT '员工数量',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    contact_email VARCHAR(100) COMMENT '联系邮箱',
    address VARCHAR(500) COMMENT '企业地址',
    province VARCHAR(50) COMMENT '省份',
    city VARCHAR(50) COMMENT '城市',
    district VARCHAR(50) COMMENT '区县',
    business_license VARCHAR(100) COMMENT '营业执照号',
    legal_representative VARCHAR(50) COMMENT '法定代表人',
    registered_capital DECIMAL(15,2) COMMENT '注册资本',
    establishment_date DATE COMMENT '成立日期',
    service_package VARCHAR(50) COMMENT '服务套餐',
    contract_start_date DATE COMMENT '合同开始日期',
    contract_end_date DATE COMMENT '合同结束日期',
    assessment_quota INT DEFAULT 0 COMMENT '测评额度',
    used_assessment_count INT DEFAULT 0 COMMENT '已使用测评次数',
    remaining_assessment_count INT DEFAULT 0 COMMENT '剩余测评次数',
    contract_amount DECIMAL(15,2) COMMENT '合同金额',
    paid_amount DECIMAL(15,2) COMMENT '已付金额',
    unpaid_amount DECIMAL(15,2) COMMENT '未付金额',
    last_payment_date DATE COMMENT '最后付款日期',
    next_payment_date DATE COMMENT '下次付款日期',
    certification_status TINYINT DEFAULT 0 COMMENT '认证状态：0未认证,1已认证',
    certification_date DATE COMMENT '认证日期',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：0禁用,1启用',
    del_flag CHAR(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0正常,2删除',
    create_by VARCHAR(64) COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_enterprise_code (enterprise_code),
    INDEX idx_enterprise_name (enterprise_name),
    INDEX idx_enterprise_type (enterprise_type),
    INDEX idx_industry (industry),
    INDEX idx_scale (scale),
    INDEX idx_status (status),
    INDEX idx_contract_end_date (contract_end_date),
    INDEX idx_create_time (create_time)
) COMMENT='企业信息表';
```

## 数据关系说明

### 1. 量表相关关系
- `psy_t_scale` (1) → (N) `psy_t_question`: 一个量表包含多个题目
- `psy_t_question` (1) → (N) `psy_t_question_option`: 一个题目包含多个选项
- `psy_t_scale` (1) → (N) `psy_t_subscale`: 一个量表包含多个分量表
- `psy_t_scale` (1) → (N) `psy_t_scoring_rule`: 一个量表包含多个计分规则

### 2. 测评相关关系
- `psy_t_scale` (1) → (N) `psy_t_assessment_record`: 一个量表可以有多次测评
- `psy_t_assessment_record` (1) → (N) `psy_t_answer_record`: 一次测评包含多个答题记录
- `psy_t_question` (1) → (N) `psy_t_answer_record`: 一个题目可以有多个答题记录

### 3. 企业相关关系
- `psy_t_enterprise` (1) → (N) `psy_t_enterprise_department`: 一个企业包含多个部门
- `psy_t_enterprise` (1) → (N) `psy_t_enterprise_assessment_plan`: 一个企业可以有多个测评计划
- `psy_t_enterprise_assessment_plan` (1) → (N) `psy_t_enterprise_assessment_participant`: 一个计划包含多个参与者

## 索引设计原则

1. **主键索引**: 所有表都有自增主键
2. **唯一索引**: 业务唯一字段如编码、会话ID等
3. **外键索引**: 所有外键字段都建立索引
4. **查询索引**: 根据常用查询条件建立复合索引
5. **排序索引**: 常用排序字段建立索引
6. **时间索引**: 时间字段建立索引用于范围查询

## 数据完整性约束

1. **外键约束**: 确保数据引用完整性
2. **非空约束**: 关键字段不允许为空
3. **唯一约束**: 业务唯一字段保证唯一性
4. **检查约束**: 状态字段限制取值范围
5. **默认值**: 为字段设置合理默认值

## 性能优化建议

1. **分区策略**: 大表可按时间分区
2. **读写分离**: 查询和写入分离
3. **缓存策略**: 热点数据使用Redis缓存
4. **归档策略**: 历史数据定期归档
5. **监控告警**: 建立数据库性能监控

## 数据安全

1. **敏感数据加密**: 个人信息字段加密存储
2. **访问控制**: 数据库用户权限控制
3. **审计日志**: 重要操作记录审计日志
4. **备份策略**: 定期数据备份
5. **恢复测试**: 定期进行恢复测试
