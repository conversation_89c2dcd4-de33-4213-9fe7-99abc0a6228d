package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 心理测评答案表对象 psy_t_answer
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentAnswer extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 答案ID */
    @Excel(name = "答案ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 测评记录ID */
    @Excel(name = "测评记录ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "测评记录ID不能为空")
    private Long recordId;

    /** 题目ID */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /** 选项ID(单选/多选) */
    @Excel(name = "选项ID", cellType = Excel.ColumnType.NUMERIC)
    private Long optionId;

    /** 答案内容(填空题) */
    @Excel(name = "答案内容")
    private String answerText;

    /** 得分 */
    @Excel(name = "得分")
    @DecimalMin(value = "0.00", message = "得分不能为负数")
    private BigDecimal score;

    /** 答题时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "答题时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date answerTime;

    /** 答题耗时(秒) */
    @Excel(name = "答题耗时")
    @Min(value = 0, message = "答题耗时不能为负数")
    private Integer timeSpent;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 测评记录信息 */
    private PsyAssessmentRecord record;

    /** 题目信息 */
    private PsyAssessmentQuestion question;

    /** 选项信息 */
    private PsyAssessmentOption option;

    // 扩展字段
    /** 题目内容 */
    private String questionText;

    /** 选项内容 */
    private String optionText;

    /** 题目序号 */
    private Integer questionNo;

    /** 测量维度 */
    private String dimension;

    // 常量定义
    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 获取答案显示内容
     */
    public String getAnswerDisplay() {
        if (answerText != null && !answerText.trim().isEmpty()) {
            return answerText;
        }
        if (option != null) {
            return option.getDisplayText();
        }
        if (optionText != null && !optionText.trim().isEmpty()) {
            return optionText;
        }
        return "未答";
    }

    /**
     * 获取格式化的答题耗时
     */
    public String getFormattedTimeSpent() {
        if (timeSpent == null || timeSpent == 0) {
            return "0秒";
        }
        
        if (timeSpent >= 60) {
            int minutes = timeSpent / 60;
            int seconds = timeSpent % 60;
            return minutes + "分钟" + (seconds > 0 ? seconds + "秒" : "");
        } else {
            return timeSpent + "秒";
        }
    }

    /**
     * 是否有答案
     */
    public boolean hasAnswer() {
        return (optionId != null) || (answerText != null && !answerText.trim().isEmpty());
    }
}
