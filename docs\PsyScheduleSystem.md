# 心理咨询排班和预约系统

## 系统概述

这是一个完整的心理咨询排班和预约管理系统，支持：
- 灵活的时间段定义
- 咨询师排班模板管理
- 15分钟间隔的精细化时间槽管理
- 完整的预约流程
- 自动化的定时任务

## 已完成的功能模块

### ✅ 实体类 (Entity Classes)
- `PsyTimeRange` - 时间段定义实体
- `PsyTimeSlot` - 时间槽实体
- `PsyTimeAppointment` - 预约记录实体
- `PsyTimeScheduleTemplate` - 排班模板实体
- `PsyTimeTemplateItem` - 模板明细实体
- `PsyTimeCounselorSchedule` - 咨询师排班实体
- `PsyTimeAppointmentSlot` - 预约时间槽关联实体

### ✅ 数据访问层 (Mapper Interfaces & XML)
- `PsyTimeRangeMapper` - 时间段数据访问
- `PsyTimeSlotMapper` - 时间槽数据访问
- `PsyTimeAppointmentMapper` - 预约记录数据访问
- `PsyTimeScheduleTemplateMapper` - 排班模板数据访问
- `PsyTimeTemplateItemMapper` - 模板明细数据访问
- `PsyTimeCounselorScheduleMapper` - 咨询师排班数据访问
- `PsyTimeAppointmentSlotMapper` - 预约关联数据访问

### ✅ 服务层 (Service Layer)
- `IPsyTimeRangeService` - 时间段管理服务
- `IPsyTimeSlotService` - 时间槽管理服务
- `IPsyTimeAppointmentService` - 预约管理服务
- `IPsyTimeScheduleTemplateService` - 排班模板服务

### ✅ 控制器层 (Controller Layer)
- `PsyTimeRangeController` - 时间段管理API
- `PsyTimeSlotController` - 时间槽管理API
- `PsyTimeTaskController` - 定时任务管理API

### ✅ 定时任务 (Scheduled Tasks)
- `PsyTimeSlotTaskService` - 自动化时间槽管理
  - 每天凌晨2点生成未来7天时间槽
  - 每天凌晨3点清理过期时间槽
  - 每小时更新过期状态

### ✅ 数据传输对象 (DTOs)
- `PsyTimeSlotDTO` - 时间槽数据传输对象
- `PsyAppointmentRequestDTO` - 预约请求对象

### ✅ 单元测试
- `PsyTimeRangeServiceTest` - 时间段服务测试
- `PsyTimeSystemStartupTest` - 系统启动测试

## 快速启动指南

### 1. 数据库初始化
```sql
-- 执行提供的数据库脚本创建表结构
-- 包含所有必要的表和初始数据
```

### 2. 启动应用
```bash
# 启动Spring Boot应用
mvn spring-boot:run
# 或者
java -jar xihuan-admin.jar
```

### 3. 初始化时间段
```bash
# 调用API初始化默认时间段
POST http://localhost:8080/system/timeRange/init
```

### 4. 生成时间槽
```bash
# 为所有咨询师生成未来7天的时间槽
POST http://localhost:8080/system/timeTask/generate?days=7
```

### 5. 验证系统
```bash
# 查询时间段列表
GET http://localhost:8080/system/timeRange/listActive

# 查询时间槽
GET http://localhost:8080/system/timeSlot/available?date=2025-07-10&centerId=1
```

## 数据库表结构

### 核心表说明

1. **psy_time_range** - 时间段定义表
   - 定义上午、下午、晚上等时间段
   - 支持自定义图标和时间范围

2. **psy_time_slot** - 时间槽主表
   - 15分钟间隔的时间槽
   - 支持咨询师专属和公开时段
   - 状态管理（可用/已预约/已过期）

3. **psy_time_appointment** - 预约记录表
   - 支持多种预约状态
   - 集成支付状态管理
   - 支持连续时间段预约

4. **psy_time_schedule_template** - 排班模板表
   - 咨询师可创建多个排班模板
   - 支持有效期设置
   - 默认模板机制

5. **psy_time_counselor_schedule** - 实际排班表
   - 基于模板生成的实际排班
   - 支持临时调班
   - 工作状态管理

## 主要功能模块

### 1. 时间段管理

**接口路径**: `/system/timeRange`

**主要功能**:
- 查询时间段列表
- 新增/修改/删除时间段
- 根据小时查询所属时间段
- 初始化默认时间段

**使用示例**:
```bash
# 初始化默认时间段
POST /system/timeRange/init

# 根据小时查询时间段
GET /system/timeRange/getByHour/10
```

### 2. 时间槽管理

**接口路径**: `/system/timeSlot`

**主要功能**:
- 查询时间槽列表
- 按咨询师和日期范围查询
- 获取格式化的时间槽数据
- 批量生成时间槽
- 状态管理

**使用示例**:
```bash
# 获取咨询师的格式化时间槽
GET /system/timeSlot/formatted/1?startDate=2025-07-10&endDate=2025-07-16

# 为咨询师生成时间槽
POST /system/timeSlot/generate/1?startDate=2025-07-10&endDate=2025-07-16

# 查询可用时间槽
GET /system/timeSlot/available?date=2025-07-10&centerId=1&counselorId=1
```

### 3. 定时任务管理

**接口路径**: `/system/timeTask`

**主要功能**:
- 自动生成未来时间槽（每天凌晨2点）
- 清理过期时间槽（每天凌晨3点）
- 更新过期状态（每小时）
- 手动触发任务

**使用示例**:
```bash
# 手动生成7天的时间槽
POST /system/timeTask/generate?days=7

# 手动清理7天前的过期时间槽
POST /system/timeTask/clean?days=7
```

## 业务流程

### 1. 系统初始化流程

1. 执行数据库脚本创建表结构
2. 调用 `/system/timeRange/init` 初始化默认时间段
3. 为咨询师创建默认排班模板
4. 生成初始时间槽

### 2. 排班管理流程

1. 咨询师创建排班模板
2. 设置模板的工作时间和有效期
3. 系统根据模板自动生成实际排班
4. 基于排班生成15分钟间隔的时间槽

### 3. 预约流程

1. 用户查询可用时间槽
2. 选择连续的时间槽进行预约
3. 系统验证时间槽可用性
4. 创建预约记录并更新时间槽状态
5. 处理支付流程
6. 确认预约

## 配置说明

### 定时任务配置

系统包含以下定时任务：

```java
// 每天凌晨2点生成未来7天的时间槽
@Scheduled(cron = "0 0 2 * * ?")

// 每天凌晨3点清理7天前的过期时间槽  
@Scheduled(cron = "0 0 3 * * ?")

// 每小时更新过期时间槽状态
@Scheduled(cron = "0 0 * * * ?")
```

### 时间槽生成规则

- 间隔：15分钟
- 范围：基于咨询师排班时间
- 状态：默认为可用（0）
- 分组：按1小时时段分组

## API 权限配置

需要在系统中配置以下权限：

```
system:timeRange:list     # 查询时间段列表
system:timeRange:add      # 新增时间段
system:timeRange:edit     # 修改时间段
system:timeRange:remove   # 删除时间段
system:timeRange:init     # 初始化时间段

system:timeSlot:list      # 查询时间槽列表
system:timeSlot:add       # 新增时间槽
system:timeSlot:edit      # 修改时间槽
system:timeSlot:remove    # 删除时间槽
system:timeSlot:generate  # 生成时间槽

system:timeTask:query     # 查询定时任务状态
system:timeTask:generate  # 手动生成时间槽
system:timeTask:clean     # 手动清理时间槽
```

## 注意事项

1. **数据一致性**: 所有涉及时间槽状态变更的操作都使用了事务管理
2. **性能优化**: 批量操作使用了批量插入和更新
3. **时区处理**: 系统使用LocalDate和LocalTime处理时间，避免时区问题
4. **软删除**: 大部分删除操作使用软删除，保留数据完整性
5. **状态管理**: 时间槽状态自动更新，确保数据准确性

## 扩展建议

1. **缓存优化**: 对频繁查询的时间槽数据添加Redis缓存
2. **消息通知**: 集成消息推送，通知预约状态变更
3. **数据统计**: 添加预约统计和分析功能
4. **移动端适配**: 提供移动端友好的API接口
5. **多租户支持**: 支持多个咨询中心的独立管理

## 故障排查

### 常见问题

1. **时间槽生成失败**
   - 检查咨询师是否有有效的排班模板
   - 确认时间段定义是否正确
   - 查看定时任务日志

2. **预约创建失败**
   - 验证时间槽是否可用
   - 检查用户是否有冲突预约
   - 确认支付状态

3. **定时任务不执行**
   - 检查Spring Boot的@EnableScheduling注解
   - 确认cron表达式正确
   - 查看应用日志
