package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyTEnterpriseAssessmentPlan;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.system.mapper.PsyTEnterpriseAssessmentPlanMapper;
import com.xihuan.system.service.IPsyTEnterpriseAssessmentPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业测评计划Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyTEnterpriseAssessmentPlanServiceImpl implements IPsyTEnterpriseAssessmentPlanService {
    
    @Autowired
    private PsyTEnterpriseAssessmentPlanMapper planMapper;

    /**
     * 查询企业测评计划列表
     * 
     * @param plan 企业测评计划
     * @return 企业测评计划集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentPlan> selectPlanList(PsyTEnterpriseAssessmentPlan plan) {
        return planMapper.selectPlanList(plan);
    }

    /**
     * 根据ID查询企业测评计划
     * 
     * @param id 企业测评计划ID
     * @return 企业测评计划
     */
    @Override
    public PsyTEnterpriseAssessmentPlan selectPlanById(Long id) {
        return planMapper.selectPlanById(id);
    }

    /**
     * 查询企业测评计划详情
     * 
     * @param id 企业测评计划ID
     * @return 企业测评计划详情
     */
    @Override
    public PsyTEnterpriseAssessmentPlan selectPlanWithDetails(Long id) {
        return planMapper.selectPlanWithDetails(id);
    }

    /**
     * 根据企业ID查询测评计划列表
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentPlan> selectPlansByEnterpriseId(Long enterpriseId) {
        return planMapper.selectPlansByEnterpriseId(enterpriseId);
    }

    /**
     * 查询进行中的测评计划
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentPlan> selectInProgressPlans(Long enterpriseId) {
        return planMapper.selectInProgressPlans(enterpriseId);
    }

    /**
     * 查询已结束的测评计划
     * 
     * @param enterpriseId 企业ID
     * @return 测评计划集合
     */
    @Override
    public List<PsyTEnterpriseAssessmentPlan> selectEndedPlans(Long enterpriseId) {
        return planMapper.selectEndedPlans(enterpriseId);
    }

    /**
     * 新增企业测评计划
     * 
     * @param plan 企业测评计划
     * @return 结果
     */
    @Override
    @Transactional
    public int insertPlan(PsyTEnterpriseAssessmentPlan plan) {
        // 设置默认值
        if (plan.getStatus() == null) {
            plan.setStatus(PsyTEnterpriseAssessmentPlan.STATUS_DRAFT);
        }
        if (plan.getActualParticipants() == null) {
            plan.setActualParticipants(0);
        }
        if (plan.getCompletedParticipants() == null) {
            plan.setCompletedParticipants(0);
        }
        if (plan.getCompletionRate() == null) {
            plan.setCompletionRate(new java.math.BigDecimal("0.0"));
        }
        if (plan.getDelFlag() == null) {
            plan.setDelFlag("0");
        }
        
        return planMapper.insertPlan(plan);
    }

    /**
     * 修改企业测评计划
     * 
     * @param plan 企业测评计划
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePlan(PsyTEnterpriseAssessmentPlan plan) {
        return planMapper.updatePlan(plan);
    }

    /**
     * 删除企业测评计划
     * 
     * @param ids 需要删除的企业测评计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deletePlanByIds(Long[] ids) {
        // 检查计划是否可以删除
        for (Long id : ids) {
            PsyTEnterpriseAssessmentPlan plan = selectPlanById(id);
            if (plan != null && PsyTEnterpriseAssessmentPlan.STATUS_IN_PROGRESS.equals(plan.getStatus())) {
                throw new ServiceException("进行中的计划不能删除");
            }
        }
        
        return planMapper.deletePlanByIds(ids);
    }

    /**
     * 创建测评计划
     * 
     * @param plan 测评计划信息
     * @return 结果
     */
    @Override
    @Transactional
    public int createAssessmentPlan(PsyTEnterpriseAssessmentPlan plan) {
        // 验证计划信息
        validatePlan(plan);
        
        // 设置初始状态
        plan.setStatus(PsyTEnterpriseAssessmentPlan.STATUS_DRAFT);
        plan.setActualParticipants(0);
        plan.setCompletedParticipants(0);
        plan.setCompletionRate(new java.math.BigDecimal("0.0"));
        
        return insertPlan(plan);
    }

    /**
     * 启动测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int startAssessmentPlan(Long planId) {
        if (!checkPlanCanStart(planId)) {
            throw new ServiceException("计划不满足启动条件");
        }
        
        PsyTEnterpriseAssessmentPlan plan = new PsyTEnterpriseAssessmentPlan();
        plan.setId(planId);
        plan.setStatus(PsyTEnterpriseAssessmentPlan.STATUS_IN_PROGRESS);
        plan.setStartTime(new Date());
        
        return updatePlan(plan);
    }

    /**
     * 暂停测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int pauseAssessmentPlan(Long planId) {
        PsyTEnterpriseAssessmentPlan plan = new PsyTEnterpriseAssessmentPlan();
        plan.setId(planId);
        plan.setStatus(PsyTEnterpriseAssessmentPlan.STATUS_PAUSED);
        
        return updatePlan(plan);
    }

    /**
     * 恢复测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int resumeAssessmentPlan(Long planId) {
        PsyTEnterpriseAssessmentPlan plan = new PsyTEnterpriseAssessmentPlan();
        plan.setId(planId);
        plan.setStatus(PsyTEnterpriseAssessmentPlan.STATUS_IN_PROGRESS);
        
        return updatePlan(plan);
    }

    /**
     * 结束测评计划
     * 
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int endAssessmentPlan(Long planId) {
        PsyTEnterpriseAssessmentPlan plan = new PsyTEnterpriseAssessmentPlan();
        plan.setId(planId);
        plan.setStatus(PsyTEnterpriseAssessmentPlan.STATUS_ENDED);
        plan.setEndTime(new Date());
        
        // 更新最终统计信息
        updatePlanStatistics(planId);
        
        return updatePlan(plan);
    }

    /**
     * 取消测评计划
     * 
     * @param planId 计划ID
     * @param reason 取消原因
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelAssessmentPlan(Long planId, String reason) {
        PsyTEnterpriseAssessmentPlan plan = new PsyTEnterpriseAssessmentPlan();
        plan.setId(planId);
        plan.setStatus(PsyTEnterpriseAssessmentPlan.STATUS_CANCELLED);
        plan.setRemark(reason);
        
        return updatePlan(plan);
    }

    /**
     * 发送计划提醒
     * 
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    public int sendPlanReminder(Long planId) {
        // TODO: 实现发送提醒逻辑
        return 1;
    }

    /**
     * 查询计划统计信息
     * 
     * @param planId 计划ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectPlanStats(Long planId) {
        return planMapper.selectPlanStats(planId);
    }

    /**
     * 查询企业计划统计
     * 
     * @param enterpriseId 企业ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectEnterprisePlanStats(Long enterpriseId) {
        return planMapper.selectEnterprisePlanStats(enterpriseId);
    }

    /**
     * 查询计划参与度分析
     * 
     * @param planId 计划ID
     * @return 分析结果
     */
    @Override
    public Map<String, Object> selectPlanParticipationAnalysis(Long planId) {
        return planMapper.selectPlanParticipationAnalysis(planId);
    }

    /**
     * 查询计划效果评估
     * 
     * @param planId 计划ID
     * @return 评估结果
     */
    @Override
    public Map<String, Object> selectPlanEffectivenessAnalysis(Long planId) {
        return planMapper.selectPlanEffectivenessAnalysis(planId);
    }

    /**
     * 生成计划报告
     * 
     * @param planId 计划ID
     * @return 报告内容
     */
    @Override
    public Map<String, Object> generatePlanReport(Long planId) {
        // TODO: 实现报告生成逻辑
        Map<String, Object> report = new HashMap<>();
        report.put("planId", planId);
        report.put("generated", false);
        report.put("message", "功能暂未实现");
        return report;
    }

    /**
     * 导出计划数据
     * 
     * @param planId 计划ID
     * @return 导出数据
     */
    @Override
    public Map<String, Object> exportPlanData(Long planId) {
        // TODO: 实现数据导出逻辑
        Map<String, Object> data = new HashMap<>();
        data.put("planId", planId);
        data.put("exported", false);
        data.put("message", "功能暂未实现");
        return data;
    }

    /**
     * 复制测评计划
     * 
     * @param sourcePlanId 源计划ID
     * @param targetPlan 目标计划信息
     * @return 结果
     */
    @Override
    @Transactional
    public int copyAssessmentPlan(Long sourcePlanId, PsyTEnterpriseAssessmentPlan targetPlan) {
        // TODO: 实现计划复制逻辑
        throw new ServiceException("功能暂未实现");
    }

    /**
     * 更新计划统计信息
     *
     * @param planId 计划ID
     * @return 结果
     */
    @Override
    @Transactional
    public int updatePlanStatistics(Long planId) {
        return planMapper.updatePlanStatistics(planId);
    }

    /**
     * 检查计划是否可以启动
     *
     * @param planId 计划ID
     * @return 检查结果
     */
    @Override
    public boolean checkPlanCanStart(Long planId) {
        PsyTEnterpriseAssessmentPlan plan = selectPlanById(planId);
        return plan != null &&
               PsyTEnterpriseAssessmentPlan.STATUS_DRAFT.equals(plan.getStatus()) &&
               plan.getTargetParticipants() != null && plan.getTargetParticipants() > 0;
    }

    /**
     * 检查计划是否可以结束
     *
     * @param planId 计划ID
     * @return 检查结果
     */
    @Override
    public boolean checkPlanCanEnd(Long planId) {
        PsyTEnterpriseAssessmentPlan plan = selectPlanById(planId);
        return plan != null &&
               (PsyTEnterpriseAssessmentPlan.STATUS_IN_PROGRESS.equals(plan.getStatus()) ||
                PsyTEnterpriseAssessmentPlan.STATUS_PAUSED.equals(plan.getStatus()));
    }

    /**
     * 查询即将开始的计划
     *
     * @param hours 小时数
     * @return 计划列表
     */
    @Override
    public List<PsyTEnterpriseAssessmentPlan> selectPlansStartingSoon(Integer hours) {
        return planMapper.selectPlansStartingSoon(hours);
    }

    /**
     * 查询即将结束的计划
     *
     * @param hours 小时数
     * @return 计划列表
     */
    @Override
    public List<PsyTEnterpriseAssessmentPlan> selectPlansEndingSoon(Integer hours) {
        return planMapper.selectPlansEndingSoon(hours);
    }

    /**
     * 查询超时未完成的计划
     *
     * @return 计划列表
     */
    @Override
    public List<PsyTEnterpriseAssessmentPlan> selectOverduePlans() {
        return planMapper.selectOverduePlans();
    }

    /**
     * 自动结束过期计划
     * 
     * @return 处理数量
     */
    @Override
    @Transactional
    public int autoEndExpiredPlans() {
        return planMapper.autoEndExpiredPlans();
    }

    /**
     * 查询计划完成率趋势
     *
     * @param enterpriseId 企业ID
     * @param days 天数
     * @return 趋势数据
     */
    @Override
    public List<Map<String, Object>> selectPlanCompletionTrend(Long enterpriseId, Integer days) {
        return planMapper.selectPlanCompletionTrend(enterpriseId, days);
    }

    /**
     * 查询热门量表统计
     *
     * @param enterpriseId 企业ID
     * @return 统计数据
     */
    @Override
    public List<Map<String, Object>> selectPopularScaleStats(Long enterpriseId) {
        return planMapper.selectPopularScaleStats(enterpriseId);
    }

    /**
     * 查询部门参与统计
     *
     * @param enterpriseId 企业ID
     * @return 统计数据
     */
    @Override
    public List<Map<String, Object>> selectDepartmentParticipationStats(Long enterpriseId) {
        return planMapper.selectDepartmentParticipationStats(enterpriseId);
    }

    /**
     * 验证计划权限
     *
     * @param planId 计划ID
     * @param userId 用户ID
     * @param operation 操作类型
     * @return 验证结果
     */
    @Override
    public boolean validatePlanPermission(Long planId, Long userId, String operation) {
        // TODO: 实现权限验证逻辑
        return true;
    }

    /**
     * 验证计划信息
     *
     * @param plan 计划信息
     */
    private void validatePlan(PsyTEnterpriseAssessmentPlan plan) {
        if (plan.getEnterpriseId() == null) {
            throw new ServiceException("企业ID不能为空");
        }
        if (plan.getScaleId() == null) {
            throw new ServiceException("量表ID不能为空");
        }
        if (plan.getPlanName() == null || plan.getPlanName().trim().isEmpty()) {
            throw new ServiceException("计划名称不能为空");
        }
        if (plan.getStartTime() == null) {
            throw new ServiceException("开始时间不能为空");
        }
        if (plan.getEndTime() == null) {
            throw new ServiceException("结束时间不能为空");
        }
        if (plan.getStartTime().after(plan.getEndTime())) {
            throw new ServiceException("开始时间不能晚于结束时间");
        }
    }
}
