package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.*;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 小程序咨询Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/consultation")
public class MiniAppConsultationController extends BaseController {
    
    @Autowired
    private TokenService tokenService;
    
    @Autowired
    private IPsyConsultantOrderService orderService;
    
    @Autowired
    private IPsyConsultationRecordService recordService;
    
    @Autowired
    private IPsyConsultantReviewService reviewService;
    
    @Autowired
    private IPsyConsultantInterruptionService interruptionService;

    /**
     * 创建咨询订单
     */
    @PostMapping("/order")
    public AjaxResult createOrder(@RequestBody PsyConsultantOrder order, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        // 设置用户信息
        order.setUserId(loginUser.getUserId());
        order.setCreateBy(loginUser.getUsername());
        
        try {
            int result = orderService.insertOrder(order);
            if (result > 0) {
                Map<String, Object> data = new HashMap<>();
                data.put("orderNo", order.getOrderNo());
                data.put("orderId", order.getId());
                data.put("paymentAmount", order.getPaymentAmount());
                return success(data);
            } else {
                return error("创建订单失败");
            }
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 获取用户订单列表
     */
    @GetMapping("/orders")
    public AjaxResult getUserOrders(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        List<PsyConsultantOrder> orders = orderService.selectOrdersByUserId(loginUser.getUserId());
        return success(orders);
    }

    /**
     * 获取订单详情
     */
    @GetMapping("/order/{id}")
    public AjaxResult getOrderDetails(@PathVariable Long id, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        PsyConsultantOrder order = orderService.selectOrderWithDetails(id);
        if (order == null || !order.getUserId().equals(loginUser.getUserId())) {
            return error("订单不存在或无权限访问");
        }
        
        return success(order);
    }

    /**
     * 取消订单
     */
    @PostMapping("/order/cancel/{id}")
    public AjaxResult cancelOrder(@PathVariable Long id, @RequestParam String cancelReason, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        // 验证订单归属
        PsyConsultantOrder order = orderService.selectOrderById(id);
        if (order == null || !order.getUserId().equals(loginUser.getUserId())) {
            return error("订单不存在或无权限操作");
        }
        
        try {
            return toAjax(orderService.cancelOrder(id, cancelReason));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 检查时间段是否可预约
     */
    @GetMapping("/checkAvailable")
    public AjaxResult checkTimeAvailable(@RequestParam Long consultantId,
                                       @RequestParam Date startTime,
                                       @RequestParam Integer duration) {
        Date endTime = new Date(startTime.getTime() + duration * 60 * 1000);
        boolean hasConflict = orderService.checkTimeConflict(consultantId, startTime, endTime, null);
        
        Map<String, Object> result = new HashMap<>();
        result.put("available", !hasConflict);
        result.put("message", hasConflict ? "该时间段已被预约" : "时间段可用");
        
        return success(result);
    }

    /**
     * 获取用户咨询记录列表
     */
    @GetMapping("/records")
    public AjaxResult getUserRecords(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        List<PsyConsultationRecord> records = recordService.selectRecordsByUserId(loginUser.getUserId());
        return success(records);
    }

    /**
     * 获取咨询记录详情
     */
    @GetMapping("/record/{id}")
    public AjaxResult getRecordDetails(@PathVariable Long id, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        PsyConsultationRecord record = recordService.selectRecordWithDetails(id);
        if (record == null || !record.getUserId().equals(loginUser.getUserId())) {
            return error("咨询记录不存在或无权限访问");
        }
        
        return success(record);
    }

    /**
     * 提交咨询评价
     */
    @PostMapping("/review")
    public AjaxResult submitReview(@RequestBody PsyConsultantReview review, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        // 检查是否已评价
        boolean reviewed = reviewService.checkUserReviewed(loginUser.getUserId(), review.getRecordId());
        if (reviewed) {
            return error("您已评价过该咨询");
        }
        
        // 检查是否有权限评价
        if (!recordService.canRate(review.getRecordId(), loginUser.getUserId())) {
            return error("无权限评价或咨询未完成");
        }
        
        // 设置评价信息
        review.setUserId(loginUser.getUserId());
        review.setCreateBy(loginUser.getUsername());
        review.setReviewTime(DateUtils.getNowDate());
        
        int result = reviewService.insertReview(review);
        if (result > 0) {
            // 更新咨询师评分信息
            reviewService.updateConsultantRating(review.getConsultantId());
            return success();
        } else {
            return error("提交评价失败");
        }
    }

    /**
     * 获取用户评价列表
     */
    @GetMapping("/reviews")
    public AjaxResult getUserReviews(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        List<PsyConsultantReview> reviews = reviewService.selectReviewsByUserId(loginUser.getUserId());
        return success(reviews);
    }

    /**
     * 获取咨询师评价列表
     */
    @GetMapping("/consultant/reviews/{consultantId}")
    public AjaxResult getConsultantReviews(@PathVariable Long consultantId) {
        List<PsyConsultantReview> reviews = reviewService.selectApprovedReviews(consultantId);
        return success(reviews);
    }

    /**
     * 获取用户咨询统计
     */
    @GetMapping("/statistics")
    public AjaxResult getUserStatistics(HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        Long userId = loginUser.getUserId();
        
        Map<String, Object> statistics = new HashMap<>();
        
        // 咨询统计
        Map<String, Object> consultationStats = recordService.getUserConsultationStats(userId);
        statistics.putAll(consultationStats);
        
        // 订单统计
        Map<String, Object> orderStats = orderService.getUserOrderStats(userId);
        statistics.putAll(orderStats);
        
        return success(statistics);
    }

    /**
     * 获取与咨询师的咨询历史
     */
    @GetMapping("/history/{consultantId}")
    public AjaxResult getConsultationHistory(@PathVariable Long consultantId, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        List<PsyConsultationRecord> records = recordService.selectRecordsByUserAndConsultant(loginUser.getUserId(), consultantId);
        return success(records);
    }

    /**
     * 检查是否可以评价咨询记录
     */
    @GetMapping("/canRate/{recordId}")
    public AjaxResult checkCanRate(@PathVariable Long recordId, HttpServletRequest request) {
        // 获取当前用户
        LoginUser loginUser = tokenService.getLoginUser(request);
        if (loginUser == null) {
            return error("用户未登录");
        }
        
        boolean canRate = recordService.canRate(recordId, loginUser.getUserId());
        boolean hasReviewed = false;
        
        if (canRate) {
            // 检查是否已评价
            PsyConsultationRecord record = recordService.selectRecordById(recordId);
            if (record != null) {
                hasReviewed = reviewService.checkUserReviewed(loginUser.getUserId(), recordId);
            }
        }
        
        Map<String, Object> result = new HashMap<>();
        result.put("canRate", canRate && !hasReviewed);
        result.put("hasReviewed", hasReviewed);
        
        return success(result);
    }
}
