package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTimeScheduleTemplate;
import java.time.LocalDate;
import java.util.List;

/**
 * 排班模板Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTimeScheduleTemplateService {
    
    /**
     * 查询排班模板列表
     * 
     * @param template 排班模板
     * @return 排班模板集合
     */
    List<PsyTimeScheduleTemplate> selectTemplateList(PsyTimeScheduleTemplate template);
    
    /**
     * 根据ID查询排班模板
     * 
     * @param id 排班模板主键
     * @return 排班模板
     */
    PsyTimeScheduleTemplate selectTemplateById(Long id);
    
    /**
     * 根据咨询师ID查询排班模板
     * 
     * @param counselorId 咨询师ID
     * @return 排班模板集合
     */
    List<PsyTimeScheduleTemplate> selectTemplatesByCounselorId(Long counselorId);
    
    /**
     * 查询咨询师的默认模板
     * 
     * @param counselorId 咨询师ID
     * @return 默认排班模板
     */
    PsyTimeScheduleTemplate selectDefaultTemplateByCounselorId(Long counselorId);
    
    /**
     * 查询在指定日期有效的模板
     * 
     * @param counselorId 咨询师ID
     * @param date 日期
     * @return 有效的排班模板
     */
    PsyTimeScheduleTemplate selectEffectiveTemplate(Long counselorId, LocalDate date);
    
    /**
     * 新增排班模板
     * 
     * @param template 排班模板
     * @return 结果
     */
    int insertTemplate(PsyTimeScheduleTemplate template);
    
    /**
     * 修改排班模板
     * 
     * @param template 排班模板
     * @return 结果
     */
    int updateTemplate(PsyTimeScheduleTemplate template);
    
    /**
     * 设置默认模板
     * 
     * @param counselorId 咨询师ID
     * @param templateId 模板ID
     * @return 结果
     */
    int setDefaultTemplate(Long counselorId, Long templateId);
    
    /**
     * 批量删除排班模板
     * 
     * @param ids 需要删除的排班模板主键集合
     * @return 结果
     */
    int deleteTemplateByIds(Long[] ids);
    
    /**
     * 删除排班模板信息
     * 
     * @param id 排班模板主键
     * @return 结果
     */
    int deleteTemplateById(Long id);
    
    /**
     * 校验模板名称是否唯一
     * 
     * @param template 排班模板信息
     * @return 结果
     */
    String checkTemplateNameUnique(PsyTimeScheduleTemplate template);
    
    /**
     * 为咨询师创建默认排班模板
     * 
     * @param counselorId 咨询师ID
     * @param centerId 咨询中心ID
     * @return 结果
     */
    int createDefaultTemplate(Long counselorId, Long centerId);
    
    /**
     * 复制排班模板
     * 
     * @param templateId 源模板ID
     * @param newName 新模板名称
     * @return 新模板ID
     */
    Long copyTemplate(Long templateId, String newName);
}
