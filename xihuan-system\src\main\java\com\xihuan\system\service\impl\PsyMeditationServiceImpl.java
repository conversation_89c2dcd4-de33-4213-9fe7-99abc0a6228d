package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyMeditation;
import com.xihuan.common.core.domain.entity.PsyMeditationOrder;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyMeditationMapper;
import com.xihuan.system.mapper.PsyMeditationOrderMapper;
import com.xihuan.system.mapper.PsyMeditationReviewMapper;
import com.xihuan.system.mapper.PsyMeditationCategoryRelMapper;
import com.xihuan.system.service.IPsyMeditationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * 冥想主表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyMeditationServiceImpl implements IPsyMeditationService {
    
    @Autowired
    private PsyMeditationMapper meditationMapper;
    
    @Autowired
    private PsyMeditationOrderMapper orderMapper;
    
    @Autowired
    private PsyMeditationReviewMapper reviewMapper;
    
    @Autowired
    private PsyMeditationCategoryRelMapper categoryRelMapper;

    /**
     * 查询冥想列表
     * 
     * @param meditation 冥想信息
     * @return 冥想集合
     */
    @Override
    public List<PsyMeditation> selectMeditationList(PsyMeditation meditation) {
        return meditationMapper.selectMeditationList(meditation);
    }

    /**
     * 查询冥想详情（包含分类等信息）
     * 
     * @param id 冥想ID
     * @return 冥想详情
     */
    @Override
    public PsyMeditation selectMeditationWithDetails(Long id) {
        return meditationMapper.selectMeditationWithDetails(id);
    }

    /**
     * 根据ID查询冥想
     * 
     * @param id 冥想ID
     * @return 冥想信息
     */
    @Override
    public PsyMeditation selectMeditationById(Long id) {
        return meditationMapper.selectMeditationById(id);
    }

    /**
     * 新增冥想
     * 
     * @param meditation 冥想信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertMeditation(PsyMeditation meditation) {
        meditation.setCreateTime(DateUtils.getNowDate());
        meditation.setDelFlag(0);
        meditation.setStatus(0); // 默认未发布状态
        meditation.setPlayCount(0);
        meditation.setRatingAvg(BigDecimal.ZERO);
        meditation.setRatingCount(0);
        
        int result = meditationMapper.insertMeditation(meditation);
        
        // 处理分类关系
        if (result > 0 && !CollectionUtils.isEmpty(meditation.getCategoryIds())) {
            meditationMapper.batchInsertMeditationCategories(meditation.getId(), meditation.getCategoryIds());
        }
        
        return result;
    }

    /**
     * 修改冥想
     * 
     * @param meditation 冥想信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateMeditation(PsyMeditation meditation) {
        meditation.setUpdateTime(DateUtils.getNowDate());
        
        // 删除原有分类关系
        meditationMapper.deleteMeditationCategories(meditation.getId());
        
        // 重新插入分类关系
        if (!CollectionUtils.isEmpty(meditation.getCategoryIds())) {
            meditationMapper.batchInsertMeditationCategories(meditation.getId(), meditation.getCategoryIds());
        }
        
        return meditationMapper.updateMeditation(meditation);
    }

    /**
     * 删除冥想
     * 
     * @param id 冥想ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMeditationById(Long id) {
        // 检查是否可以删除
        if (!canDeleteMeditation(id)) {
            throw new ServiceException("该冥想已有订单，无法删除");
        }
        
        // 删除分类关系
        meditationMapper.deleteMeditationCategories(id);
        
        // 删除评价
        reviewMapper.deleteReviewByMeditationId(id);
        
        // 删除冥想
        return meditationMapper.deleteMeditationById(id);
    }

    /**
     * 批量删除冥想
     * 
     * @param ids 需要删除的冥想ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteMeditationByIds(Long[] ids) {
        // 检查是否可以删除
        for (Long id : ids) {
            if (!canDeleteMeditation(id)) {
                throw new ServiceException("冥想ID为" + id + "的冥想已有订单，无法删除");
            }
        }
        
        // 删除分类关系
        meditationMapper.deleteMeditationCategoriesByIds(ids);
        
        // 删除评价
        reviewMapper.deleteReviewByMeditationIds(ids);
        
        // 删除冥想
        return meditationMapper.deleteMeditationByIds(ids);
    }

    /**
     * 根据分类ID查询冥想列表
     * 
     * @param categoryId 分类ID
     * @return 冥想集合
     */
    @Override
    public List<PsyMeditation> selectMeditationsByCategoryId(Long categoryId) {
        return meditationMapper.selectMeditationsByCategoryId(categoryId);
    }

    /**
     * 更新冥想评分信息
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    @Override
    public int updateMeditationRating(Long meditationId) {
        // 计算平均评分
        BigDecimal ratingAvg = reviewMapper.calculateAverageRating(meditationId);
        
        // 统计评价数量
        int ratingCount = reviewMapper.countReviewsByMeditationId(meditationId);
        
        return meditationMapper.updateMeditationRating(meditationId, ratingAvg, ratingCount);
    }

    /**
     * 增加冥想播放次数
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    @Override
    public int incrementPlayCount(Long meditationId) {
        return meditationMapper.incrementPlayCount(meditationId);
    }

    /**
     * 发布冥想
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    @Override
    public int publishMeditation(Long meditationId) {
        PsyMeditation meditation = new PsyMeditation();
        meditation.setId(meditationId);
        meditation.setStatus(1); // 已发布
        meditation.setUpdateTime(DateUtils.getNowDate());
        return meditationMapper.updateMeditation(meditation);
    }

    /**
     * 下架冥想
     * 
     * @param meditationId 冥想ID
     * @return 结果
     */
    @Override
    public int unpublishMeditation(Long meditationId) {
        PsyMeditation meditation = new PsyMeditation();
        meditation.setId(meditationId);
        meditation.setStatus(2); // 已下架
        meditation.setUpdateTime(DateUtils.getNowDate());
        return meditationMapper.updateMeditation(meditation);
    }

    /**
     * 检查冥想是否可以删除
     * 
     * @param meditationId 冥想ID
     * @return 是否可以删除
     */
    @Override
    public boolean canDeleteMeditation(Long meditationId) {
        // 检查是否有已支付的订单
        List<PsyMeditationOrder> orders = orderMapper.selectOrdersByMeditationId(meditationId);
        for (PsyMeditationOrder order : orders) {
            if (order.getStatus() != null && order.getStatus() >= 1) { // 已支付或已完成
                return false;
            }
        }
        return true;
    }
}
