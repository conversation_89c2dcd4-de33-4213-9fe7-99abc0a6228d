package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyCourseInstructor;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyCourseInstructorService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 讲师信息表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/instructor")
public class PsyCourseInstructorController extends BaseController {
    
    @Autowired
    private IPsyCourseInstructorService instructorService;

    /**
     * 查询讲师列表
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyCourseInstructor instructor) {
        startPage();
        List<PsyCourseInstructor> list = instructorService.selectInstructorList(instructor);
        return getDataTable(list);
    }

    /**
     * 导出讲师列表
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:export')")
    @Log(title = "讲师", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyCourseInstructor instructor) {
        List<PsyCourseInstructor> list = instructorService.selectInstructorList(instructor);
        ExcelUtil<PsyCourseInstructor> util = new ExcelUtil<PsyCourseInstructor>(PsyCourseInstructor.class);
        util.exportExcel(response, list, "讲师数据");
    }

    /**
     * 获取讲师详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(instructorService.selectInstructorById(id));
    }

    /**
     * 获取讲师详细信息（包含头像图片）
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(instructorService.selectInstructorWithDetails(id));
    }

    /**
     * 新增讲师
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:add')")
    @Log(title = "讲师", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyCourseInstructor instructor) {
        return toAjax(instructorService.insertInstructor(instructor));
    }

    /**
     * 修改讲师
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:edit')")
    @Log(title = "讲师", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyCourseInstructor instructor) {
        return toAjax(instructorService.updateInstructor(instructor));
    }

    /**
     * 删除讲师
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:remove')")
    @Log(title = "讲师", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(instructorService.deleteInstructorByIds(ids));
    }

    /**
     * 获取所有讲师简单信息（用于下拉框）
     */
    @PreAuthorize("@ss.hasPermi('system:instructor:list')")
    @GetMapping("/simple")
    public AjaxResult getSimpleList() {
        List<PsyCourseInstructor> list = instructorService.selectAllSimpleList();
        return success(list);
    }
}
