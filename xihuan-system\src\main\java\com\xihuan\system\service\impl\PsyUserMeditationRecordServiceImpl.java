package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyUserMeditationRecord;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyUserMeditationRecordMapper;
import com.xihuan.system.service.IPsyUserMeditationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户冥想记录表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyUserMeditationRecordServiceImpl implements IPsyUserMeditationRecordService {
    
    @Autowired
    private PsyUserMeditationRecordMapper recordMapper;

    /**
     * 查询冥想记录列表
     * 
     * @param record 冥想记录信息
     * @return 冥想记录集合
     */
    @Override
    public List<PsyUserMeditationRecord> selectRecordList(PsyUserMeditationRecord record) {
        return recordMapper.selectRecordList(record);
    }

    /**
     * 根据ID查询冥想记录
     * 
     * @param id 冥想记录ID
     * @return 冥想记录信息
     */
    @Override
    public PsyUserMeditationRecord selectRecordById(Long id) {
        return recordMapper.selectRecordById(id);
    }

    /**
     * 根据用户ID和冥想ID查询最新记录
     * 
     * @param userId 用户ID
     * @param meditationId 冥想ID
     * @return 冥想记录信息
     */
    @Override
    public PsyUserMeditationRecord selectLatestRecordByUserAndMeditation(Long userId, Long meditationId) {
        return recordMapper.selectLatestRecordByUserAndMeditation(userId, meditationId);
    }

    /**
     * 根据用户ID查询冥想记录列表
     * 
     * @param userId 用户ID
     * @return 冥想记录集合
     */
    @Override
    public List<PsyUserMeditationRecord> selectRecordsByUserId(Long userId) {
        return recordMapper.selectRecordsByUserId(userId);
    }

    /**
     * 新增冥想记录
     * 
     * @param record 冥想记录信息
     * @return 结果
     */
    @Override
    public int insertRecord(PsyUserMeditationRecord record) {
        record.setCreateTime(DateUtils.getNowDate());
        
        // 设置默认值
        if (record.getDurationPlayed() == null) {
            record.setDurationPlayed(0);
        }
        if (record.getIsCompleted() == null) {
            record.setIsCompleted(0);
        }
        
        return recordMapper.insertRecord(record);
    }

    /**
     * 修改冥想记录
     * 
     * @param record 冥想记录信息
     * @return 结果
     */
    @Override
    public int updateRecord(PsyUserMeditationRecord record) {
        return recordMapper.updateRecord(record);
    }

    /**
     * 删除冥想记录
     * 
     * @param id 冥想记录ID
     * @return 结果
     */
    @Override
    public int deleteRecordById(Long id) {
        return recordMapper.deleteRecordById(id);
    }

    /**
     * 批量删除冥想记录
     * 
     * @param ids 需要删除的冥想记录ID
     * @return 结果
     */
    @Override
    public int deleteRecordByIds(Long[] ids) {
        return recordMapper.deleteRecordByIds(ids);
    }

    /**
     * 统计用户冥想总时长
     * 
     * @param userId 用户ID
     * @return 总时长（秒）
     */
    @Override
    public int sumUserMeditationDuration(Long userId) {
        return recordMapper.sumUserMeditationDuration(userId);
    }

    /**
     * 统计用户冥想次数
     * 
     * @param userId 用户ID
     * @return 冥想次数
     */
    @Override
    public int countUserMeditationTimes(Long userId) {
        return recordMapper.countUserMeditationTimes(userId);
    }

    /**
     * 统计用户完成的冥想次数
     * 
     * @param userId 用户ID
     * @return 完成次数
     */
    @Override
    public int countUserCompletedMeditations(Long userId) {
        return recordMapper.countUserCompletedMeditations(userId);
    }
}
