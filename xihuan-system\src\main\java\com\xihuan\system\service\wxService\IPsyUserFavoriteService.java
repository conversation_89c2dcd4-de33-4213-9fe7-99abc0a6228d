package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyFavoriteGroup;
import com.xihuan.common.core.domain.entity.PsyUserFavorite;

import java.util.List;
import java.util.Map;

/**
 * 用户收藏服务接口
 */
public interface IPsyUserFavoriteService {
    /**
     * 新增收藏
     */
    PsyUserFavorite addFavorite(PsyUserFavorite favorite);

    /**
     * 取消收藏
     */
    int removeFavorite(Long[] favoriteIds);

    /**
     * 查询用户收藏列表
     */
    List<PsyUserFavorite> getFavorites(PsyUserFavorite favorite);

    /**
     * 查询用户收藏列表(包含详细信息)
     */
    List<Map<String, Object>> getFavoritesWithDetails(PsyUserFavorite favorite);

    /**
     * 检查是否已收藏
     */
    PsyUserFavorite checkFavorites(Long userId, Integer targetType, Long targetId);

    /**
     * 兼容旧版本的检查方法
     */
    PsyUserFavorite checkFavoritesOld(Long userId, Integer targetType, Long counselorId, Long productId);

    /**
     * 更新收藏信息
     */
    int updateFavorite(PsyUserFavorite favorite);

    /**
     * 查询收藏详情
     */
    PsyUserFavorite getFavoriteById(Long favoriteId);

    /**
     * 更新收藏查看次数
     */
    int updateViewCount(Long favoriteId);

    /**
     * 查询用户收藏统计
     */
    Map<String, Object> getUserFavoriteStats(Long userId);

    /**
     * 查询目标被收藏次数
     */
    int countFavoriteByTarget(Integer targetType, Long targetId);

    /**
     * 查询用户收藏的目标ID列表
     */
    List<Long> getUserFavoriteTargetIds(Long userId, Integer targetType);

    /**
     * 查询热门收藏排行
     */
    List<Map<String, Object>> getHotFavorites(Integer targetType, Integer limit);

    /**
     * 查询用户收藏分组列表
     */
    List<PsyFavoriteGroup> getFavoriteGroups(Long userId);

    /**
     * 创建收藏分组
     */
    PsyFavoriteGroup createFavoriteGroup(PsyFavoriteGroup group);

    /**
     * 更新收藏分组
     */
    int updateFavoriteGroup(PsyFavoriteGroup group);

    /**
     * 删除收藏分组
     */
    int deleteFavoriteGroup(Long groupId);

    /**
     * 添加收藏到分组
     */
    int addFavoriteToGroup(Long favoriteId, Long groupId);

    /**
     * 从分组中移除收藏
     */
    int removeFavoriteFromGroup(Long favoriteId, Long groupId);

    /**
     * 查询分组内收藏列表
     */
    List<Map<String, Object>> getGroupFavorites(Long groupId, Long userId);
}