<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyMeditationMapper">

    <!-- 基础冥想字段映射 -->
    <resultMap id="BaseResultMap" type="PsyMeditation">
        <id column="id" property="id"/>
        <result column="title" property="title"/>
        <result column="category_id" property="categoryId"/>
        <result column="description" property="description"/>
        <result column="audio_url" property="audioUrl"/>
        <result column="cover_image" property="coverImage"/>
        <result column="duration" property="duration"/>
        <result column="difficulty_level" property="difficultyLevel"/>
        <result column="narrator" property="narrator"/>
        <result column="price" property="price"/>
        <result column="is_free" property="isFree"/>
        <result column="play_count" property="playCount"/>
        <result column="rating_avg" property="ratingAvg"/>
        <result column="rating_count" property="ratingCount"/>
        <result column="status" property="status"/>
        <result column="tags" property="tags"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 冥想列表映射（包含分类信息） -->
    <resultMap id="MeditationListMap" type="PsyMeditation" extends="BaseResultMap">
        <collection property="categories" ofType="PsyCategory">
            <id column="rel_category_id" property="categoryId"/>
            <result column="rel_category_name" property="categoryName"/>
            <result column="rel_parent_id" property="parentId"/>
            <result column="rel_order_num" property="orderNum"/>
        </collection>
    </resultMap>

    <!-- 冥想详情映射（包含分类等信息） -->
    <resultMap id="MeditationWithDetailsMap" type="PsyMeditation" extends="BaseResultMap">
        <collection property="categories" ofType="PsyCategory">
            <id column="rel_category_id" property="categoryId"/>
            <result column="rel_category_name" property="categoryName"/>
            <result column="rel_parent_id" property="parentId"/>
            <result column="rel_order_num" property="orderNum"/>
            <result column="rel_category_status" property="status"/>
        </collection>
    </resultMap>

    <!-- 查询冥想列表 -->
    <select id="selectMeditationList" parameterType="PsyMeditation" resultMap="MeditationListMap">
        SELECT DISTINCT
            m.id,
            m.title,
            m.description,
            m.audio_url,
            m.cover_image,
            m.duration,
            m.difficulty_level,
            m.narrator,
            m.price,
            m.is_free,
            m.play_count,
            m.rating_avg,
            m.rating_count,
            m.status,
            m.tags,
            m.del_flag,
            m.create_by,
            m.create_time,
            m.update_by,
            m.update_time,
            m.remark,
            m.category_id,
            cat.category_id AS rel_category_id,
            cat.category_name AS rel_category_name,
            cat.parent_id AS rel_parent_id,
            cat.order_num AS rel_order_num
        FROM psy_meditation m
        LEFT JOIN psy_meditation_category_rel mcr ON m.id = mcr.meditation_id
        LEFT JOIN psy_category cat ON mcr.category_id = cat.category_id
        <where>
            m.del_flag = 0
            <if test="title != null and title != ''">
                AND m.title LIKE CONCAT('%', #{title}, '%')
            </if>
            <if test="status != null">
                AND m.status = #{status}
            </if>
            <if test="isFree != null">
                AND m.is_free = #{isFree}
            </if>
            <if test="difficultyLevel != null">
                AND m.difficulty_level = #{difficultyLevel}
            </if>
            <if test="narrator != null and narrator != ''">
                AND m.narrator LIKE CONCAT('%', #{narrator}, '%')
            </if>
            <if test="categoryIds != null and categoryIds.size() > 0">
                AND mcr.category_id IN
                <foreach collection="categoryIds" item="categoryId" open="(" separator="," close=")">
                    #{categoryId}
                </foreach>
            </if>
            <if test="params.beginPrice != null and params.endPrice != null">
                AND m.price BETWEEN #{params.beginPrice} AND #{params.endPrice}
            </if>
        </where>
        ORDER BY m.id DESC
    </select>

    <!-- 查询冥想详情 -->
    <select id="selectMeditationWithDetails" resultMap="MeditationWithDetailsMap">
        SELECT
            m.*,
            rel_cat.category_id AS rel_category_id,
            rel_cat.category_name AS rel_category_name,
            rel_cat.parent_id AS rel_parent_id,
            rel_cat.order_num AS rel_order_num,
            rel_cat.status AS rel_category_status
        FROM psy_meditation m
        LEFT JOIN psy_meditation_category_rel mcr ON m.id = mcr.meditation_id
        LEFT JOIN psy_category rel_cat ON mcr.category_id = rel_cat.category_id
        WHERE m.id = #{id} AND m.del_flag = 0
    </select>

    <!-- 根据ID查询冥想 -->
    <select id="selectMeditationById" resultMap="BaseResultMap">
        SELECT * FROM psy_meditation WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据分类ID查询冥想列表 -->
    <select id="selectMeditationsByCategoryId" resultMap="MeditationListMap">
        SELECT DISTINCT
            m.*,
            cat.category_id AS rel_category_id,
            cat.category_name AS rel_category_name,
            cat.parent_id AS rel_parent_id,
            cat.order_num AS rel_order_num
        FROM psy_meditation m
        INNER JOIN psy_meditation_category_rel mcr ON m.id = mcr.meditation_id
        LEFT JOIN psy_category cat ON mcr.category_id = cat.category_id
        WHERE mcr.category_id = #{categoryId} AND m.del_flag = 0 AND m.status = 1
        ORDER BY m.id DESC
    </select>

    <!-- 新增冥想 -->
    <insert id="insertMeditation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_meditation (
            title, description, audio_url, cover_image, duration,
            difficulty_level, narrator, price, is_free, play_count, rating_avg,
            rating_count, status, tags, del_flag, create_by, create_time,
            update_by, update_time, remark
        ) VALUES (
            #{title}, #{description}, #{audioUrl}, #{coverImage}, #{duration},
            #{difficultyLevel}, #{narrator}, #{price}, #{isFree}, #{playCount}, #{ratingAvg},
            #{ratingCount}, #{status}, #{tags}, #{delFlag}, #{createBy}, #{createTime},
            #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <!-- 修改冥想 -->
    <update id="updateMeditation" parameterType="PsyMeditation">
        UPDATE psy_meditation
        <set>
            <if test="title != null">title = #{title},</if>
            <if test="description != null">description = #{description},</if>
            <if test="audioUrl != null">audio_url = #{audioUrl},</if>
            <if test="coverImage != null">cover_image = #{coverImage},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="difficultyLevel != null">difficulty_level = #{difficultyLevel},</if>
            <if test="narrator != null">narrator = #{narrator},</if>
            <if test="price != null">price = #{price},</if>
            <if test="isFree != null">is_free = #{isFree},</if>
            <if test="playCount != null">play_count = #{playCount},</if>
            <if test="ratingAvg != null">rating_avg = #{ratingAvg},</if>
            <if test="ratingCount != null">rating_count = #{ratingCount},</if>
            <if test="status != null">status = #{status},</if>
            <if test="tags != null">tags = #{tags},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除冥想 -->
    <update id="deleteMeditationById">
        UPDATE psy_meditation SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除冥想 -->
    <update id="deleteMeditationByIds">
        UPDATE psy_meditation SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量插入冥想分类关联 -->
    <insert id="batchInsertMeditationCategories">
        INSERT INTO psy_meditation_category_rel (meditation_id, category_id) VALUES
        <foreach collection="categoryIds" item="categoryId" separator=",">
            (#{meditationId}, #{categoryId})
        </foreach>
    </insert>

    <!-- 删除冥想分类关联 -->
    <delete id="deleteMeditationCategories">
        DELETE FROM psy_meditation_category_rel WHERE meditation_id = #{meditationId}
    </delete>

    <!-- 批量删除冥想分类关联 -->
    <delete id="deleteMeditationCategoriesByIds">
        DELETE FROM psy_meditation_category_rel
        WHERE meditation_id IN
        <foreach item="meditationId" collection="array" open="(" separator="," close=")">
            #{meditationId}
        </foreach>
    </delete>

    <!-- 更新冥想评分信息 -->
    <update id="updateMeditationRating">
        UPDATE psy_meditation
        SET rating_avg = #{ratingAvg},
            rating_count = #{ratingCount},
            update_time = NOW()
        WHERE id = #{meditationId}
    </update>

    <!-- 增加冥想播放次数 -->
    <update id="incrementPlayCount">
        UPDATE psy_meditation
        SET play_count = play_count + 1,
            update_time = NOW()
        WHERE id = #{meditationId}
    </update>

</mapper>
