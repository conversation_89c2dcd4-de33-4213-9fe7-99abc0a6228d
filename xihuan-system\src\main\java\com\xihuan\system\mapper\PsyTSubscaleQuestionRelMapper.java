package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTSubscaleQuestionRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 分量表题目关系Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyTSubscaleQuestionRelMapper {
    
    /**
     * 查询分量表题目关系列表
     * 
     * @param rel 关系信息
     * @return 关系集合
     */
    List<PsyTSubscaleQuestionRel> selectRelList(PsyTSubscaleQuestionRel rel);

    /**
     * 根据分量表ID和题目ID查询关系
     * 
     * @param subscaleId 分量表ID
     * @param questionId 题目ID
     * @return 关系信息
     */
    PsyTSubscaleQuestionRel selectRelByIds(@Param("subscaleId") Long subscaleId, 
                                          @Param("questionId") Long questionId);

    /**
     * 根据分量表ID查询题目关系列表
     * 
     * @param subscaleId 分量表ID
     * @return 关系集合
     */
    List<PsyTSubscaleQuestionRel> selectRelsBySubscaleId(Long subscaleId);

    /**
     * 根据题目ID查询分量表关系列表
     * 
     * @param questionId 题目ID
     * @return 关系集合
     */
    List<PsyTSubscaleQuestionRel> selectRelsByQuestionId(Long questionId);

    /**
     * 新增分量表题目关系
     * 
     * @param rel 关系信息
     * @return 结果
     */
    int insertRel(PsyTSubscaleQuestionRel rel);

    /**
     * 批量新增分量表题目关系
     * 
     * @param rels 关系列表
     * @return 结果
     */
    int batchInsertRels(List<PsyTSubscaleQuestionRel> rels);

    /**
     * 修改分量表题目关系
     * 
     * @param rel 关系信息
     * @return 结果
     */
    int updateRel(PsyTSubscaleQuestionRel rel);

    /**
     * 删除分量表题目关系
     * 
     * @param subscaleId 分量表ID
     * @param questionId 题目ID
     * @return 结果
     */
    int deleteRel(@Param("subscaleId") Long subscaleId, @Param("questionId") Long questionId);

    /**
     * 根据分量表ID删除关系
     * 
     * @param subscaleId 分量表ID
     * @return 结果
     */
    int deleteRelsBySubscaleId(Long subscaleId);

    /**
     * 根据题目ID删除关系
     * 
     * @param questionId 题目ID
     * @return 结果
     */
    int deleteRelsByQuestionId(Long questionId);

    /**
     * 根据量表ID删除关系
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteRelsByScaleId(Long scaleId);

    /**
     * 批量删除分量表题目关系
     * 
     * @param rels 关系列表
     * @return 结果
     */
    int batchDeleteRels(List<PsyTSubscaleQuestionRel> rels);

    /**
     * 检查关系是否存在
     * 
     * @param subscaleId 分量表ID
     * @param questionId 题目ID
     * @return 数量
     */
    int checkRelExists(@Param("subscaleId") Long subscaleId, @Param("questionId") Long questionId);

    /**
     * 统计分量表题目数量
     * 
     * @param subscaleId 分量表ID
     * @return 数量
     */
    int countQuestionsBySubscaleId(Long subscaleId);

    /**
     * 统计题目所属分量表数量
     * 
     * @param questionId 题目ID
     * @return 数量
     */
    int countSubscalesByQuestionId(Long questionId);

    /**
     * 查询分量表权重统计
     * 
     * @param subscaleId 分量表ID
     * @return 统计信息
     */
    Map<String, Object> selectSubscaleWeightStats(Long subscaleId);

    /**
     * 查询题目权重信息
     * 
     * @param questionId 题目ID
     * @return 权重信息
     */
    List<Map<String, Object>> selectQuestionWeightInfo(Long questionId);

    /**
     * 更新关系权重
     * 
     * @param subscaleId 分量表ID
     * @param questionId 题目ID
     * @param weight 权重
     * @return 结果
     */
    int updateRelWeight(@Param("subscaleId") Long subscaleId, 
                       @Param("questionId") Long questionId, 
                       @Param("weight") BigDecimal weight);

    /**
     * 批量更新关系权重
     * 
     * @param rels 关系列表
     * @return 结果
     */
    int batchUpdateRelWeights(List<PsyTSubscaleQuestionRel> rels);

    /**
     * 复制关系到新分量表
     * 
     * @param sourceSubscaleId 源分量表ID
     * @param targetSubscaleId 目标分量表ID
     * @return 结果
     */
    int copyRelsToSubscale(@Param("sourceSubscaleId") Long sourceSubscaleId, 
                          @Param("targetSubscaleId") Long targetSubscaleId);

    /**
     * 查询分量表题目权重分布
     * 
     * @param subscaleId 分量表ID
     * @return 分布信息
     */
    List<Map<String, Object>> selectSubscaleQuestionWeightDistribution(Long subscaleId);

    /**
     * 查询题目在各分量表中的权重
     * 
     * @param questionId 题目ID
     * @return 权重信息
     */
    List<Map<String, Object>> selectQuestionSubscaleWeights(Long questionId);

    /**
     * 重置分量表所有题目权重为默认值
     * 
     * @param subscaleId 分量表ID
     * @return 结果
     */
    int resetSubscaleQuestionWeights(Long subscaleId);

    /**
     * 查询分量表题目详情（包含权重）
     * 
     * @param subscaleId 分量表ID
     * @return 题目详情列表
     */
    List<Map<String, Object>> selectSubscaleQuestionDetails(Long subscaleId);
}
