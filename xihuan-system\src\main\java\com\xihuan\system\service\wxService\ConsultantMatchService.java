package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.system.mapper.PsyConsultantMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;

/**
 * 咨询师匹配服务
 */
@Service
@RequiredArgsConstructor
public class ConsultantMatchService {
    
    private final PsyConsultantMapper consultantMapper;

    /**
     * 根据用户选择的选项筛选咨询师
     * @param optionIds 用户选择的选项ID列表
     * @return 匹配的咨询师列表
     */
    public List<PsyConsultant> matchConsultants(List<Long> optionIds) {
        if (optionIds == null || optionIds.isEmpty()) {
            return Collections.emptyList();
        }
        
        // 查询匹配所有选项的咨询师
        return consultantMapper.selectByOptionIds(optionIds, optionIds.size());
    }
} 