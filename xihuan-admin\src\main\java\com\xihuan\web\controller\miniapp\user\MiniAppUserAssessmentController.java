package com.xihuan.web.controller.miniapp.user;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyAssessmentReview;
import com.xihuan.common.core.domain.entity.PsyAssessmentScale;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;
import com.xihuan.system.service.IPsyAssessmentOrderService;
import com.xihuan.system.service.IPsyAssessmentRecordService;
import com.xihuan.system.service.IPsyAssessmentReviewService;
import com.xihuan.system.service.IPsyAssessmentScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 小程序用户端心理测评Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/assessment")
public class MiniAppUserAssessmentController extends BaseController {
    
    @Autowired
    private IPsyAssessmentScaleService scaleService;

    @Autowired
    private IPsyAssessmentRecordService recordService;

    @Autowired
    private IPsyAssessmentReviewService reviewService;

    @Autowired
    private IPsyAssessmentOrderService orderService;

    /**
     * 查询量表列表
     */
    @GetMapping("/scale/list")
    public TableDataInfo listScales(PsyAssessmentScale scale) {
        startPage();
        // 只查询已发布的量表
        scale.setStatus(PsyAssessmentScale.STATUS_PUBLISHED);
        List<PsyAssessmentDTO.ScaleDTO> list = scaleService.getScaleListDTO(scale, getUserId());
        return getDataTable(list);
    }

    /**
     * 获取量表详情
     */
    @GetMapping("/scale/{id}")
    public AjaxResult getScaleDetail(@PathVariable("id") Long id) {
        // 更新查看次数
        scaleService.updateViewCount(id);
        
        PsyAssessmentDTO.ScaleDTO scale = scaleService.getScaleDTO(id, getUserId());
        if (scale == null) {
            return error("量表不存在或已下架");
        }
        
        return success(scale);
    }

    /**
     * 搜索量表
     */
    @GetMapping("/scale/search")
    public TableDataInfo searchScales(@RequestParam(value = "keyword", required = false) String keyword,
                                     @RequestParam(value = "categoryId", required = false) Long categoryId,
                                     @RequestParam(value = "difficultyLevel", required = false) Integer difficultyLevel,
                                     @RequestParam(value = "isFree", required = false) Integer isFree) {
        startPage();
        List<PsyAssessmentScale> list = scaleService.searchScales(keyword, categoryId, difficultyLevel, isFree);
        
        // 如果有关键词，更新搜索次数
        if (StringUtils.isNotEmpty(keyword)) {
            for (PsyAssessmentScale scale : list) {
                scaleService.updateSearchCount(scale.getId());
            }
        }
        
        return getDataTable(list);
    }

    /**
     * 查询热门量表
     */
    @GetMapping("/scale/hot")
    public AjaxResult getHotScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<PsyAssessmentScale> list = scaleService.selectHotScales(limit);
        return success(list);
    }

    /**
     * 查询推荐量表
     */
    @GetMapping("/scale/recommend")
    public AjaxResult getRecommendScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<PsyAssessmentScale> list = scaleService.selectRecommendScales(getUserId(), limit);
        return success(list);
    }

    /**
     * 查询免费量表
     */
    @GetMapping("/scale/free")
    public AjaxResult getFreeScales() {
        List<PsyAssessmentScale> list = scaleService.selectFreeScales();
        return success(list);
    }

    /**
     * 查询相似量表
     */
    @GetMapping("/scale/similar/{scaleId}")
    public AjaxResult getSimilarScales(@PathVariable("scaleId") Long scaleId, @RequestParam(value = "limit", defaultValue = "5") Integer limit) {
        List<PsyAssessmentScale> list = scaleService.selectSimilarScales(scaleId, limit);
        return success(list);
    }

    /**
     * 开始测评
     */
    @Log(title = "心理测评", businessType = BusinessType.INSERT)
    @PostMapping("/test/start")
    public AjaxResult startTest(@RequestBody @Valid PsyAssessmentDTO.StartTestRequest request) {
        // 检查量表是否需要购买
        PsyAssessmentScale scale = scaleService.selectScaleById(request.getScaleId());
        if (scale == null || !scale.isPublished()) {
            return error("量表不存在或未发布");
        }

        // 如果是付费量表，检查是否已购买
        if (!scale.isFreeScale() && !orderService.checkUserPurchased(getUserId(), request.getScaleId())) {
            return error("请先购买该量表");
        }

        PsyAssessmentRecord record = recordService.startTest(getUserId(), request.getScaleId(), request.getIsAnonymous());

        // 更新量表测试次数
        scaleService.updateTestCount(request.getScaleId());

        return success(record);
    }

    /**
     * 提交答案
     */
    @Log(title = "心理测评", businessType = BusinessType.UPDATE)
    @PostMapping("/test/submit")
    public AjaxResult submitAnswer(@RequestBody @Valid PsyAssessmentDTO.SubmitAnswerRequest request) {
        int result = recordService.submitAnswer(
            request.getSessionId(), 
            request.getQuestionId(), 
            request.getOptionId(), 
            request.getAnswerText(), 
            request.getTimeSpent()
        );
        
        return toAjax(result);
    }

    /**
     * 完成测评
     */
    @Log(title = "心理测评", businessType = BusinessType.UPDATE)
    @PostMapping("/test/complete")
    public AjaxResult completeTest(@RequestBody @Valid PsyAssessmentDTO.CompleteTestRequest request) {
        PsyAssessmentRecord record = recordService.completeTest(request.getSessionId());
        
        if (record == null) {
            return error("测评记录不存在或已完成");
        }
        
        // 获取测评结果DTO
        PsyAssessmentDTO.RecordDTO result = recordService.getTestResultDTO(request.getSessionId());
        
        return success(result);
    }

    /**
     * 获取测评结果
     */
    @GetMapping("/test/result/{sessionId}")
    public AjaxResult getTestResult(@PathVariable("sessionId") String sessionId) {
        PsyAssessmentDTO.RecordDTO result = recordService.getTestResultDTO(sessionId);
        
        if (result == null) {
            return error("测评记录不存在或未完成");
        }
        
        return success(result);
    }

    /**
     * 查询用户测评记录列表
     */
    @GetMapping("/record/list")
    public TableDataInfo listRecords() {
        startPage();
        PsyAssessmentRecord record = new PsyAssessmentRecord();
        record.setUserId(getUserId());
        List<PsyAssessmentDTO.RecordDTO> list = recordService.getRecordListDTO(record);
        return getDataTable(list);
    }

    /**
     * 查询用户测评记录详情
     */
    @GetMapping("/record/{id}")
    public AjaxResult getRecordDetail(@PathVariable("id") Long id) {
        PsyAssessmentDTO.RecordDTO record = recordService.getRecordDTO(id);
        
        if (record == null) {
            return error("测评记录不存在");
        }
        
        // 检查是否是当前用户的记录
        if (!getUserId().equals(record.getUserId())) {
            return error("无权查看该测评记录");
        }
        
        return success(record);
    }

    /**
     * 查询用户在指定量表的测评历史
     */
    @GetMapping("/record/history/{scaleId}")
    public TableDataInfo getTestHistory(@PathVariable("scaleId") Long scaleId) {
        startPage();
        List<PsyAssessmentRecord> list = recordService.selectUserTestHistory(getUserId(), scaleId);
        return getDataTable(list);
    }

    /**
     * 提交评价
     */
    @Log(title = "心理测评", businessType = BusinessType.INSERT)
    @PostMapping("/review/submit")
    public AjaxResult submitReview(@RequestBody @Valid PsyAssessmentDTO.SubmitReviewRequest request) {
        PsyAssessmentReview review = new PsyAssessmentReview();
        review.setUserId(getUserId());
        review.setScaleId(request.getScaleId());
        review.setRecordId(request.getRecordId());
        review.setRating(request.getRating());
        review.setContent(request.getContent());
        review.setIsAnonymous(request.getIsAnonymous());
        
        int result = reviewService.insertReview(review);
        
        // 更新量表评分统计
        if (result > 0) {
            scaleService.updateRatingStats(request.getScaleId());
        }
        
        return toAjax(result);
    }

    /**
     * 查询量表评价列表
     */
    @GetMapping("/review/list/{scaleId}")
    public TableDataInfo listReviews(@PathVariable("scaleId") Long scaleId) {
        startPage();
        PsyAssessmentReview review = new PsyAssessmentReview();
        review.setScaleId(scaleId);
        review.setStatus(PsyAssessmentReview.STATUS_APPROVED); // 只查询已通过的评价
        List<PsyAssessmentReview> list = reviewService.selectReviewList(review);
        return getDataTable(list);
    }

    /**
     * 查询用户评价列表
     */
    @GetMapping("/review/user")
    public TableDataInfo listUserReviews() {
        startPage();
        PsyAssessmentReview review = new PsyAssessmentReview();
        review.setUserId(getUserId());
        List<PsyAssessmentReview> list = reviewService.selectReviewList(review);
        return getDataTable(list);
    }

    /**
     * 删除评价
     */
    @Log(title = "心理测评", businessType = BusinessType.DELETE)
    @DeleteMapping("/review/{id}")
    public AjaxResult deleteReview(@PathVariable("id") Long id) {
        PsyAssessmentReview review = reviewService.selectReviewById(id);
        
        if (review == null) {
            return error("评价不存在");
        }
        
        // 检查是否是当前用户的评价
        if (!getUserId().equals(review.getUserId())) {
            return error("无权删除该评价");
        }
        
        int result = reviewService.deleteReviewById(id);
        
        // 更新量表评分统计
        if (result > 0) {
            scaleService.updateRatingStats(review.getScaleId());
        }
        
        return toAjax(result);
    }

    /**
     * 查询用户测评统计
     */
    @GetMapping("/stats/user")
    public AjaxResult getUserTestStats() {
        return success(recordService.selectUserRecordStats(getUserId()));
    }
}
