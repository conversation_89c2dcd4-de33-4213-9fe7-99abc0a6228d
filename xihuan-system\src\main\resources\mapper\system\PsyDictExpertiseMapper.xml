<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyDictExpertiseMapper">

    <resultMap type="PsyDictExpertise" id="PsyDictExpertiseResult">
        <id property="id" column="id"/>
        <result property="parentId" column="parent_id"/>
        <result property="typeName" column="type_name"/>
        <result property="sort" column="sort"/>
        <result property="icon" column="icon"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectPsyDictExpertiseVo">
        select id, parent_id, type_name, sort, del_flag, icon, create_by, create_time, update_by, update_time, remark
        from psy_dict_expertise
    </sql>

    <select id="selectPsyDictExpertiseList" parameterType="PsyDictExpertise" resultMap="PsyDictExpertiseResult">
        <include refid="selectPsyDictExpertiseVo"/>
        <where>
            <if test="typeName != null and typeName != ''">
                AND type_name like concat('%', #{typeName}, '%')
            </if>
            <if test="parentId != null">
                AND parent_id = #{parentId}
            </if>
            AND del_flag = '0'
        </where>
        order by sort
    </select>

    <select id="selectPsyDictExpertiseById" parameterType="Long" resultMap="PsyDictExpertiseResult">
        <include refid="selectPsyDictExpertiseVo"/>
        where id = #{id} AND del_flag = '0'
    </select>

    <select id="selectParentExpertises" resultMap="PsyDictExpertiseResult">
        <include refid="selectPsyDictExpertiseVo"/>
        where parent_id is null AND del_flag = '0'
        order by sort
    </select>

    <select id="selectChildrenByParentId" parameterType="Long" resultMap="PsyDictExpertiseResult">
        <include refid="selectPsyDictExpertiseVo"/>
        where parent_id = #{parentId} AND del_flag = '0'
        order by sort
    </select>

    <select id="selectAllChildExpertises" resultMap="PsyDictExpertiseResult">
        <include refid="selectPsyDictExpertiseVo"/>
        where parent_id is not null AND del_flag = '0'
        order by sort
    </select>

    <insert id="insertPsyDictExpertise" parameterType="PsyDictExpertise" useGeneratedKeys="true" keyProperty="id">
        insert into psy_dict_expertise
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="parentId != null">parent_id,</if>
            <if test="typeName != null">type_name,</if>
            <if test="sort != null">sort,</if>
            <if test="icon != null">icon,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="parentId != null">#{parentId},</if>
            <if test="typeName != null">#{typeName},</if>
            <if test="sort != null">#{sort},</if>
            <if test="icon != null">#{icon},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>

    <update id="updatePsyDictExpertise" parameterType="PsyDictExpertise">
        update psy_dict_expertise
        <trim prefix="SET" suffixOverrides=",">
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="typeName != null">type_name = #{typeName},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="icon != null">icon = #{icon},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where id = #{id}
    </update>

    <delete id="deletePsyDictExpertiseById" parameterType="Long">
        update psy_dict_expertise set del_flag = '2' where id = #{id}
    </delete>

    <delete id="deletePsyDictExpertiseByIds" parameterType="Long">
        update psy_dict_expertise set del_flag = '2' where id in
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

</mapper> 