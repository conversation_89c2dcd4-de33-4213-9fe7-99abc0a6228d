package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.kangnili.KangniliQuestion;

import java.util.List;

public interface KangniliQuestionMapper {
    List<KangniliQuestion> selectQuestionList(KangniliQuestion question);

    int insertQuestion(KangniliQuestion question);

    int updateQuestion(KangniliQuestion question);

    int deleteQuestionByIds(Long[] ids);

    // 在KangniliQuestionMapper接口添加
    KangniliQuestion selectQuestionById(Long id);

    // 查询全部题干和选项
    List<KangniliQuestion> selectQuestionListWithOptions();
}