package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTEnterprise;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyTEnterpriseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 企业信息Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/enterprise")
public class PsyTEnterpriseController extends BaseController {
    
    @Autowired
    private IPsyTEnterpriseService enterpriseService;

    /**
     * 查询企业列表
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyTEnterprise enterprise) {
        startPage();
        List<PsyTEnterprise> list = enterpriseService.selectEnterpriseList(enterprise);
        return getDataTable(list);
    }

    /**
     * 导出企业列表
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:export')")
    @Log(title = "企业信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyTEnterprise enterprise) {
        List<PsyTEnterprise> list = enterpriseService.selectEnterpriseList(enterprise);
        ExcelUtil<PsyTEnterprise> util = new ExcelUtil<>(PsyTEnterprise.class);
        util.exportExcel(response, list, "企业信息数据");
    }

    /**
     * 获取企业详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(enterpriseService.selectEnterpriseWithDetails(id));
    }

    /**
     * 根据企业编码查询企业
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:query')")
    @GetMapping("/code/{code}")
    public AjaxResult getByCode(@PathVariable("code") String code) {
        return success(enterpriseService.selectEnterpriseByCode(code));
    }

    /**
     * 新增企业
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:add')")
    @Log(title = "企业信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyTEnterprise enterprise) {
        return toAjax(enterpriseService.insertEnterprise(enterprise));
    }

    /**
     * 修改企业
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyTEnterprise enterprise) {
        return toAjax(enterpriseService.updateEnterprise(enterprise));
    }

    /**
     * 删除企业
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:remove')")
    @Log(title = "企业信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(enterpriseService.deleteEnterpriseByIds(ids));
    }

    /**
     * 查询启用的企业列表
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/enabled")
    public AjaxResult getEnabledEnterprises() {
        List<PsyTEnterprise> list = enterpriseService.selectEnabledEnterprises();
        return success(list);
    }

    /**
     * 查询即将过期的企业列表
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/expiring")
    public AjaxResult getExpiringEnterprises(@RequestParam(defaultValue = "30") Integer days) {
        List<PsyTEnterprise> list = enterpriseService.selectExpiringEnterprises(days);
        return success(list);
    }

    /**
     * 查询已过期的企业列表
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/expired")
    public AjaxResult getExpiredEnterprises() {
        List<PsyTEnterprise> list = enterpriseService.selectExpiredEnterprises();
        return success(list);
    }

    /**
     * 搜索企业
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/search")
    public TableDataInfo searchEnterprises(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) Integer enterpriseType,
            @RequestParam(required = false) String scale,
            @RequestParam(required = false) Integer status) {
        startPage();
        List<PsyTEnterprise> list = enterpriseService.searchEnterprises(keyword, enterpriseType, scale, status);
        return getDataTable(list);
    }

    /**
     * 更新企业测评使用次数
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping("/{id}/usage-count")
    public AjaxResult updateUsageCount(@PathVariable("id") Long id, @RequestParam Integer count) {
        return toAjax(enterpriseService.updateAssessmentUsageCount(id, count));
    }

    /**
     * 增加企业测评使用次数
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/increment-usage")
    public AjaxResult incrementUsageCount(@PathVariable("id") Long id, @RequestParam Integer increment) {
        return toAjax(enterpriseService.incrementAssessmentUsageCount(id, increment));
    }

    /**
     * 批量更新企业状态
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PutMapping("/batch-status")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params) {
        Long[] ids = (Long[]) params.get("ids");
        Integer status = (Integer) params.get("status");
        return toAjax(enterpriseService.batchUpdateEnterpriseStatus(ids, status));
    }

    /**
     * 企业认证
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/certify")
    public AjaxResult certify(@PathVariable("id") Long id) {
        return toAjax(enterpriseService.certifyEnterprise(id));
    }

    /**
     * 企业续费
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/renew")
    public AjaxResult renew(
            @PathVariable("id") Long id,
            @RequestParam Integer months,
            @RequestParam Integer assessmentCount) {
        return toAjax(enterpriseService.renewEnterprise(id, months, assessmentCount));
    }

    /**
     * 企业升级
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/upgrade")
    public AjaxResult upgrade(@PathVariable("id") Long id, @RequestParam String servicePackage) {
        return toAjax(enterpriseService.upgradeEnterprise(id, servicePackage));
    }

    /**
     * 企业降级
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/downgrade")
    public AjaxResult downgrade(@PathVariable("id") Long id, @RequestParam String servicePackage) {
        return toAjax(enterpriseService.downgradeEnterprise(id, servicePackage));
    }

    /**
     * 冻结企业
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/freeze")
    public AjaxResult freeze(@PathVariable("id") Long id, @RequestParam String reason) {
        return toAjax(enterpriseService.freezeEnterprise(id, reason));
    }

    /**
     * 解冻企业
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/{id}/unfreeze")
    public AjaxResult unfreeze(@PathVariable("id") Long id) {
        return toAjax(enterpriseService.unfreezeEnterprise(id));
    }

    /**
     * 查询企业统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/stats")
    public AjaxResult getStats() {
        Map<String, Object> stats = enterpriseService.selectEnterpriseStats();
        return success(stats);
    }

    /**
     * 查询企业类型统计
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/type-stats")
    public AjaxResult getTypeStats() {
        List<Map<String, Object>> stats = enterpriseService.selectEnterpriseTypeStats();
        return success(stats);
    }

    /**
     * 查询企业规模统计
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/scale-stats")
    public AjaxResult getScaleStats() {
        List<Map<String, Object>> stats = enterpriseService.selectEnterpriseScaleStats();
        return success(stats);
    }

    /**
     * 查询企业测评使用统计
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/{id}/usage-stats")
    public AjaxResult getUsageStats(@PathVariable("id") Long id) {
        Map<String, Object> stats = enterpriseService.selectEnterpriseUsageStats(id);
        return success(stats);
    }

    /**
     * 查询企业测评趋势
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/{id}/usage-trend")
    public AjaxResult getUsageTrend(@PathVariable("id") Long id, @RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> trend = enterpriseService.selectEnterpriseUsageTrend(id, days);
        return success(trend);
    }

    /**
     * 查询企业合同到期提醒
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/contract-expiry-reminder")
    public AjaxResult getContractExpiryReminder(@RequestParam(defaultValue = "30") Integer days) {
        List<PsyTEnterprise> list = enterpriseService.selectContractExpiryReminder(days);
        return success(list);
    }

    /**
     * 查询企业测评额度预警
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/quota-warning")
    public AjaxResult getQuotaWarning(@RequestParam(defaultValue = "80") Integer threshold) {
        List<PsyTEnterprise> list = enterpriseService.selectAssessmentQuotaWarning(threshold);
        return success(list);
    }

    /**
     * 查询企业行业分布
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/industry-distribution")
    public AjaxResult getIndustryDistribution() {
        List<Map<String, Object>> distribution = enterpriseService.selectEnterpriseIndustryDistribution();
        return success(distribution);
    }

    /**
     * 查询企业地区分布
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/region-distribution")
    public AjaxResult getRegionDistribution() {
        List<Map<String, Object>> distribution = enterpriseService.selectEnterpriseRegionDistribution();
        return success(distribution);
    }

    /**
     * 查询企业服务套餐统计
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/service-package-stats")
    public AjaxResult getServicePackageStats() {
        List<Map<String, Object>> stats = enterpriseService.selectServicePackageStats();
        return success(stats);
    }

    /**
     * 查询企业活跃度统计
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/activity-stats")
    public AjaxResult getActivityStats(@RequestParam(defaultValue = "30") Integer days) {
        List<Map<String, Object>> stats = enterpriseService.selectEnterpriseActivityStats(days);
        return success(stats);
    }

    /**
     * 查询企业收入统计
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/revenue-stats")
    public AjaxResult getRevenueStats(
            @RequestParam(required = false) Integer year,
            @RequestParam(required = false) Integer month) {
        Map<String, Object> stats = enterpriseService.selectEnterpriseRevenueStats(year, month);
        return success(stats);
    }

    /**
     * 查询企业续费提醒
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:list')")
    @GetMapping("/renewal-reminder")
    public AjaxResult getRenewalReminder(@RequestParam(defaultValue = "30") Integer days) {
        List<PsyTEnterprise> list = enterpriseService.selectRenewalReminder(days);
        return success(list);
    }

    /**
     * 自动更新过期企业状态
     */
    @PreAuthorize("@ss.hasPermi('system:enterprise:edit')")
    @Log(title = "企业信息", businessType = BusinessType.UPDATE)
    @PostMapping("/auto-update-expired")
    public AjaxResult autoUpdateExpiredStatus() {
        int count = enterpriseService.autoUpdateExpiredEnterpriseStatus();
        return success("更新了 " + count + " 个过期企业状态");
    }

    /**
     * 检查企业编码唯一性
     */
    @GetMapping("/checkCodeUnique")
    public AjaxResult checkCodeUnique(
            @RequestParam String enterpriseCode,
            @RequestParam(required = false) Long excludeId) {
        boolean unique = enterpriseService.checkEnterpriseCodeUnique(enterpriseCode, excludeId);
        return success(unique);
    }
}
