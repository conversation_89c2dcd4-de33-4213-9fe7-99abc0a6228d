package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.consultant.PsyConsultant;
import com.xihuan.common.core.domain.entity.PsyMatchQuestion;
import com.xihuan.common.core.domain.entity.PsyMatchQuestionOption;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 极速匹配问题Mapper接口
 */
@Mapper
public interface PsyMatchQuestionMapper {
    
    /**
     * 查询问题列表（包含选项，支持搜索）
     */
    List<PsyMatchQuestion> selectQuestionList(@Param("title") String title);
    
    /**
     * 新增问题
     */
    int insert(PsyMatchQuestion question);
    
    /**
     * 修改问题
     */
    int update(PsyMatchQuestion question);
    
    /**
     * 删除问题
     */
    int deleteById(Long id);
    
    /**
     * 根据ID查询问题
     */
    PsyMatchQuestion selectById(Long id);

    /**
     * 根据问题ID删除所有选项
     */
    int deleteQuestionOptionsByQuestionId(Long questionId);

    /**
     * 插入单个问题选项
     */
    int insertOption(PsyMatchQuestionOption option);

    /**
     * 批量插入问题选项
     */
    int batchInsertOptions(@Param("questionId") Long questionId, @Param("options") List<PsyMatchQuestionOption> options);
    
    /**
     * 批量插入选项咨询师关联
     */
    int batchInsertOptionConsultants(@Param("optionId") Long optionId, @Param("consultantIds") List<Long> consultantIds);
    
    /**
     * 删除选项的所有咨询师关联
     */
    int deleteOptionConsultants(Long optionId);
    
    /**
     * 查询选项关联的咨询师ID列表
     */
    List<Long> selectConsultantIdsByOptionId(Long optionId);

    /**
     * 根据选项ID和咨询领域ID筛选咨询师
     */
    List<PsyConsultant> selectByOptionIds(
        @Param("optionIds") List<Long> optionIds,
        @Param("optionCount") Integer optionCount,
        @Param("expertiseIds") List<Long> expertiseIds,
        @Param("expertiseCount") Integer expertiseCount
    );
} 