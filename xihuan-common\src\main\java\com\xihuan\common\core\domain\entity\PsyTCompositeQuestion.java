package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;

/**
 * 复合题特殊计分表 psy_t_composite_question
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyTCompositeQuestion extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 复合题ID */
    @Excel(name = "复合题ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 关联psy_t_question.id */
    @Excel(name = "题目ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    /** 子项编号 */
    @Excel(name = "子项编号")
    @NotBlank(message = "子项编号不能为空")
    @Size(max = 20, message = "子项编号不能超过20个字符")
    private String subitemNo;

    /** 子项内容 */
    @Excel(name = "子项内容")
    @NotBlank(message = "子项内容不能为空")
    @Size(max = 200, message = "子项内容不能超过200个字符")
    private String content;

    /** 计分权重 */
    @Excel(name = "计分权重")
    @NotNull(message = "计分权重不能为空")
    @Min(value = 1, message = "计分权重不能小于1")
    private Integer scoreWeight;

    // 关联对象
    /** 题目信息 */
    private PsyTQuestion question;

    // 扩展字段
    /** 题目内容 */
    private String questionContent;

    /** 题目序号 */
    private Integer questionNo;

    /** 量表ID */
    private Long scaleId;

    /** 量表名称 */
    private String scaleName;

    /** 用户答案 */
    private String userAnswer;

    /** 用户得分 */
    private Integer userScore;

    /** 是否已答题 */
    private Boolean answered;

    // 常量定义
    /** 默认计分权重 */
    public static final Integer DEFAULT_SCORE_WEIGHT = 1;

    /**
     * 获取子项完整编号
     */
    public String getFullSubitemNo() {
        if (questionNo != null && subitemNo != null) {
            return questionNo + "." + subitemNo;
        }
        return subitemNo;
    }

    /**
     * 计算加权得分
     */
    public Integer calculateWeightedScore(Integer baseScore) {
        if (baseScore == null || scoreWeight == null) return 0;
        return baseScore * scoreWeight;
    }

    /**
     * 是否默认权重
     */
    public boolean isDefaultWeight() {
        return DEFAULT_SCORE_WEIGHT.equals(scoreWeight);
    }

    /**
     * 获取权重描述
     */
    public String getWeightDesc() {
        if (scoreWeight == null) return "1";
        if (scoreWeight == 1) return "标准";
        return "×" + scoreWeight;
    }
}
