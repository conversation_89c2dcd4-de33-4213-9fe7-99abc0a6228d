package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.domain.SysConfig;
import com.xihuan.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 时间槽配置Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/timeSlotConfig")
public class PsyTimeSlotConfigController extends BaseController {
    
    @Autowired
    private ISysConfigService configService;

    /**
     * 获取延后过期配置
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlotConfig:query')")
    @GetMapping("/delayExpiration")
    public AjaxResult getDelayExpirationConfig() {
        Map<String, Object> config = new HashMap<>();
        
        // 获取是否启用延后过期
        String enabledValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
        boolean enabled = "true".equalsIgnoreCase(enabledValue) || "1".equals(enabledValue);
        
        // 获取延后小时数
        String hoursValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
        int hours = 2; // 默认2小时
        try {
            hours = Integer.parseInt(hoursValue);
        } catch (Exception e) {
            // 使用默认值
        }
        
        config.put("enabled", enabled);
        config.put("hours", hours);
        config.put("description", enabled ? 
            String.format("已启用延后过期功能，延后 %d 小时", hours) : 
            "未启用延后过期功能");
        
        return AjaxResult.success(config);
    }

    /**
     * 设置延后过期配置
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlotConfig:edit')")
    @Log(title = "时间槽配置", businessType = BusinessType.UPDATE)
    @PostMapping("/delayExpiration")
    public AjaxResult setDelayExpirationConfig(@RequestBody Map<String, Object> config) {
        try {
            Boolean enabled = (Boolean) config.get("enabled");
            Integer hours = (Integer) config.get("hours");
            
            if (enabled == null) {
                return AjaxResult.error("enabled参数不能为空");
            }
            
            if (hours == null || hours < 0 || hours > 24) {
                return AjaxResult.error("hours参数必须在0-24之间");
            }
            
            // 更新配置
            updateConfigByKey("psy.slot.delay.expiration.enabled", enabled.toString());
            updateConfigByKey("psy.slot.delay.expiration.hours", hours.toString());
            
            String message = enabled ? 
                String.format("延后过期功能已启用，延后时间：%d小时", hours) : 
                "延后过期功能已禁用";
                
            return AjaxResult.success(message);
        } catch (Exception e) {
            logger.error("设置延后过期配置失败", e);
            return AjaxResult.error("设置配置失败：" + e.getMessage());
        }
    }

    /**
     * 重置延后过期配置为默认值
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlotConfig:edit')")
    @Log(title = "时间槽配置", businessType = BusinessType.UPDATE)
    @PostMapping("/delayExpiration/reset")
    public AjaxResult resetDelayExpirationConfig() {
        try {
            // 重置为默认值
            updateConfigByKey("psy.slot.delay.expiration.enabled", "false");
            updateConfigByKey("psy.slot.delay.expiration.hours", "2");
            
            return AjaxResult.success("延后过期配置已重置为默认值（禁用，2小时）");
        } catch (Exception e) {
            logger.error("重置延后过期配置失败", e);
            return AjaxResult.error("重置配置失败：" + e.getMessage());
        }
    }

    /**
     * 获取所有时间槽相关配置
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlotConfig:query')")
    @GetMapping("/all")
    public AjaxResult getAllTimeSlotConfigs() {
        Map<String, Object> configs = new HashMap<>();
        
        // 延后过期配置
        Map<String, Object> delayConfig = new HashMap<>();
        String enabledValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
        String hoursValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
        
        delayConfig.put("enabled", "true".equalsIgnoreCase(enabledValue) || "1".equals(enabledValue));
        delayConfig.put("hours", parseIntWithDefault(hoursValue, 2));
        
        configs.put("delayExpiration", delayConfig);
        
        // 可以在这里添加其他时间槽相关配置
        
        return AjaxResult.success(configs);
    }

    /**
     * 测试延后过期功能
     */
    @PreAuthorize("@ss.hasPermi('system:timeSlotConfig:test')")
    @PostMapping("/delayExpiration/test")
    public AjaxResult testDelayExpiration() {
        try {
            Map<String, Object> result = new HashMap<>();
            
            // 获取当前配置
            boolean enabled = isDelayExpirationEnabled();
            int hours = getDelayExpirationHours();
            
            Map<String, Object> currentConfig = new HashMap<>();
            currentConfig.put("enabled", enabled);
            currentConfig.put("hours", hours);
            result.put("currentConfig", currentConfig);
            
            // 计算过期时间
            java.time.LocalDateTime now = java.time.LocalDateTime.now();
            java.time.LocalDateTime cutoffTime = enabled ? now.minusHours(hours) : now;
            
            result.put("currentTime", now.toString());
            result.put("cutoffTime", cutoffTime.toString());
            result.put("description", enabled ? 
                String.format("当前启用延后过期，时间槽在 %s 之前的才会被标记为过期", cutoffTime) :
                "当前未启用延后过期，时间槽在当前时间之前的会被标记为过期");
            
            return AjaxResult.success(result);
        } catch (Exception e) {
            logger.error("测试延后过期功能失败", e);
            return AjaxResult.error("测试失败：" + e.getMessage());
        }
    }

    /**
     * 解析整数，失败时返回默认值
     */
    private int parseIntWithDefault(String value, int defaultValue) {
        try {
            return Integer.parseInt(value);
        } catch (Exception e) {
            return defaultValue;
        }
    }

    /**
     * 检查是否启用延后过期功能
     */
    private boolean isDelayExpirationEnabled() {
        String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
        return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
    }

    /**
     * 获取延后过期的小时数
     */
    private int getDelayExpirationHours() {
        String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
        return parseIntWithDefault(configValue, 2);
    }

    /**
     * 根据配置键更新配置值
     */
    private void updateConfigByKey(String configKey, String configValue) {
        // 查询现有配置
        SysConfig queryConfig = new SysConfig();
        queryConfig.setConfigKey(configKey);
        List<SysConfig> configList = configService.selectConfigList(queryConfig);

        if (!configList.isEmpty()) {
            // 更新现有配置
            SysConfig existingConfig = configList.get(0);
            existingConfig.setConfigValue(configValue);
            configService.updateConfig(existingConfig);
        } else {
            // 如果配置不存在，创建新配置
            SysConfig newConfig = new SysConfig();
            newConfig.setConfigKey(configKey);
            newConfig.setConfigValue(configValue);
            newConfig.setConfigName(getConfigNameByKey(configKey));
            newConfig.setConfigType("Y");
            newConfig.setRemark(getConfigRemarkByKey(configKey));
            configService.insertConfig(newConfig);
        }
    }

    /**
     * 根据配置键获取配置名称
     */
    private String getConfigNameByKey(String configKey) {
        switch (configKey) {
            case "psy.slot.delay.expiration.enabled":
                return "时间槽延后过期功能开关";
            case "psy.slot.delay.expiration.hours":
                return "时间槽延后过期小时数";
            default:
                return configKey;
        }
    }

    /**
     * 根据配置键获取配置备注
     */
    private String getConfigRemarkByKey(String configKey) {
        switch (configKey) {
            case "psy.slot.delay.expiration.enabled":
                return "是否启用时间槽延后过期功能，true-启用，false-禁用";
            case "psy.slot.delay.expiration.hours":
                return "时间槽延后过期的小时数，默认2小时";
            default:
                return "";
        }
    }
}
