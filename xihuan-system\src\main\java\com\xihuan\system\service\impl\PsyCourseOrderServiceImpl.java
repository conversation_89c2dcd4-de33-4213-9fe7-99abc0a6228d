package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyCourse;
import com.xihuan.common.core.domain.entity.PsyCourseOrder;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyCourseOrderMapper;
import com.xihuan.system.service.IPsyCourseOrderService;
import com.xihuan.system.service.IPsyCourseService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 课程订单表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyCourseOrderServiceImpl implements IPsyCourseOrderService {
    
    @Autowired
    private PsyCourseOrderMapper orderMapper;
    
    @Autowired
    private IPsyCourseService courseService;

    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    @Override
    public List<PsyCourseOrder> selectOrderList(PsyCourseOrder order) {
        return orderMapper.selectOrderList(order);
    }

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    @Override
    public PsyCourseOrder selectOrderById(Long id) {
        return orderMapper.selectOrderById(id);
    }

    /**
     * 根据订单号查询订单
     * 
     * @param orderNo 订单号
     * @return 订单信息
     */
    @Override
    public PsyCourseOrder selectOrderByOrderNo(String orderNo) {
        return orderMapper.selectOrderByOrderNo(orderNo);
    }

    /**
     * 查询订单详情（包含课程和用户信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    @Override
    public PsyCourseOrder selectOrderWithDetails(Long id) {
        return orderMapper.selectOrderWithDetails(id);
    }

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOrder(PsyCourseOrder order) {
        // 生成订单号
        if (order.getOrderNo() == null || order.getOrderNo().isEmpty()) {
            order.setOrderNo(generateOrderNo());
        }
        
        // 设置默认值
        order.setCreateTime(DateUtils.getNowDate());
        order.setDelFlag(0);
        order.setStatus(0); // 待支付
        
        // 获取课程信息设置订单价格
        if (order.getCourseId() != null) {
            PsyCourse course = courseService.selectCourseById(order.getCourseId());
            if (course != null) {
                order.setOriginalPrice(course.getPrice());
                // 如果没有设置支付金额，默认为课程价格
                if (order.getPaymentAmount() == null) {
                    order.setPaymentAmount(course.getPrice());
                }
            }
        }
        
        return orderMapper.insertOrder(order);
    }

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    @Override
    public int updateOrder(PsyCourseOrder order) {
        order.setUpdateTime(DateUtils.getNowDate());
        return orderMapper.updateOrder(order);
    }

    /**
     * 删除订单
     * 
     * @param id 订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderById(Long id) {
        return orderMapper.deleteOrderById(id);
    }

    /**
     * 批量删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    @Override
    public int deleteOrderByIds(Long[] ids) {
        return orderMapper.deleteOrderByIds(ids);
    }

    /**
     * 更新订单支付状态
     * 
     * @param orderNo 订单号
     * @param status 订单状态
     * @param paymentMethod 支付方式
     * @param transactionId 第三方交易号
     * @param paymentTime 支付时间
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrderPaymentStatus(String orderNo, Integer status, String paymentMethod, String transactionId, Date paymentTime) {
        int result = orderMapper.updateOrderPaymentStatus(orderNo, status, paymentMethod, transactionId, paymentTime);
        
        // 如果支付成功，更新课程销售数量
        if (result > 0 && status == 1) {
            PsyCourseOrder order = orderMapper.selectOrderByOrderNo(orderNo);
            if (order != null && order.getCourseId() != null) {
                courseService.incrementSalesCount(order.getCourseId(), 1);
            }
        }
        
        return result;
    }

    /**
     * 更新订单退款信息
     * 
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param refundTime 退款时间
     * @return 结果
     */
    @Override
    public int updateOrderRefund(String orderNo, BigDecimal refundAmount, Date refundTime) {
        return orderMapper.updateOrderRefund(orderNo, refundAmount, refundTime);
    }

    /**
     * 检查用户是否已购买课程
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否已购买
     */
    @Override
    public boolean checkUserPurchased(Long userId, Long courseId) {
        int count = orderMapper.checkUserPurchased(userId, courseId);
        return count > 0;
    }

    /**
     * 获取用户已购买的课程列表
     * 
     * @param userId 用户ID
     * @return 课程集合
     */
    @Override
    public List<PsyCourse> selectUserPurchasedCourses(Long userId) {
        // 查询用户已支付的订单
        PsyCourseOrder queryOrder = new PsyCourseOrder();
        queryOrder.setUserId(userId);
        queryOrder.setStatus(1); // 已支付
        List<PsyCourseOrder> orders = orderMapper.selectOrderList(queryOrder);
        
        // 获取课程信息
        List<PsyCourse> courses = new java.util.ArrayList<>();
        for (PsyCourseOrder order : orders) {
            PsyCourse course = courseService.selectCourseById(order.getCourseId());
            if (course != null) {
                courses.add(course);
            }
        }
        
        return courses;
    }

    /**
     * 生成订单号
     * 
     * @return 订单号
     */
    @Override
    public String generateOrderNo() {
        return orderMapper.generateOrderNo();
    }
}
