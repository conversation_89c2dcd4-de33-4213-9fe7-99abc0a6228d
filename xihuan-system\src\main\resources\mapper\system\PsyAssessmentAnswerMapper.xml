<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentAnswerMapper">

    <!-- 结果映射 -->
    <resultMap id="AnswerResultMap" type="PsyAssessmentAnswer">
        <id property="id" column="id"/>
        <result property="recordId" column="record_id"/>
        <result property="questionId" column="question_id"/>
        <result property="optionId" column="option_id"/>
        <result property="answerText" column="answer_text"/>
        <result property="score" column="score"/>
        <result property="answerTime" column="answer_time"/>
        <result property="timeSpent" column="time_spent"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 带详情的结果映射 -->
    <resultMap id="AnswerWithDetailsMap" type="PsyAssessmentAnswer" extends="AnswerResultMap">
        <association property="question" javaType="PsyAssessmentQuestion">
            <id property="id" column="q_id"/>
            <result property="questionNo" column="q_question_no"/>
            <result property="questionText" column="q_question_text"/>
            <result property="questionType" column="q_question_type"/>
            <result property="dimension" column="q_dimension"/>
        </association>
        <association property="option" javaType="PsyAssessmentOption">
            <id property="id" column="o_id"/>
            <result property="optionText" column="o_option_text"/>
            <result property="optionValue" column="o_option_value"/>
            <result property="score" column="o_score"/>
        </association>
    </resultMap>

    <!-- 查询答案列表 -->
    <select id="selectAnswerList" parameterType="PsyAssessmentAnswer" resultMap="AnswerResultMap">
        SELECT * FROM psy_t_answer
        WHERE del_flag = 0
        <if test="recordId != null">
            AND record_id = #{recordId}
        </if>
        <if test="questionId != null">
            AND question_id = #{questionId}
        </if>
        <if test="optionId != null">
            AND option_id = #{optionId}
        </if>
        ORDER BY record_id, question_id
    </select>

    <!-- 根据ID查询答案 -->
    <select id="selectAnswerById" parameterType="Long" resultMap="AnswerResultMap">
        SELECT * FROM psy_t_answer WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 查询答案详情 -->
    <select id="selectAnswerWithDetails" parameterType="Long" resultMap="AnswerWithDetailsMap">
        SELECT a.*, 
               q.id as q_id, q.question_no as q_question_no, q.question_text as q_question_text,
               q.question_type as q_question_type, q.dimension as q_dimension,
               o.id as o_id, o.option_text as o_option_text, o.option_value as o_option_value, o.score as o_score
        FROM psy_t_answer a
        LEFT JOIN psy_t_question q ON a.question_id = q.id
        LEFT JOIN psy_t_option o ON a.option_id = o.id
        WHERE a.id = #{id} AND a.del_flag = 0
    </select>

    <!-- 根据测评记录ID查询答案列表 -->
    <select id="selectAnswersByRecordId" parameterType="Long" resultMap="AnswerResultMap">
        SELECT * FROM psy_t_answer 
        WHERE record_id = #{recordId} AND del_flag = 0
        ORDER BY question_id
    </select>

    <!-- 根据测评记录ID查询答案列表（包含题目、选项信息） -->
    <select id="selectAnswersWithDetailsByRecordId" parameterType="Long" resultMap="AnswerWithDetailsMap">
        SELECT a.*, 
               q.id as q_id, q.question_no as q_question_no, q.question_text as q_question_text,
               q.question_type as q_question_type, q.dimension as q_dimension,
               o.id as o_id, o.option_text as o_option_text, o.option_value as o_option_value, o.score as o_score
        FROM psy_t_answer a
        LEFT JOIN psy_t_question q ON a.question_id = q.id
        LEFT JOIN psy_t_option o ON a.option_id = o.id
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
        ORDER BY q.question_no
    </select>

    <!-- 根据题目ID查询答案列表 -->
    <select id="selectAnswersByQuestionId" parameterType="Long" resultMap="AnswerResultMap">
        SELECT * FROM psy_t_answer 
        WHERE question_id = #{questionId} AND del_flag = 0
        ORDER BY record_id
    </select>

    <!-- 新增答案 -->
    <insert id="insertAnswer" parameterType="PsyAssessmentAnswer" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_answer (
            record_id, question_id, option_id, answer_text, score, answer_time, time_spent,
            del_flag, create_by, create_time, update_by, update_time
        ) VALUES (
            #{recordId}, #{questionId}, #{optionId}, #{answerText}, #{score}, #{answerTime}, #{timeSpent},
            #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate()
        )
    </insert>

    <!-- 修改答案 -->
    <update id="updateAnswer" parameterType="PsyAssessmentAnswer">
        UPDATE psy_t_answer
        <set>
            <if test="recordId != null">record_id = #{recordId},</if>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionId != null">option_id = #{optionId},</if>
            <if test="answerText != null">answer_text = #{answerText},</if>
            <if test="score != null">score = #{score},</if>
            <if test="answerTime != null">answer_time = #{answerTime},</if>
            <if test="timeSpent != null">time_spent = #{timeSpent},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除答案 -->
    <update id="deleteAnswerById" parameterType="Long">
        UPDATE psy_t_answer SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除答案 -->
    <update id="deleteAnswerByIds" parameterType="Long">
        UPDATE psy_t_answer SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据测评记录ID删除答案 -->
    <update id="deleteAnswersByRecordId" parameterType="Long">
        UPDATE psy_t_answer SET del_flag = 1 WHERE record_id = #{recordId}
    </update>

    <!-- 查询用户在指定题目的答案 -->
    <select id="selectAnswerByRecordAndQuestion" resultMap="AnswerResultMap">
        SELECT * FROM psy_t_answer 
        WHERE record_id = #{recordId} AND question_id = #{questionId} AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 批量插入答案 -->
    <insert id="batchInsertAnswers" parameterType="java.util.List">
        INSERT INTO psy_t_answer (
            record_id, question_id, option_id, answer_text, score, answer_time, time_spent,
            del_flag, create_by, create_time, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.recordId}, #{item.questionId}, #{item.optionId}, #{item.answerText}, #{item.score}, 
                #{item.answerTime}, #{item.timeSpent}, #{item.delFlag}, #{item.createBy}, sysdate(), 
                #{item.updateBy}, sysdate()
            )
        </foreach>
    </insert>

    <!-- 统计测评记录的答案数量 -->
    <select id="countAnswersByRecordId" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_answer WHERE record_id = #{recordId} AND del_flag = 0
    </select>

    <!-- 计算测评记录的总分 -->
    <select id="calculateTotalScore" parameterType="Long" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(score), 0) FROM psy_t_answer WHERE record_id = #{recordId} AND del_flag = 0
    </select>

    <!-- 计算维度得分 -->
    <select id="calculateDimensionScore" resultType="java.math.BigDecimal">
        SELECT IFNULL(SUM(a.score), 0)
        FROM psy_t_answer a
        JOIN psy_t_question q ON a.question_id = q.id
        WHERE a.record_id = #{recordId} AND q.dimension = #{dimension} AND a.del_flag = 0
    </select>

    <!-- 查询答案统计信息 -->
    <select id="selectAnswerStats" parameterType="Long" resultType="java.util.Map">
        SELECT 
            q.dimension,
            COUNT(a.id) as answer_count,
            SUM(a.score) as total_score,
            AVG(a.score) as avg_score,
            AVG(a.time_spent) as avg_time_spent
        FROM psy_t_answer a
        JOIN psy_t_question q ON a.question_id = q.id
        WHERE a.record_id = #{recordId} AND a.del_flag = 0
        GROUP BY q.dimension
    </select>

    <!-- 查询题目答案分布统计 -->
    <select id="selectQuestionAnswerStats" parameterType="Long" resultType="java.util.Map">
        SELECT 
            o.id as option_id,
            o.option_text,
            o.option_value,
            COUNT(a.id) as answer_count,
            ROUND(COUNT(a.id) * 100.0 / (
                SELECT COUNT(1) FROM psy_t_answer 
                WHERE question_id = #{questionId} AND del_flag = 0
            ), 2) as percentage
        FROM psy_t_option o
        LEFT JOIN psy_t_answer a ON o.id = a.option_id AND a.del_flag = 0
        WHERE o.question_id = #{questionId} AND o.del_flag = 0
        GROUP BY o.id, o.option_text, o.option_value
        ORDER BY o.order_num
    </select>
</mapper>
