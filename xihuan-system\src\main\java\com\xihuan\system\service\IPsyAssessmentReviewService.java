package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyAssessmentReview;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;

import java.util.List;
import java.util.Map;

/**
 * 心理测评评价Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyAssessmentReviewService {
    
    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectReviewList(PsyAssessmentReview review);

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    PsyAssessmentReview selectReviewById(Long id);

    /**
     * 查询评价详情（包含用户、量表等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    PsyAssessmentReview selectReviewWithDetails(Long id);

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectReviewsByUserId(Long userId);

    /**
     * 根据量表ID查询评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectReviewsByScaleId(Long scaleId);

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int insertReview(PsyAssessmentReview review);

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int updateReview(PsyAssessmentReview review);

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    int deleteReviewById(Long id);

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    int deleteReviewByIds(Long[] ids);

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param status 审核状态
     * @param auditRemark 审核备注
     * @return 结果
     */
    int auditReview(Long id, Integer status, String auditRemark);

    /**
     * 点赞评价
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    int likeReview(Long id, Long userId);

    /**
     * 取消点赞
     * 
     * @param id 评价ID
     * @param userId 用户ID
     * @return 结果
     */
    int unlikeReview(Long id, Long userId);

    /**
     * 查询用户是否已点赞
     * 
     * @param reviewId 评价ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    boolean isLiked(Long reviewId, Long userId);

    /**
     * 查询量表的评价统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectReviewStats(Long scaleId);

    /**
     * 查询用户的评价统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserReviewStats(Long userId);

    /**
     * 获取评价DTO
     * 
     * @param id 评价ID
     * @param userId 当前用户ID
     * @return 评价DTO
     */
    PsyAssessmentDTO.ReviewDTO getReviewDTO(Long id, Long userId);

    /**
     * 获取评价列表DTO
     * 
     * @param review 查询条件
     * @param userId 当前用户ID
     * @return 评价DTO列表
     */
    List<PsyAssessmentDTO.ReviewDTO> getReviewListDTO(PsyAssessmentReview review, Long userId);
}
