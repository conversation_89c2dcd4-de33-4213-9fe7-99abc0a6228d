package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 心理测评订单表对象 psy_t_order
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentOrder extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 订单ID */
    @Excel(name = "订单ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 订单编号 */
    @Excel(name = "订单编号")
    @NotBlank(message = "订单编号不能为空")
    @Size(max = 50, message = "订单编号不能超过50个字符")
    private String orderNo;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 量表名称 */
    @Excel(name = "量表名称")
    @NotBlank(message = "量表名称不能为空")
    @Size(max = 100, message = "量表名称不能超过100个字符")
    private String scaleName;

    /** 原价 */
    @Excel(name = "原价")
    @DecimalMin(value = "0.00", message = "原价不能为负数")
    private BigDecimal originalPrice;

    /** 实付金额 */
    @Excel(name = "实付金额")
    @DecimalMin(value = "0.00", message = "实付金额不能为负数")
    private BigDecimal actualPrice;

    /** 优惠金额 */
    @Excel(name = "优惠金额")
    @DecimalMin(value = "0.00", message = "优惠金额不能为负数")
    private BigDecimal discountAmount;

    /** 优惠券ID */
    @Excel(name = "优惠券ID", cellType = Excel.ColumnType.NUMERIC)
    private Long couponId;

    /** 支付方式 */
    @Excel(name = "支付方式")
    @Size(max = 20, message = "支付方式不能超过20个字符")
    private String paymentMethod;

    /** 支付状态(0=待支付 1=已支付 2=已退款) */
    @Excel(name = "支付状态", readConverterExp = "0=待支付,1=已支付,2=已退款")
    private Integer paymentStatus;

    /** 订单状态(0=待支付 1=已支付 2=已完成 3=已取消) */
    @Excel(name = "订单状态", readConverterExp = "0=待支付,1=已支付,2=已完成,3=已取消")
    private Integer orderStatus;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 退款金额 */
    @Excel(name = "退款金额")
    @DecimalMin(value = "0.00", message = "退款金额不能为负数")
    private BigDecimal refundAmount;

    /** 退款时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "退款时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date refundTime;

    /** 退款原因 */
    @Excel(name = "退款原因")
    @Size(max = 200, message = "退款原因不能超过200个字符")
    private String refundReason;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 用户信息 */
    private SysUser user;

    /** 量表信息 */
    private PsyAssessmentScale scale;

    /** 优惠券信息 */
    private Object coupon; // TODO 待定义优惠券实体

    // 扩展字段
    /** 用户昵称 */
    private String nickName;

    /** 是否可以退款 */
    private Boolean canRefund;

    /** 是否可以取消 */
    private Boolean canCancel;

    /** 距离过期时间(分钟) */
    private Long timeToExpire;

    /** 支付状态描述 */
    private String paymentStatusDesc;

    /** 订单状态描述 */
    private String orderStatusDesc;

    // 常量定义
    /** 支付状态：待支付 */
    public static final Integer PAYMENT_STATUS_PENDING = 0;
    
    /** 支付状态：已支付 */
    public static final Integer PAYMENT_STATUS_PAID = 1;
    
    /** 支付状态：已退款 */
    public static final Integer PAYMENT_STATUS_REFUNDED = 2;

    /** 订单状态：待支付 */
    public static final Integer ORDER_STATUS_PENDING = 0;
    
    /** 订单状态：已支付 */
    public static final Integer ORDER_STATUS_PAID = 1;
    
    /** 订单状态：已完成 */
    public static final Integer ORDER_STATUS_COMPLETED = 2;
    
    /** 订单状态：已取消 */
    public static final Integer ORDER_STATUS_CANCELLED = 3;

    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 获取支付状态描述
     */
    public String getPaymentStatusDesc() {
        if (paymentStatus == null) return "";
        switch (paymentStatus) {
            case 0: return "待支付";
            case 1: return "已支付";
            case 2: return "已退款";
            default: return "未知";
        }
    }

    /**
     * 获取订单状态描述
     */
    public String getOrderStatusDesc() {
        if (orderStatus == null) return "";
        switch (orderStatus) {
            case 0: return "待支付";
            case 1: return "已支付";
            case 2: return "已完成";
            case 3: return "已取消";
            default: return "未知";
        }
    }

    /**
     * 是否待支付
     */
    public boolean isPending() {
        return PAYMENT_STATUS_PENDING.equals(paymentStatus);
    }

    /**
     * 是否已支付
     */
    public boolean isPaid() {
        return PAYMENT_STATUS_PAID.equals(paymentStatus);
    }

    /**
     * 是否已退款
     */
    public boolean isRefunded() {
        return PAYMENT_STATUS_REFUNDED.equals(paymentStatus);
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return ORDER_STATUS_COMPLETED.equals(orderStatus);
    }

    /**
     * 是否已取消
     */
    public boolean isCancelled() {
        return ORDER_STATUS_CANCELLED.equals(orderStatus);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }
}
