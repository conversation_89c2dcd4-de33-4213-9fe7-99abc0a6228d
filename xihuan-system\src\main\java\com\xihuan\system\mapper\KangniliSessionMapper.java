package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.kangnili.KangniliSession;
import org.apache.ibatis.annotations.*;

/**
 * 测评会话Mapper接口
 */
@Mapper
public interface KangniliSessionMapper {
    /**
     * 插入新的测评会话记录
     * @param session 会话实体对象
     * @return 影响行数
     */
    @Insert("INSERT INTO kangnili_session(session_id, start_time, finish_time, total_score, result_level, share_code) " +
            "VALUES(#{sessionId}, #{startTime}, #{finishTime}, #{totalScore}, #{resultLevel}, #{shareCode})")
    int insertSession(KangniliSession session);

    /**
     * 根据分享码查询是否存在记录
     * @param shareCode 分享码
     * @return 是否存在
     */
    @Select("SELECT COUNT(*) FROM kangnili_session WHERE share_code = #{shareCode}")
    boolean existsByShareCode(String shareCode);

    /**
     * 根据分享码查询是否存在记录
     * @param shareCode 分享码
     * @return 是否存在
     */
    @Select("SELECT " +
            "session_id, " +
            "start_time, " +
            "finish_time, " +
            "total_score, " +
            "result_level, " +
            "share_code " +
            "FROM kangnili_session " +
            "WHERE share_code = #{shareCode}")
    @Results(id = "sessionMap", value = {
            @Result(column = "session_id", property = "sessionId"),
            @Result(column = "start_time", property = "startTime"),
            @Result(column = "finish_time", property = "finishTime"),
            @Result(column = "total_score", property = "totalScore"),
            @Result(column = "result_level", property = "resultLevel"),
            @Result(column = "share_code", property = "shareCode")
    })
    KangniliSession getByShareCode(String shareCode);
}
