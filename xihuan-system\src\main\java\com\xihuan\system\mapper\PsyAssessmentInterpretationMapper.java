package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentInterpretation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;

/**
 * 测评结果解释Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentInterpretationMapper {
    
    /**
     * 查询结果解释列表
     * 
     * @param interpretation 结果解释信息
     * @return 结果解释集合
     */
    List<PsyAssessmentInterpretation> selectInterpretationList(PsyAssessmentInterpretation interpretation);

    /**
     * 根据ID查询结果解释
     * 
     * @param id 结果解释ID
     * @return 结果解释信息
     */
    PsyAssessmentInterpretation selectInterpretationById(Long id);

    /**
     * 根据量表ID查询结果解释列表
     * 
     * @param scaleId 量表ID
     * @return 结果解释集合
     */
    List<PsyAssessmentInterpretation> selectInterpretationsByScaleId(Long scaleId);

    /**
     * 根据量表ID和维度查询结果解释列表
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @return 结果解释集合
     */
    List<PsyAssessmentInterpretation> selectInterpretationsByDimension(@Param("scaleId") Long scaleId, @Param("dimension") String dimension);

    /**
     * 根据分数查询结果解释
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称（为空表示总分）
     * @param score 分数
     * @return 结果解释信息
     */
    PsyAssessmentInterpretation selectInterpretationByScore(@Param("scaleId") Long scaleId, 
                                                          @Param("dimension") String dimension, 
                                                          @Param("score") BigDecimal score);

    /**
     * 新增结果解释
     * 
     * @param interpretation 结果解释信息
     * @return 结果
     */
    int insertInterpretation(PsyAssessmentInterpretation interpretation);

    /**
     * 修改结果解释
     * 
     * @param interpretation 结果解释信息
     * @return 结果
     */
    int updateInterpretation(PsyAssessmentInterpretation interpretation);

    /**
     * 删除结果解释
     * 
     * @param id 结果解释ID
     * @return 结果
     */
    int deleteInterpretationById(Long id);

    /**
     * 批量删除结果解释
     * 
     * @param ids 需要删除的结果解释ID
     * @return 结果
     */
    int deleteInterpretationByIds(Long[] ids);

    /**
     * 根据量表ID删除结果解释
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    int deleteInterpretationsByScaleId(Long scaleId);

    /**
     * 批量插入结果解释
     * 
     * @param interpretations 结果解释列表
     * @return 结果
     */
    int batchInsertInterpretations(List<PsyAssessmentInterpretation> interpretations);

    /**
     * 检查分数范围是否重叠
     * 
     * @param scaleId 量表ID
     * @param dimension 维度名称
     * @param minScore 最小分数
     * @param maxScore 最大分数
     * @param excludeId 排除的ID
     * @return 数量
     */
    int checkScoreRangeOverlap(@Param("scaleId") Long scaleId, 
                              @Param("dimension") String dimension, 
                              @Param("minScore") BigDecimal minScore, 
                              @Param("maxScore") BigDecimal maxScore, 
                              @Param("excludeId") Long excludeId);

    /**
     * 查询量表的维度列表
     * 
     * @param scaleId 量表ID
     * @return 维度列表
     */
    List<String> selectDimensionsByScaleId(Long scaleId);

    /**
     * 查询量表的结果等级列表
     * 
     * @param scaleId 量表ID
     * @return 等级列表
     */
    List<String> selectLevelsByScaleId(Long scaleId);
}
