package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantReview;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyConsultantReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 咨询师评价表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/consultantReview")
public class PsyConsultantReviewController extends BaseController {
    
    @Autowired
    private IPsyConsultantReviewService reviewService;

    /**
     * 查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyConsultantReview review) {
        startPage();
        List<PsyConsultantReview> list = reviewService.selectReviewList(review);
        return getDataTable(list);
    }

    /**
     * 导出评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:export')")
    @Log(title = "咨询师评价", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyConsultantReview review) {
        List<PsyConsultantReview> list = reviewService.selectReviewList(review);
        ExcelUtil<PsyConsultantReview> util = new ExcelUtil<PsyConsultantReview>(PsyConsultantReview.class);
        util.exportExcel(response, list, "咨询师评价数据");
    }

    /**
     * 获取评价详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewById(id));
    }

    /**
     * 获取评价详细信息（包含关联信息）
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(reviewService.selectReviewWithDetails(id));
    }

    /**
     * 新增评价
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:add')")
    @Log(title = "咨询师评价", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyConsultantReview review) {
        return toAjax(reviewService.insertReview(review));
    }

    /**
     * 修改评价
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:edit')")
    @Log(title = "咨询师评价", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyConsultantReview review) {
        return toAjax(reviewService.updateReview(review));
    }

    /**
     * 删除评价（支持单个删除和批量删除）
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:remove')")
    @Log(title = "咨询师评价", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String ids) {
        // 解析ID字符串，支持单个ID或多个ID（用逗号分隔）
        String[] idArray = ids.split(",");
        Long[] idLongs = new Long[idArray.length];

        try {
            for (int i = 0; i < idArray.length; i++) {
                idLongs[i] = Long.parseLong(idArray[i].trim());
            }
        } catch (NumberFormatException e) {
            return error("无效的评价ID格式");
        }

        // 如果只有一个ID，调用单个删除方法
        if (idLongs.length == 1) {
            return toAjax(reviewService.deleteReviewById(idLongs[0]));
        } else {
            // 多个ID，调用批量删除方法
            return toAjax(reviewService.deleteReviewByIds(idLongs));
        }
    }

    /**
     * 根据咨询师ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:list')")
    @GetMapping("/consultant/{consultantId}")
    public AjaxResult getReviewsByConsultant(@PathVariable Long consultantId) {
        List<PsyConsultantReview> list = reviewService.selectReviewsByConsultantId(consultantId);
        return success(list);
    }

    /**
     * 根据用户ID查询评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getReviewsByUser(@PathVariable Long userId) {
        List<PsyConsultantReview> list = reviewService.selectReviewsByUserId(userId);
        return success(list);
    }

    /**
     * 根据咨询记录ID查询评价
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:query')")
    @GetMapping("/record/{recordId}")
    public AjaxResult getReviewByRecord(@PathVariable Long recordId) {
        PsyConsultantReview review = reviewService.selectReviewByRecordId(recordId);
        return success(review);
    }

    /**
     * 审核评价
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:audit')")
    @Log(title = "审核评价", businessType = BusinessType.UPDATE)
    @PostMapping("/audit/{id}")
    public AjaxResult auditReview(@PathVariable Long id, @RequestParam Integer adminCheck) {
        return toAjax(reviewService.auditReview(id, adminCheck, getUsername()));
    }

    /**
     * 咨询师回复评价
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:reply')")
    @Log(title = "回复评价", businessType = BusinessType.UPDATE)
    @PostMapping("/reply/{id}")
    public AjaxResult replyReview(@PathVariable Long id, @RequestParam String consultantReply) {
        return toAjax(reviewService.replyReview(id, consultantReply, getUsername()));
    }

    /**
     * 查询待审核评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:list')")
    @GetMapping("/pending")
    public AjaxResult getPendingReviews() {
        List<PsyConsultantReview> list = reviewService.selectPendingReviews();
        return success(list);
    }

    /**
     * 查询已通过审核的评价列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:list')")
    @GetMapping("/approved/{consultantId}")
    public AjaxResult getApprovedReviews(@PathVariable Long consultantId) {
        List<PsyConsultantReview> list = reviewService.selectApprovedReviews(consultantId);
        return success(list);
    }

    /**
     * 获取咨询师评价统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:list')")
    @GetMapping("/statistics/{consultantId}")
    public AjaxResult getReviewStatistics(@PathVariable Long consultantId) {
        // 计算平均评分
        java.math.BigDecimal avgRating = reviewService.calculateAverageRating(consultantId);
        
        // 统计评价数量
        int totalCount = reviewService.countReviewsByConsultantId(consultantId);
        
        // 获取评分分布
        java.util.Map<String, Object> distribution = reviewService.getRatingDistribution(consultantId);
        
        java.util.Map<String, Object> statistics = new java.util.HashMap<>();
        statistics.put("avgRating", avgRating);
        statistics.put("totalCount", totalCount);
        statistics.put("distribution", distribution);
        
        return success(statistics);
    }

    /**
     * 更新咨询师评分信息
     */
    @PreAuthorize("@ss.hasPermi('system:consultantReview:edit')")
    @Log(title = "更新咨询师评分", businessType = BusinessType.UPDATE)
    @PostMapping("/updateRating/{consultantId}")
    public AjaxResult updateConsultantRating(@PathVariable Long consultantId) {
        return toAjax(reviewService.updateConsultantRating(consultantId));
    }
}
