<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCategoryMapper">
    <!-- 基础字段映射 -->
    <resultMap id="BaseResultMap" type="PsyCategory">
        <id     column="category_id"    property="categoryId"   />
        <result column="parent_id"      property="parentId"     />
        <result column="category_name"  property="categoryName" />
        <result column="order_num"      property="orderNum"     />
        <result column="status"        property="status"       />
        <result column="create_by"      property="createBy"     />
        <result column="create_time"    property="createTime"   />
        <result column="update_by"      property="updateBy"     />
        <result column="update_time"    property="updateTime"   />
    </resultMap>

    <!-- 服务项目映射 -->
    <resultMap id="ServiceItemResultMap" type="PsyServiceItem">
        <id column="item_id" property="itemId"/>
        <result column="item_name" property="itemName"/>
        <result column="category" property="category"/>
        <result column="price" property="price"/>
        <result column="quantity" property="quantity"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="del_flag" property="delFlag"/>
        <result column="product_id" property="productId"/>
    </resultMap>

    <!-- 包含产品和服务项目的扩展映射 -->
    <resultMap id="CategoryWithProductsMap" type="PsyCategory" extends="BaseResultMap">
        <collection property="products" ofType="PsyProduct">
            <id column="prod_product_id" property="productId"/>
            <result column="prod_product_name" property="productName"/>
            <result column="prod_product_image" property="productImage"/>
            <result column="prod_service_method" property="serviceMethod"/>
            <result column="prod_service_guarantee" property="serviceGuarantee"/>
            <result column="prod_service_direction" property="serviceDirection"/>
            <result column="prod_service_direction_type" property="serviceDirectionType"/>
            <result column="prod_consultant_grade" property="consultantGrade"/>
            <result column="prod_service_duration" property="serviceDuration"/>
            <result column="prod_graphic_details" property="graphicDetails"/>
            <result column="prod_original_price" property="originalPrice"/>
            <result column="prod_discount_price" property="discountPrice"/>
            <result column="prod_discount_rate" property="discountRate"/>
            <result column="prod_validity_period" property="validityPeriod"/>
            <result column="prod_disable_flag" property="disableFlag"/>
            <result column="prod_create_time" property="createTime"/>
            <!-- 服务项目集合 -->
            <collection property="serviceItems" ofType="PsyServiceItem">
                <id column="si_item_id" property="itemId"/>
                <result column="si_item_name" property="itemName"/>
                <result column="si_category" property="category"/>
                <result column="si_price" property="price"/>
                <result column="si_quantity" property="quantity"/>
                <result column="si_sort_order" property="sortOrder"/>
            </collection>
        </collection>
    </resultMap>

    <!-- 通用查询字段 -->
    <sql id="selectCategory">
        SELECT
            category_id,
            parent_id,
            category_name,
            order_num,
            status,
            create_by,
            create_time,
            update_by,
            update_time
        FROM psy_category
    </sql>

    <!-- 分类列表查询 -->
    <select id="selectCategoryList" parameterType="PsyCategory" resultMap="BaseResultMap">
        WITH RECURSIVE category_tree AS (
            -- 查询根节点
            SELECT
                category_id,
                parent_id,
                category_name,
                order_num,
                status,
                create_by,
                create_time,
                update_by,
                update_time
            FROM psy_category
            WHERE parent_id = 0
            
            UNION ALL
            
            -- 递归查询子节点
            SELECT
                c.category_id,
                c.parent_id,
                c.category_name,
                c.order_num,
                c.status,
                c.create_by,
                c.create_time,
                c.update_by,
                c.update_time
            FROM psy_category c
            INNER JOIN category_tree ct ON c.parent_id = ct.category_id
        )
        SELECT * FROM category_tree
        <where>
            <if test="categoryName != null and categoryName != ''">
                AND category_name LIKE CONCAT('%', #{categoryName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
        </where>
        ORDER BY parent_id, order_num ASC
    </select>

    <!-- 根据ID查询分类 -->
    <select id="selectCategoryById" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectCategory"/>
        WHERE category_id = #{categoryId}
    </select>

    <!-- 查询子分类列表 -->
    <select id="selectChildrenByParentId" parameterType="Long" resultMap="BaseResultMap">
        <include refid="selectCategory"/>
        WHERE parent_id = #{parentId}
        ORDER BY order_num ASC
    </select>

    <!-- 检查关联产品是否存在 -->
    <select id="checkExistProducts" resultType="int">
        SELECT COUNT(1)
        FROM psy_product_category
        WHERE category_id = #{categoryId}
            LIMIT 1
    </select>

    <!-- 校验分类唯一性 -->
    <select id="checkCategoryUnique" resultType="int">
        SELECT COUNT(1)
        FROM psy_category
        WHERE parent_id = #{parentId}
        AND category_name = #{categoryName}
        <if test="categoryId != null">
            AND category_id != #{categoryId}
        </if>
    </select>

    <!-- 新增分类 -->
    <insert id="insertCategory" useGeneratedKeys="true" keyProperty="categoryId">
        INSERT INTO psy_category (
            parent_id, category_name, order_num, status,
            create_by, create_time
        ) VALUES (
                     #{parentId}, #{categoryName}, #{orderNum}, #{status},
                     #{createBy}, #{createTime}
                 )
    </insert>

    <!-- 更新分类 -->
    <update id="updateCategory" parameterType="PsyCategory">
        UPDATE psy_category
        <set>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="categoryName != null">category_name = #{categoryName},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        WHERE category_id = #{categoryId}
    </update>

    <!-- 删除分类 -->
    <delete id="deleteCategoryById" parameterType="Long">
        DELETE FROM psy_category
        WHERE category_id = #{categoryId}
    </delete>


    <!-- 在PsyCategoryMapper.xml中补充 -->
    <select id="selectChildrenCount" resultType="int">
        SELECT COUNT(1)
        FROM psy_category
        WHERE parent_id = #{parentId}
    </select>

    <!-- 增强树查询 -->
    <select id="selectCategoryWithProducts" resultMap="CategoryWithProductsMap">
        SELECT
            c.*,
            p.product_id AS prod_product_id,
            p.product_name AS prod_product_name,
            p.product_image AS prod_product_image,
            p.service_method AS prod_service_method,
            p.service_guarantee AS prod_service_guarantee,
            p.service_direction AS prod_service_direction,
            p.service_direction_type AS prod_service_direction_type,
            p.graphic_details AS prod_graphic_details,
            p.consultant_grade AS prod_consultant_grade,
            p.service_duration AS prod_service_duration,
            p.graphic_details AS prod_graphic_details,
            p.original_price AS prod_original_price,
            p.discount_price AS prod_discount_price,
            p.discount_rate AS prod_discount_rate,
            p.validity_period AS prod_validity_period,
            p.disable_flag AS prod_disable_flag,
            si.item_id AS si_item_id,
            si.item_name AS si_item_name,
            si.category AS si_category,
            si.price AS si_price,
            si.quantity AS si_quantity,
            si.sort_order AS si_sort_order
        FROM psy_category c
                 LEFT JOIN psy_product_category pc ON c.category_id = pc.category_id
                 LEFT JOIN psy_product p ON pc.product_id = p.product_id
                 LEFT JOIN psy_product_service ps ON p.product_id = ps.product_id
                 LEFT JOIN psy_service_item si ON ps.item_id = si.item_id
        WHERE c.category_id = #{categoryId}
          AND (p.del_flag = '0' OR p.del_flag IS NULL)
          AND (si.del_flag = '0' OR si.del_flag IS NULL)
        ORDER BY p.product_id DESC, si.sort_order ASC
    </select>

    <!-- 修改分类列表查询，关联产品信息和服务项目 -->
    <select id="selectCategoryListWithProducts" resultMap="CategoryWithProductsMap">
        WITH RECURSIVE category_tree AS (
            -- 查询根节点
            SELECT
                c.category_id,
                c.parent_id,
                c.category_name,
                c.order_num,
                c.status,
                c.create_by,
                c.create_time,
                c.update_by,
                c.update_time
            FROM psy_category c
            WHERE c.parent_id = 0
            AND c.status = '0'
            
            UNION ALL
            
            -- 递归查询子节点
            SELECT
                c.category_id,
                c.parent_id,
                c.category_name,
                c.order_num,
                c.status,
                c.create_by,
                c.create_time,
                c.update_by,
                c.update_time
            FROM psy_category c
            INNER JOIN category_tree ct ON c.parent_id = ct.category_id
            WHERE c.status = '0'
        )
        SELECT 
            ct.*,
            p.product_id AS prod_product_id,
            p.product_name AS prod_product_name,
            p.product_image AS prod_product_image,
            p.service_method AS prod_service_method,
            p.service_guarantee AS prod_service_guarantee,
            p.service_direction AS prod_service_direction,
            p.service_direction_type AS prod_service_direction_type,
            p.graphic_details AS prod_graphic_details,
            p.consultant_grade AS prod_consultant_grade,
            p.service_duration AS prod_service_duration,
            p.original_price AS prod_original_price,
            p.discount_price AS prod_discount_price,
            p.discount_rate AS prod_discount_rate,
            p.validity_period AS prod_validity_period,
            p.disable_flag AS prod_disable_flag,
            p.create_time AS prod_create_time,
            si.item_id AS si_item_id,
            si.item_name AS si_item_name,
            si.category AS si_category,
            si.price AS si_price,
            si.quantity AS si_quantity,
            si.sort_order AS si_sort_order
        FROM category_tree ct
        LEFT JOIN psy_product_category pc ON ct.category_id = pc.category_id
        LEFT JOIN psy_product p ON pc.product_id = p.product_id AND p.del_flag = '0'
        LEFT JOIN psy_product_service ps ON p.product_id = ps.product_id
        LEFT JOIN psy_service_item si ON ps.item_id = si.item_id AND si.del_flag = '0'
        ORDER BY ct.parent_id, ct.order_num ASC, p.create_time DESC, si.sort_order ASC
    </select>
</mapper>