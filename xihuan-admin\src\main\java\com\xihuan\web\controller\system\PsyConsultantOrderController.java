package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyConsultantOrder;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyConsultantOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 心理咨询订单表Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/consultantOrder")
public class PsyConsultantOrderController extends BaseController {
    
    @Autowired
    private IPsyConsultantOrderService orderService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyConsultantOrder order) {
        startPage();
        List<PsyConsultantOrder> list = orderService.selectOrderList(order);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:export')")
    @Log(title = "咨询订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyConsultantOrder order) {
        List<PsyConsultantOrder> list = orderService.selectOrderList(order);
        ExcelUtil<PsyConsultantOrder> util = new ExcelUtil<PsyConsultantOrder>(PsyConsultantOrder.class);
        util.exportExcel(response, list, "咨询订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(orderService.selectOrderById(id));
    }

    /**
     * 根据订单号查询订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:query')")
    @GetMapping(value = "/orderNo/{orderNo}")
    public AjaxResult getInfoByOrderNo(@PathVariable("orderNo") String orderNo) {
        return success(orderService.selectOrderByOrderNo(orderNo));
    }

    /**
     * 获取订单详细信息（包含关联信息）
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:query')")
    @GetMapping(value = "/details/{id}")
    public AjaxResult getDetails(@PathVariable("id") Long id) {
        return success(orderService.selectOrderWithDetails(id));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:add')")
    @Log(title = "咨询订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyConsultantOrder order) {
        try {
            return toAjax(orderService.insertOrder(order));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:edit')")
    @Log(title = "咨询订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyConsultantOrder order) {
        return toAjax(orderService.updateOrder(order));
    }

    /**
     * 删除订单（支持单个删除和批量删除）
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:remove')")
    @Log(title = "咨询订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable String ids) {
        // 解析ID字符串，支持单个ID或多个ID（用逗号分隔）
        String[] idArray = ids.split(",");
        Long[] idLongs = new Long[idArray.length];

        try {
            for (int i = 0; i < idArray.length; i++) {
                idLongs[i] = Long.parseLong(idArray[i].trim());
            }
        } catch (NumberFormatException e) {
            return error("无效的订单ID格式");
        }

        // 如果只有一个ID，调用单个删除方法
        if (idLongs.length == 1) {
            return toAjax(orderService.deleteOrderById(idLongs[0]));
        } else {
            // 多个ID，调用批量删除方法
            return toAjax(orderService.deleteOrderByIds(idLongs));
        }
    }

    /**
     * 根据用户ID查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/user/{userId}")
    public AjaxResult getOrdersByUser(@PathVariable Long userId) {
        List<PsyConsultantOrder> list = orderService.selectOrdersByUserId(userId);
        return success(list);
    }

    /**
     * 根据咨询师ID查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/consultant/{consultantId}")
    public AjaxResult getOrdersByConsultant(@PathVariable Long consultantId) {
        List<PsyConsultantOrder> list = orderService.selectOrdersByConsultantId(consultantId);
        return success(list);
    }

    /**
     * 更新订单支付状态
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:edit')")
    @Log(title = "更新支付状态", businessType = BusinessType.UPDATE)
    @PostMapping("/payment/{orderNo}")
    public AjaxResult updatePaymentStatus(@PathVariable String orderNo, 
                                        @RequestParam String status,
                                        @RequestParam String paymentMethod,
                                        @RequestParam Date paymentTime) {
        return toAjax(orderService.updateOrderPaymentStatus(orderNo, status, paymentMethod, paymentTime));
    }

    /**
     * 更新订单状态
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:edit')")
    @Log(title = "更新订单状态", businessType = BusinessType.UPDATE)
    @PostMapping("/status/{id}")
    public AjaxResult updateStatus(@PathVariable Long id, @RequestParam String status) {
        return toAjax(orderService.updateOrderStatus(id, status));
    }

    /**
     * 取消订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:edit')")
    @Log(title = "取消订单", businessType = BusinessType.UPDATE)
    @PostMapping("/cancel/{id}")
    public AjaxResult cancelOrder(@PathVariable Long id, @RequestParam String cancelReason) {
        try {
            return toAjax(orderService.cancelOrder(id, cancelReason));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 退款订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:edit')")
    @Log(title = "退款订单", businessType = BusinessType.UPDATE)
    @PostMapping("/refund/{id}")
    public AjaxResult refundOrder(@PathVariable Long id, 
                                @RequestParam BigDecimal refundAmount,
                                @RequestParam String refundReason) {
        try {
            return toAjax(orderService.refundOrder(id, refundAmount, refundReason));
        } catch (Exception e) {
            return error(e.getMessage());
        }
    }

    /**
     * 检查时间段冲突
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:query')")
    @GetMapping("/checkConflict")
    public AjaxResult checkTimeConflict(@RequestParam Long consultantId,
                                      @RequestParam Date startTime,
                                      @RequestParam Date endTime,
                                      @RequestParam(required = false) Long excludeOrderId) {
        boolean hasConflict = orderService.checkTimeConflict(consultantId, startTime, endTime, excludeOrderId);
        return success(hasConflict);
    }

    /**
     * 查询即将到期的订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/expiring")
    public AjaxResult getExpiringOrders(@RequestParam(defaultValue = "30") Integer minutes) {
        List<PsyConsultantOrder> list = orderService.selectExpiringOrders(minutes);
        return success(list);
    }

    /**
     * 查询已过期的订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/expired")
    public AjaxResult getExpiredOrders() {
        List<PsyConsultantOrder> list = orderService.selectExpiredOrders();
        return success(list);
    }

    /**
     * 处理过期订单
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:edit')")
    @Log(title = "处理过期订单", businessType = BusinessType.UPDATE)
    @PostMapping("/processExpired")
    public AjaxResult processExpiredOrders() {
        int count = orderService.processExpiredOrders();
        return success("处理了 " + count + " 个过期订单");
    }

    /**
     * 获取用户订单统计
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/statistics/user/{userId}")
    public AjaxResult getUserOrderStats(@PathVariable Long userId) {
        return success(orderService.getUserOrderStats(userId));
    }

    /**
     * 获取咨询师订单统计
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/statistics/consultant/{consultantId}")
    public AjaxResult getConsultantOrderStats(@PathVariable Long consultantId) {
        return success(orderService.getConsultantOrderStats(consultantId));
    }

    /**
     * 统计订单收入
     */
    @PreAuthorize("@ss.hasPermi('system:consultantOrder:list')")
    @GetMapping("/income/{consultantId}")
    public AjaxResult getOrderIncome(@PathVariable Long consultantId,
                                   @RequestParam(required = false) Date startTime,
                                   @RequestParam(required = false) Date endTime) {
        BigDecimal income = orderService.sumOrderIncome(consultantId, startTime, endTime);
        return success(income);
    }
}

