package com.xihuan.common.core.domain.entity;

import lombok.Data;

/**
 * 预约-时间槽关系表实体类
 * 对应数据库表：psy_time_appointment_slot
 * 
 * <AUTHOR>
 */
@Data
public class PsyTimeAppointmentSlot {
    private static final long serialVersionUID = 1L;

    /** 预约记录ID */
    private String appointmentId;

    /** 时间槽ID */
    private Long slotId;

    // 关联对象（非数据库字段）
    /** 预约记录信息 */
    private PsyTimeAppointment appointment;

    /** 时间槽信息 */
    private PsyTimeSlot timeSlot;
}
