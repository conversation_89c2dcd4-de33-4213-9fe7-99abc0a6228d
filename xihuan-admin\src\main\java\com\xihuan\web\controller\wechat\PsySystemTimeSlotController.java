package com.xihuan.web.controller.wechat;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 系统公共时间槽Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/systemTimeSlot")
public class PsySystemTimeSlotController extends BaseController {
    
    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;

    /**
     * 查询系统时间槽列表
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsySystemTimeSlot systemTimeSlot) {
        startPage();
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectSystemTimeSlotList(systemTimeSlot);
        return getDataTable(list);
    }

    /**
     * 获取系统时间槽详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(systemTimeSlotService.selectSystemTimeSlotById(id));
    }

    /**
     * 查询指定日期范围的系统时间槽
     */
    @GetMapping("/dateRange")
    public AjaxResult getSlotsByDateRange(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectSlotsByDateRange(startDate, endDate, centerId);
        return AjaxResult.success(list);
    }

    /**
     * 查询指定日期的系统时间槽
     */
    @GetMapping("/date/{date}")
    public AjaxResult getSlotsByDate(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "1") Long centerId) {
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectSlotsByDate(date, centerId);
        return AjaxResult.success(list);
    }

    /**
     * 查询有可用咨询师的时间槽
     */
    @GetMapping("/available")
    public AjaxResult getAvailableSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        List<PsySystemTimeSlot> list = systemTimeSlotService.selectAvailableSlots(startDate, endDate, centerId);
        return AjaxResult.success(list);
    }

    /**
     * 获取格式化的系统时间槽数据
     */
    @GetMapping("/formatted")
    public AjaxResult getFormattedTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        Map<String, Object> result = systemTimeSlotService.getFormattedSystemTimeSlots(startDate, endDate, centerId);
        return AjaxResult.success(result);
    }

    /**
     * 新增系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:add')")
    @Log(title = "系统时间槽", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsySystemTimeSlot systemTimeSlot) {
        return toAjax(systemTimeSlotService.insertSystemTimeSlot(systemTimeSlot));
    }

    /**
     * 修改系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:edit')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsySystemTimeSlot systemTimeSlot) {
        return toAjax(systemTimeSlotService.updateSystemTimeSlot(systemTimeSlot));
    }

    /**
     * 删除系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:remove')")
    @Log(title = "系统时间槽", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(systemTimeSlotService.deleteSystemTimeSlotByIds(ids));
    }

    /**
     * 生成系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:generate')")
    @Log(title = "系统时间槽", businessType = BusinessType.INSERT)
    @PostMapping("/generate")
    public AjaxResult generateSystemTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, centerId);
        return AjaxResult.success("成功生成 " + count + " 个系统时间槽");
    }

    /**
     * 重新生成系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:regenerate')")
    @Log(title = "系统时间槽", businessType = BusinessType.INSERT)
    @PostMapping("/regenerate")
    public AjaxResult regenerateSystemTimeSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.regenerateSystemTimeSlots(startDate, endDate, centerId);
        return AjaxResult.success("成功重新生成 " + count + " 个系统时间槽");
    }

    /**
     * 更新可用性统计
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:update')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/updateStats")
    public AjaxResult updateAvailabilityStats(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.updateAvailabilityStats(date, centerId);
        return AjaxResult.success("成功更新 " + count + " 个时间槽的可用性统计");
    }

    /**
     * 清理过期的系统时间槽
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:clean')")
    @Log(title = "系统时间槽", businessType = BusinessType.DELETE)
    @DeleteMapping("/cleanExpired")
    public AjaxResult cleanExpiredSlots(
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate beforeDate,
            @RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.cleanExpiredSystemSlots(beforeDate, centerId);
        return AjaxResult.success("成功清理 " + count + " 个过期系统时间槽");
    }

    /**
     * 更新过期状态
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:update')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/updateExpiredStatus")
    public AjaxResult updateExpiredStatus(@RequestParam(defaultValue = "1") Long centerId) {
        int count = systemTimeSlotService.updateExpiredSlotStatusWithDelay(centerId);
        return AjaxResult.success("成功更新 " + count + " 个时间槽的过期状态");
    }

    /**
     * 批量更新时间槽状态
     */
    @PreAuthorize("@ss.hasPermi('system:systemTimeSlot:update')")
    @Log(title = "系统时间槽", businessType = BusinessType.UPDATE)
    @PutMapping("/batchUpdateStatus")
    public AjaxResult batchUpdateStatus(@RequestBody Map<String, Object> params) {
        try {
            @SuppressWarnings("unchecked")
            List<Long> slotIds = (List<Long>) params.get("slotIds");
            Integer status = (Integer) params.get("status");

            if (slotIds == null || slotIds.isEmpty()) {
                return AjaxResult.error("时间槽ID列表不能为空");
            }

            if (status == null || (status != 0 && status != 2)) {
                return AjaxResult.error("状态值无效，只能是0(可用)或2(已过期)");
            }

            int count = systemTimeSlotService.batchUpdateSlotStatus(slotIds, status);
            String statusText = status == 0 ? "可用" : "已过期";
            return AjaxResult.success("成功将 " + count + " 个时间槽状态更新为：" + statusText);
        } catch (Exception e) {
            logger.error("批量更新时间槽状态失败", e);
            return AjaxResult.error("批量更新失败：" + e.getMessage());
        }
    }
}
