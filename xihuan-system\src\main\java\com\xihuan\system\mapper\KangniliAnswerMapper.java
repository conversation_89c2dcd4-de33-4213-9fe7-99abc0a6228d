package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.kangnili.KangniliAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 测评答案Mapper接口
 */
@Mapper
public interface KangniliAnswerMapper {
    /**
     * 批量插入答案记录（MyBatis动态SQL实现）
     * @param answers 答案列表
     * @return 影响行数
     */
    int batchInsertAnswers(@Param("list") List<KangniliAnswer> answers);
}