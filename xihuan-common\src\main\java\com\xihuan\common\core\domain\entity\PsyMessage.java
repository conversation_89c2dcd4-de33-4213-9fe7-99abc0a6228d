package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;

import java.util.Date;

@Data
public class PsyMessage extends BaseEntity {
    private static final long serialVersionUID = 1L;

    @Excel(name = "消息ID")
    private Long messageId;

    @Excel(name = "发送者ID")
    private Long senderId;

    @Excel(name = "接收者ID")
    private Long receiverId;

    @Excel(name = "关联咨询师id")
    private Long conversationId;

    @Excel(name = "消息内容")
    private String content;

    @Excel(name = "消息类型", readConverterExp = "0=文本,1=图片,2=文件")
    private String messageType;

    @Excel(name = "文件路径")
    private String fileUrl;

    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    @Excel(name = "是否撤回", readConverterExp = "0=否,1=是")
    private String isWithdrawn;

    // 消息类型,前端接收字段
    private String type;

    private Boolean isUser;
}
