package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyAssessmentScale;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.common.utils.bean.BeanUtils;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;
import com.xihuan.system.mapper.PsyAssessmentScaleMapper;
import com.xihuan.system.service.IPsyAssessmentScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 心理测评量表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyAssessmentScaleServiceImpl implements IPsyAssessmentScaleService {
    
    @Autowired
    private PsyAssessmentScaleMapper scaleMapper;

    /**
     * 查询量表列表
     * 
     * @param scale 量表信息
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectScaleList(PsyAssessmentScale scale) {
        return scaleMapper.selectScaleList(scale);
    }

    /**
     * 根据ID查询量表
     * 
     * @param id 量表ID
     * @return 量表信息
     */
    @Override
    public PsyAssessmentScale selectScaleById(Long id) {
        return scaleMapper.selectScaleById(id);
    }

    /**
     * 查询量表详情（包含题目、分类等信息）
     * 
     * @param id 量表ID
     * @return 量表详情
     */
    @Override
    public PsyAssessmentScale selectScaleWithDetails(Long id) {
        return scaleMapper.selectScaleWithDetails(id);
    }

    /**
     * 根据编码查询量表
     * 
     * @param scaleCode 量表编码
     * @return 量表信息
     */
    @Override
    public PsyAssessmentScale selectScaleByCode(String scaleCode) {
        return scaleMapper.selectScaleByCode(scaleCode);
    }

    /**
     * 新增量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertScale(PsyAssessmentScale scale) {
        // 校验量表编码唯一性
        if (!checkScaleCodeUnique(scale.getScaleCode(), null)) {
            throw new ServiceException("量表编码已存在");
        }
        
        // 设置默认值
        if (scale.getStatus() == null) {
            scale.setStatus(PsyAssessmentScale.STATUS_UNPUBLISHED);
        }
        if (scale.getIsFree() == null) {
            scale.setIsFree(PsyAssessmentScale.FREE_YES);
        }
        if (scale.getDifficultyLevel() == null) {
            scale.setDifficultyLevel(PsyAssessmentScale.DIFFICULTY_EASY);
        }
        if (scale.getViewCount() == null) {
            scale.setViewCount(0);
        }
        if (scale.getTestCount() == null) {
            scale.setTestCount(0);
        }
        if (scale.getRatingCount() == null) {
            scale.setRatingCount(0);
        }
        if (scale.getSearchCount() == null) {
            scale.setSearchCount(0);
        }
        if (scale.getDelFlag() == null) {
            scale.setDelFlag(PsyAssessmentScale.DEL_FLAG_NORMAL);
        }
        
        scale.setCreateTime(DateUtils.getNowDate());
        return scaleMapper.insertScale(scale);
    }

    /**
     * 修改量表
     * 
     * @param scale 量表信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateScale(PsyAssessmentScale scale) {
        // 校验量表编码唯一性
        if (StringUtils.isNotEmpty(scale.getScaleCode()) && 
            !checkScaleCodeUnique(scale.getScaleCode(), scale.getId())) {
            throw new ServiceException("量表编码已存在");
        }
        
        scale.setUpdateTime(DateUtils.getNowDate());
        return scaleMapper.updateScale(scale);
    }

    /**
     * 删除量表
     * 
     * @param ids 需要删除的量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteScaleByIds(Long[] ids) {
        // 检查是否被使用
        for (Long id : ids) {
            if (checkScaleInUse(id)) {
                PsyAssessmentScale scale = selectScaleById(id);
                throw new ServiceException(String.format("量表【%s】已被使用，不能删除", scale.getScaleName()));
            }
        }
        return scaleMapper.deleteScaleByIds(ids);
    }

    /**
     * 根据分类ID查询量表列表
     * 
     * @param categoryId 分类ID
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectScalesByCategoryId(Long categoryId) {
        return scaleMapper.selectScalesByCategoryId(categoryId);
    }

    /**
     * 查询用户已购买的量表列表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectPurchasedScalesByUserId(Long userId) {
        return scaleMapper.selectPurchasedScalesByUserId(userId);
    }

    /**
     * 查询热门量表列表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectHotScales(Integer limit) {
        return scaleMapper.selectHotScales(limit);
    }

    /**
     * 查询推荐量表列表
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectRecommendScales(Long userId, Integer limit) {
        return scaleMapper.selectRecommendScales(userId, limit);
    }

    /**
     * 查询免费量表列表
     * 
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectFreeScales() {
        return scaleMapper.selectFreeScales();
    }

    /**
     * 更新量表查看次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int updateViewCount(Long id) {
        return scaleMapper.updateViewCount(id);
    }

    /**
     * 更新量表测试次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int updateTestCount(Long id) {
        return scaleMapper.updateTestCount(id);
    }

    /**
     * 更新量表搜索次数
     * 
     * @param id 量表ID
     * @return 结果
     */
    @Override
    public int updateSearchCount(Long id) {
        return scaleMapper.updateSearchCount(id);
    }

    /**
     * 更新量表评分统计
     * 
     * @param scaleId 量表ID
     * @return 结果
     */
    @Override
    public int updateRatingStats(Long scaleId) {
        return scaleMapper.updateRatingStats(scaleId);
    }

    /**
     * 检查量表编码唯一性
     * 
     * @param scaleCode 量表编码
     * @param excludeId 排除的ID
     * @return 是否唯一
     */
    @Override
    public boolean checkScaleCodeUnique(String scaleCode, Long excludeId) {
        int count = scaleMapper.checkScaleCodeUnique(scaleCode, excludeId);
        return count == 0;
    }

    /**
     * 检查量表是否被使用
     * 
     * @param id 量表ID
     * @return 是否被使用
     */
    @Override
    public boolean checkScaleInUse(Long id) {
        int count = scaleMapper.checkScaleInUse(id);
        return count > 0;
    }

    /**
     * 搜索量表
     * 
     * @param keyword 关键词
     * @param categoryId 分类ID
     * @param difficultyLevel 难度等级
     * @param isFree 是否免费
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> searchScales(String keyword, Long categoryId, Integer difficultyLevel, Integer isFree) {
        return scaleMapper.searchScales(keyword, categoryId, difficultyLevel, isFree);
    }

    /**
     * 统计量表数量
     * 
     * @param scale 查询条件
     * @return 数量
     */
    @Override
    public int countScales(PsyAssessmentScale scale) {
        return scaleMapper.countScales(scale);
    }

    /**
     * 查询量表统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleStats() {
        return scaleMapper.selectScaleStats().get(0);
    }

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserTestStats(Long userId) {
        return scaleMapper.selectUserTestStats(userId);
    }

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleTestStats(Long scaleId) {
        return scaleMapper.selectScaleTestStats(scaleId);
    }

    /**
     * 查询今日热门量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectTodayHotScales(Integer limit) {
        return scaleMapper.selectTodayHotScales(limit);
    }

    /**
     * 查询最新量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectLatestScales(Integer limit) {
        return scaleMapper.selectLatestScales(limit);
    }

    /**
     * 查询用户收藏的量表
     * 
     * @param userId 用户ID
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectFavoriteScalesByUserId(Long userId) {
        return scaleMapper.selectFavoriteScalesByUserId(userId);
    }

    /**
     * 查询相似量表
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<PsyAssessmentScale> selectSimilarScales(Long scaleId, Integer limit) {
        return scaleMapper.selectSimilarScales(scaleId, limit);
    }

    /**
     * 发布量表
     * 
     * @param id 量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int publishScale(Long id) {
        PsyAssessmentScale scale = new PsyAssessmentScale();
        scale.setId(id);
        scale.setStatus(PsyAssessmentScale.STATUS_PUBLISHED);
        return updateScale(scale);
    }

    /**
     * 下架量表
     * 
     * @param id 量表ID
     * @return 结果
     */
    @Override
    @Transactional
    public int offlineScale(Long id) {
        PsyAssessmentScale scale = new PsyAssessmentScale();
        scale.setId(id);
        scale.setStatus(PsyAssessmentScale.STATUS_OFFLINE);
        return updateScale(scale);
    }

    /**
     * 复制量表
     * 
     * @param id 量表ID
     * @param newScaleName 新量表名称
     * @param newScaleCode 新量表编码
     * @return 结果
     */
    @Override
    @Transactional
    public int copyScale(Long id, String newScaleName, String newScaleCode) {
        // 获取原量表信息
        PsyAssessmentScale originalScale = selectScaleWithDetails(id);
        if (originalScale == null) {
            throw new ServiceException("原量表不存在");
        }
        
        // 创建新量表
        PsyAssessmentScale newScale = new PsyAssessmentScale();
        BeanUtils.copyBeanProp(newScale, originalScale);
        newScale.setId(null);
        newScale.setScaleName(newScaleName);
        newScale.setScaleCode(newScaleCode);
        newScale.setStatus(PsyAssessmentScale.STATUS_UNPUBLISHED);
        newScale.setViewCount(0);
        newScale.setTestCount(0);
        newScale.setRatingCount(0);
        newScale.setSearchCount(0);
        newScale.setCreateTime(null);
        newScale.setUpdateTime(null);
        
        return insertScale(newScale);
    }

    /**
     * 导入量表
     * 
     * @param scaleList 量表列表
     * @param isUpdateSupport 是否更新支持
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    @Transactional
    public String importScale(List<PsyAssessmentScale> scaleList, Boolean isUpdateSupport, String operName) {
        if (StringUtils.isNull(scaleList) || scaleList.size() == 0) {
            throw new ServiceException("导入量表数据不能为空！");
        }
        
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        
        for (PsyAssessmentScale scale : scaleList) {
            try {
                // 验证是否存在这个量表
                PsyAssessmentScale existScale = selectScaleByCode(scale.getScaleCode());
                if (StringUtils.isNull(existScale)) {
                    scale.setCreateBy(operName);
                    this.insertScale(scale);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、量表 " + scale.getScaleName() + " 导入成功");
                } else if (isUpdateSupport) {
                    scale.setId(existScale.getId());
                    scale.setUpdateBy(operName);
                    this.updateScale(scale);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、量表 " + scale.getScaleName() + " 更新成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、量表 " + scale.getScaleName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、量表 " + scale.getScaleName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new ServiceException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        
        return successMsg.toString();
    }

    /**
     * 导出量表
     * 
     * @param scaleList 量表列表
     */
    @Override
    public void exportScale(List<PsyAssessmentScale> scaleList) {
        // TODO 实现导出功能
    }

    /**
     * 获取量表DTO
     * 
     * @param id 量表ID
     * @param userId 用户ID
     * @return 量表DTO
     */
    @Override
    public PsyAssessmentDTO.ScaleDTO getScaleDTO(Long id, Long userId) {
        PsyAssessmentScale scale = selectScaleWithDetails(id);
        if (scale == null) {
            return null;
        }
        
        PsyAssessmentDTO.ScaleDTO dto = new PsyAssessmentDTO.ScaleDTO();
        BeanUtils.copyBeanProp(dto, scale);
        
        // 设置扩展字段
        dto.setDifficultyDesc(scale.getDifficultyDesc());
        dto.setStatusDesc(scale.getStatusDesc());
        dto.setPriceDesc(scale.getPriceDesc());
        dto.setCreateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, scale.getCreateTime()));
        
        // TODO 设置用户相关信息（是否已购买、测评记录、评价等）
        
        return dto;
    }

    /**
     * 获取量表列表DTO
     * 
     * @param scale 查询条件
     * @param userId 用户ID
     * @return 量表DTO列表
     */
    @Override
    public List<PsyAssessmentDTO.ScaleDTO> getScaleListDTO(PsyAssessmentScale scale, Long userId) {
        List<PsyAssessmentScale> scaleList = selectScaleList(scale);
        List<PsyAssessmentDTO.ScaleDTO> dtoList = new ArrayList<>();
        
        for (PsyAssessmentScale item : scaleList) {
            PsyAssessmentDTO.ScaleDTO dto = getScaleDTO(item.getId(), userId);
            if (dto != null) {
                dtoList.add(dto);
            }
        }
        
        return dtoList;
    }
}
