package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 测评记录Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAssessmentRecordService {
    
    /**
     * 查询测评记录列表
     * 
     * @param record 测评记录信息
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectRecordList(PsyTAssessmentRecord record);

    /**
     * 根据ID查询测评记录
     * 
     * @param id 测评记录ID
     * @return 测评记录信息
     */
    PsyTAssessmentRecord selectRecordById(Long id);

    /**
     * 根据会话ID查询测评记录
     * 
     * @param sessionId 会话ID
     * @return 测评记录信息
     */
    PsyTAssessmentRecord selectRecordBySessionId(String sessionId);

    /**
     * 查询测评记录详情（包含答题记录、量表信息等）
     * 
     * @param id 测评记录ID
     * @return 测评记录详情
     */
    PsyTAssessmentRecord selectRecordWithDetails(Long id);

    /**
     * 根据用户ID查询测评记录列表
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectRecordsByUserId(Long userId);

    /**
     * 根据量表ID查询测评记录列表
     * 
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectRecordsByScaleId(Long scaleId);

    /**
     * 查询用户在指定量表的测评记录
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectUserRecordsByScaleId(Long userId, Long scaleId);

    /**
     * 新增测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    int insertRecord(PsyTAssessmentRecord record);

    /**
     * 修改测评记录
     * 
     * @param record 测评记录信息
     * @return 结果
     */
    int updateRecord(PsyTAssessmentRecord record);

    /**
     * 删除测评记录
     * 
     * @param ids 需要删除的测评记录ID
     * @return 结果
     */
    int deleteRecordByIds(Long[] ids);

    /**
     * 开始测评
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 测评记录
     */
    PsyTAssessmentRecord startAssessment(Long userId, Long scaleId);

    /**
     * 完成测评
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    int completeAssessment(Long recordId);

    /**
     * 放弃测评
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    int abandonAssessment(Long recordId);

    /**
     * 计算测评结果
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    Map<String, Object> calculateAssessmentResult(Long recordId);

    /**
     * 生成测评报告
     * 
     * @param recordId 测评记录ID
     * @return 报告内容
     */
    Map<String, Object> generateAssessmentReport(Long recordId);

    /**
     * 查询进行中的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectInProgressRecords(Long userId);

    /**
     * 查询已完成的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectCompletedRecords(Long userId);

    /**
     * 查询已放弃的测评记录
     * 
     * @param userId 用户ID
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectAbandonedRecords(Long userId);

    /**
     * 统计用户测评记录数量
     * 
     * @param userId 用户ID
     * @return 数量
     */
    int countRecordsByUserId(Long userId);

    /**
     * 统计量表测评记录数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countRecordsByScaleId(Long scaleId);

    /**
     * 查询用户测评统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserRecordStats(Long userId);

    /**
     * 查询量表测评统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleRecordStats(Long scaleId);

    /**
     * 查询测评趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    List<Map<String, Object>> selectRecordTrendStats(Integer days);

    /**
     * 查询测评完成率统计
     * 
     * @param scaleId 量表ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计信息
     */
    Map<String, Object> selectCompletionRateStats(Long scaleId, String startDate, String endDate);

    /**
     * 查询测评时长统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectDurationStats(Long scaleId);

    /**
     * 查询测评分数分布
     * 
     * @param scaleId 量表ID
     * @return 分布信息
     */
    List<Map<String, Object>> selectScoreDistribution(Long scaleId);

    /**
     * 查询用户最近测评记录
     * 
     * @param userId 用户ID
     * @param limit 限制数量
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectRecentRecordsByUserId(Long userId, Integer limit);

    /**
     * 查询热门测评记录
     * 
     * @param limit 限制数量
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectHotRecords(Integer limit);

    /**
     * 搜索测评记录
     * 
     * @param keyword 关键词
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param status 状态
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> searchRecords(String keyword, Long userId, Long scaleId, Integer status);

    /**
     * 更新测评记录状态
     * 
     * @param id 测评记录ID
     * @param status 状态
     * @return 结果
     */
    int updateRecordStatus(Long id, Integer status);

    /**
     * 批量更新测评记录状态
     * 
     * @param ids 测评记录ID数组
     * @param status 状态
     * @return 结果
     */
    int batchUpdateRecordStatus(Long[] ids, Integer status);

    /**
     * 查询超时未完成的测评记录
     * 
     * @param timeoutHours 超时小时数
     * @return 测评记录集合
     */
    List<PsyTAssessmentRecord> selectTimeoutRecords(Integer timeoutHours);

    /**
     * 清理过期的测评记录
     * 
     * @param expireDays 过期天数
     * @return 清理数量
     */
    int cleanExpiredRecords(Integer expireDays);

    /**
     * 查询测评记录排行榜
     * 
     * @param scaleId 量表ID
     * @param limit 限制数量
     * @return 排行榜信息
     */
    List<Map<String, Object>> selectRecordRanking(Long scaleId, Integer limit);

    /**
     * 查询用户测评进度
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 进度信息
     */
    Map<String, Object> selectUserTestProgress(Long userId, Long scaleId);

    /**
     * 恢复测评
     * 
     * @param sessionId 会话ID
     * @return 测评记录
     */
    PsyTAssessmentRecord resumeAssessment(String sessionId);

    /**
     * 保存测评进度
     * 
     * @param recordId 测评记录ID
     * @param currentQuestionNo 当前题目序号
     * @return 结果
     */
    int saveAssessmentProgress(Long recordId, Integer currentQuestionNo);

    /**
     * 验证测评权限
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 验证结果
     */
    Map<String, Object> validateAssessmentPermission(Long userId, Long scaleId);

    /**
     * 获取测评建议
     * 
     * @param recordId 测评记录ID
     * @return 建议内容
     */
    String getAssessmentSuggestions(Long recordId);
}
