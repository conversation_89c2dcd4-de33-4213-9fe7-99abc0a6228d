package com.xihuan.web.controller.system;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyAssessmentOrder;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.poi.ExcelUtil;
import com.xihuan.system.service.IPsyAssessmentOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 心理测评订单Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/system/assessment/order")
public class PsyAssessmentOrderController extends BaseController {
    
    @Autowired
    private IPsyAssessmentOrderService orderService;

    /**
     * 查询订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:list')")
    @GetMapping("/list")
    public TableDataInfo list(PsyAssessmentOrder order) {
        startPage();
        List<PsyAssessmentOrder> list = orderService.selectOrderList(order);
        return getDataTable(list);
    }

    /**
     * 导出订单列表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:export')")
    @Log(title = "心理测评订单", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, PsyAssessmentOrder order) {
        List<PsyAssessmentOrder> list = orderService.selectOrderList(order);
        ExcelUtil<PsyAssessmentOrder> util = new ExcelUtil<>(PsyAssessmentOrder.class);
        util.exportExcel(response, list, "心理测评订单数据");
    }

    /**
     * 获取订单详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(orderService.selectOrderWithDetails(id));
    }

    /**
     * 新增订单
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:add')")
    @Log(title = "心理测评订单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody PsyAssessmentOrder order) {
        return toAjax(orderService.insertOrder(order));
    }

    /**
     * 修改订单
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:edit')")
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody PsyAssessmentOrder order) {
        return toAjax(orderService.updateOrder(order));
    }

    /**
     * 删除订单
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:remove')")
    @Log(title = "心理测评订单", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(orderService.deleteOrderByIds(ids));
    }

    /**
     * 完成订单
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:edit')")
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PutMapping("/complete/{orderNo}")
    public AjaxResult complete(@PathVariable("orderNo") String orderNo) {
        return toAjax(orderService.completeOrder(orderNo));
    }

    /**
     * 取消订单
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:edit')")
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PutMapping("/cancel/{orderNo}")
    public AjaxResult cancel(@PathVariable("orderNo") String orderNo) {
        return toAjax(orderService.cancelOrder(orderNo));
    }

    /**
     * 处理退款
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:edit')")
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PutMapping("/refund/{orderNo}")
    public AjaxResult processRefund(@PathVariable("orderNo") String orderNo) {
        return toAjax(orderService.processRefund(orderNo));
    }

    /**
     * 处理过期订单
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:edit')")
    @Log(title = "心理测评订单", businessType = BusinessType.UPDATE)
    @PostMapping("/handleExpired")
    public AjaxResult handleExpiredOrders() {
        int count = orderService.handleExpiredOrders();
        return success("成功处理 " + count + " 个过期订单");
    }

    /**
     * 查询订单统计信息
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:list')")
    @GetMapping("/stats")
    public AjaxResult getOrderStats() {
        return success(orderService.selectOrderStats());
    }

    /**
     * 查询订单收入统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:list')")
    @GetMapping("/income")
    public AjaxResult getOrderIncomeStats(@RequestParam(value = "days", defaultValue = "30") Integer days) {
        return success(orderService.selectOrderIncomeStats(days));
    }

    /**
     * 查询热销量表
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:list')")
    @GetMapping("/hotSales")
    public AjaxResult getHotSalesScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        return success(orderService.selectHotSalesScales(limit));
    }

    /**
     * 查询订单趋势统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:list')")
    @GetMapping("/trend")
    public AjaxResult getOrderTrend(@RequestParam(value = "days", defaultValue = "30") Integer days) {
        return success(orderService.selectOrderTrend(days));
    }

    /**
     * 查询用户订单统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:list')")
    @GetMapping("/userStats/{userId}")
    public AjaxResult getUserOrderStats(@PathVariable("userId") Long userId) {
        return success(orderService.selectUserOrderStats(userId));
    }

    /**
     * 查询量表订单统计
     */
    @PreAuthorize("@ss.hasPermi('system:assessment:order:list')")
    @GetMapping("/scaleStats/{scaleId}")
    public AjaxResult getScaleOrderStats(@PathVariable("scaleId") Long scaleId) {
        return success(orderService.selectScaleOrderStats(scaleId));
    }
}
