package com.xihuan.system.domain.dto;


import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * 单个答案DTO
 */
@Data
public class AnswerDTO {
    @NotNull(message = "题目ID不能为空")
    private Long questionId;

    @NotNull(message = "选项ID不能为空")
    private Long optionId;

    @Min(value = 0, message = "得分不能小于0")
    @Max(value = 2, message = "得分不能大于2")
    private Integer score;

    private Integer responseSeconds;

    @Min(value = 1, message = "题目顺序从1开始")
    private Integer answerOrder;
}