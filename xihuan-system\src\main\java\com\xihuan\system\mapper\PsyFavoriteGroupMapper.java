package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyFavoriteGroup;
import com.xihuan.common.core.domain.entity.PsyFavoriteGroupRel;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 收藏分组数据访问层
 */
public interface PsyFavoriteGroupMapper {
    
    /**
     * 查询收藏分组列表
     */
    List<PsyFavoriteGroup> selectGroupList(PsyFavoriteGroup group);
    
    /**
     * 查询收藏分组详情
     */
    PsyFavoriteGroup selectGroupById(Long groupId);
    
    /**
     * 新增收藏分组
     */
    int insertGroup(PsyFavoriteGroup group);
    
    /**
     * 更新收藏分组
     */
    int updateGroup(PsyFavoriteGroup group);
    
    /**
     * 删除收藏分组
     */
    int deleteGroupById(Long groupId);
    
    /**
     * 批量删除收藏分组
     */
    int deleteGroupByIds(Long[] groupIds);
    
    /**
     * 查询用户默认分组
     */
    PsyFavoriteGroup selectDefaultGroup(Long userId);
    
    /**
     * 创建用户默认分组
     */
    int createDefaultGroup(Long userId);
    
    /**
     * 更新分组收藏数量
     */
    int updateGroupFavoriteCount(@Param("groupId") Long groupId, @Param("count") Integer count);
    
    /**
     * 查询分组关系
     */
    List<PsyFavoriteGroupRel> selectGroupRelList(@Param("favoriteId") Long favoriteId, @Param("groupId") Long groupId);
    
    /**
     * 新增分组关系
     */
    int insertGroupRel(PsyFavoriteGroupRel rel);
    
    /**
     * 删除分组关系
     */
    int deleteGroupRel(@Param("favoriteId") Long favoriteId, @Param("groupId") Long groupId);
    
    /**
     * 批量删除分组关系
     */
    int deleteGroupRelByFavoriteIds(Long[] favoriteIds);
    
    /**
     * 查询分组内收藏列表
     */
    List<Map<String, Object>> selectGroupFavorites(@Param("groupId") Long groupId, @Param("userId") Long userId);
}
