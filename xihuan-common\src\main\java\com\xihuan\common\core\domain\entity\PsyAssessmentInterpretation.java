package com.xihuan.common.core.domain.entity;

import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 测评结果解释表对象 psy_t_interpretation
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentInterpretation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 解释ID */
    @Excel(name = "解释ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 维度名称(为空表示总分) */
    @Excel(name = "维度名称")
    @Size(max = 50, message = "维度名称不能超过50个字符")
    private String dimension;

    /** 最小分数 */
    @Excel(name = "最小分数")
    @NotNull(message = "最小分数不能为空")
    @DecimalMin(value = "0.00", message = "最小分数不能为负数")
    private BigDecimal minScore;

    /** 最大分数 */
    @Excel(name = "最大分数")
    @NotNull(message = "最大分数不能为空")
    @DecimalMin(value = "0.00", message = "最大分数不能为负数")
    private BigDecimal maxScore;

    /** 等级名称 */
    @Excel(name = "等级名称")
    @NotBlank(message = "等级名称不能为空")
    @Size(max = 50, message = "等级名称不能超过50个字符")
    private String levelName;

    /** 等级描述 */
    @Excel(name = "等级描述")
    private String levelDescription;

    /** 建议 */
    @Excel(name = "建议")
    private String suggestions;

    /** 显示颜色 */
    @Excel(name = "显示颜色")
    @Size(max = 20, message = "显示颜色不能超过20个字符")
    private String color;

    /** 显示顺序 */
    @Excel(name = "显示顺序")
    @Min(value = 0, message = "显示顺序不能为负数")
    private Integer orderNum;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 量表信息 */
    private PsyAssessmentScale scale;

    // 扩展字段
    /** 量表名称 */
    private String scaleName;

    /** 分数范围显示 */
    private String scoreRange;

    /** 是否总分解释 */
    private Boolean isTotal;

    // 常量定义
    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 获取分数范围显示
     */
    public String getScoreRange() {
        if (minScore == null || maxScore == null) {
            return "";
        }
        return minScore + " - " + maxScore;
    }

    /**
     * 是否总分解释
     */
    public boolean isTotal() {
        return dimension == null || dimension.trim().isEmpty();
    }

    /**
     * 判断分数是否在范围内
     * 
     * @param score 分数
     * @return 是否在范围内
     */
    public boolean isInRange(BigDecimal score) {
        if (score == null || minScore == null || maxScore == null) {
            return false;
        }
        return score.compareTo(minScore) >= 0 && score.compareTo(maxScore) <= 0;
    }
}
