package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.store.PsyStores;

import java.util.List;

/**
 * 心理咨询门店Mapper接口
 */
public interface PsyStoresMapper {
    /**
     * 查询门店列表
     * 
     * @param psyStores 门店信息
     * @return 门店集合
     */
    List<PsyStores> selectPsyStoresList(PsyStores psyStores);

    /**
     * 查询门店信息
     * 
     * @param id 门店主键
     * @return 门店信息
     */
    PsyStores selectPsyStoresById(Long id);

    /**
     * 新增门店
     * 
     * @param psyStores 门店信息
     * @return 结果
     */
    int insertPsyStores(PsyStores psyStores);

    /**
     * 修改门店
     * 
     * @param psyStores 门店信息
     * @return 结果
     */
    int updatePsyStores(PsyStores psyStores);

    /**
     * 删除门店
     * 
     * @param id 门店主键
     * @return 结果
     */
    int deletePsyStoresById(Long id);

    /**
     * 批量删除门店
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deletePsyStoresByIds(Long[] ids);


} 