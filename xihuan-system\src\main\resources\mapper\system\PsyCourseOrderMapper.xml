<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyCourseOrderMapper">

    <!-- 基础订单字段映射 -->
    <resultMap id="BaseResultMap" type="PsyCourseOrder">
        <id column="id" property="id"/>
        <result column="order_no" property="orderNo"/>
        <result column="course_id" property="courseId"/>
        <result column="user_id" property="userId"/>
        <result column="original_price" property="originalPrice"/>
        <result column="payment_amount" property="paymentAmount"/>
        <result column="payment_method" property="paymentMethod"/>
        <result column="payment_time" property="paymentTime"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="refund_time" property="refundTime"/>
        <result column="cancel_time" property="cancelTime"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_discount" property="couponDiscount"/>
        <result column="membership_discount" property="membershipDiscount"/>
        <result column="points_used" property="pointsUsed"/>
        <result column="points_discount" property="pointsDiscount"/>
        <result column="is_membership_free" property="isMembershipFree"/>
        <result column="status" property="status"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="create_time" property="createTime"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <!-- 订单详情映射（包含课程和用户信息） -->
    <resultMap id="OrderWithDetailsMap" type="PsyCourseOrder" extends="BaseResultMap">
        <association property="course" javaType="PsyCourse">
            <id column="course_id" property="id"/>
            <result column="course_title" property="title"/>
            <result column="course_cover_image" property="coverImage"/>
            <result column="course_price" property="price"/>
        </association>
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
            <result column="phonenumber" property="phonenumber"/>
        </association>
    </resultMap>

    <!-- 查询订单列表 -->
    <select id="selectOrderList" parameterType="PsyCourseOrder" resultMap="BaseResultMap">
        SELECT * FROM psy_course_order
        <where>
            del_flag = 0
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
            <if test="courseId != null">
                AND course_id = #{courseId}
            </if>
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="paymentMethod != null and paymentMethod != ''">
                AND payment_method = #{paymentMethod}
            </if>
            <if test="params.beginTime != null and params.endTime != null">
                AND create_time BETWEEN #{params.beginTime} AND #{params.endTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>

    <!-- 根据ID查询订单 -->
    <select id="selectOrderById" resultMap="BaseResultMap">
        SELECT * FROM psy_course_order WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据订单号查询订单 -->
    <select id="selectOrderByOrderNo" resultMap="BaseResultMap">
        SELECT * FROM psy_course_order WHERE order_no = #{orderNo} AND del_flag = 0
    </select>

    <!-- 查询订单详情 -->
    <select id="selectOrderWithDetails" resultMap="OrderWithDetailsMap">
        SELECT 
            o.*,
            c.title AS course_title,
            c.cover_image AS course_cover_image,
            c.price AS course_price,
            u.user_name,
            u.nick_name,
            u.phonenumber
        FROM psy_course_order o
        LEFT JOIN psy_course c ON o.course_id = c.id
        LEFT JOIN sys_user u ON o.user_id = u.user_id
        WHERE o.id = #{id} AND o.del_flag = 0
    </select>

    <!-- 根据用户ID查询订单列表 -->
    <select id="selectOrdersByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_course_order 
        WHERE user_id = #{userId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <!-- 根据课程ID查询订单列表 -->
    <select id="selectOrdersByCourseId" resultMap="BaseResultMap">
        SELECT * FROM psy_course_order 
        WHERE course_id = #{courseId} AND del_flag = 0
        ORDER BY id DESC
    </select>

    <!-- 新增订单 -->
    <insert id="insertOrder" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_course_order (
            order_no, course_id, user_id, original_price, payment_amount,
            payment_method, payment_time, transaction_id, refund_amount, refund_time,
            cancel_time, coupon_id, coupon_discount, membership_discount,
            points_used, points_discount, is_membership_free, status, del_flag,
            create_by, create_time, update_by, update_time, remark
        ) VALUES (
            #{orderNo}, #{courseId}, #{userId}, #{originalPrice}, #{paymentAmount},
            #{paymentMethod}, #{paymentTime}, #{transactionId}, #{refundAmount}, #{refundTime},
            #{cancelTime}, #{couponId}, #{couponDiscount}, #{membershipDiscount},
            #{pointsUsed}, #{pointsDiscount}, #{isMembershipFree}, #{status}, #{delFlag},
            #{createBy}, #{createTime}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <!-- 修改订单 -->
    <update id="updateOrder" parameterType="PsyCourseOrder">
        UPDATE psy_course_order
        <set>
            <if test="originalPrice != null">original_price = #{originalPrice},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="paymentTime != null">payment_time = #{paymentTime},</if>
            <if test="transactionId != null">transaction_id = #{transactionId},</if>
            <if test="refundAmount != null">refund_amount = #{refundAmount},</if>
            <if test="refundTime != null">refund_time = #{refundTime},</if>
            <if test="cancelTime != null">cancel_time = #{cancelTime},</if>
            <if test="couponId != null">coupon_id = #{couponId},</if>
            <if test="couponDiscount != null">coupon_discount = #{couponDiscount},</if>
            <if test="membershipDiscount != null">membership_discount = #{membershipDiscount},</if>
            <if test="pointsUsed != null">points_used = #{pointsUsed},</if>
            <if test="pointsDiscount != null">points_discount = #{pointsDiscount},</if>
            <if test="isMembershipFree != null">is_membership_free = #{isMembershipFree},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除订单 -->
    <update id="deleteOrderById">
        UPDATE psy_course_order SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除订单 -->
    <update id="deleteOrderByIds">
        UPDATE psy_course_order SET del_flag = 1
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 更新订单支付状态 -->
    <update id="updateOrderPaymentStatus">
        UPDATE psy_course_order
        SET status = #{status},
            payment_method = #{paymentMethod},
            transaction_id = #{transactionId},
            payment_time = #{paymentTime},
            update_time = NOW()
        WHERE order_no = #{orderNo}
    </update>

    <!-- 更新订单退款信息 -->
    <update id="updateOrderRefund">
        UPDATE psy_course_order
        SET refund_amount = #{refundAmount},
            refund_time = #{refundTime},
            update_time = NOW()
        WHERE order_no = #{orderNo}
    </update>

    <!-- 生成订单号 -->
    <select id="generateOrderNo" resultType="String">
        SELECT CONCAT('CO', DATE_FORMAT(NOW(), '%Y%m%d%H%i%s'), LPAD(FLOOR(RAND() * 1000), 3, '0'))
    </select>

    <!-- 检查用户是否已购买课程 -->
    <select id="checkUserPurchased" resultType="int">
        SELECT COUNT(*) FROM psy_course_order 
        WHERE user_id = #{userId} AND course_id = #{courseId} 
        AND status IN (1, 2) AND del_flag = 0
    </select>

</mapper>
