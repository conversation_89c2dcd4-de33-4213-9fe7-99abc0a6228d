package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyConsultationRecord;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 心理咨询记录表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyConsultationRecordMapper {
    
    /**
     * 查询咨询记录列表
     * 
     * @param record 咨询记录信息
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordList(PsyConsultationRecord record);

    /**
     * 根据ID查询咨询记录
     * 
     * @param id 咨询记录ID
     * @return 咨询记录信息
     */
    PsyConsultationRecord selectRecordById(Long id);

    /**
     * 查询咨询记录详情（包含用户、咨询师等信息）
     * 
     * @param id 咨询记录ID
     * @return 咨询记录详情
     */
    PsyConsultationRecord selectRecordWithDetails(Long id);

    /**
     * 根据用户ID查询咨询记录列表
     * 
     * @param userId 用户ID
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByUserId(Long userId);

    /**
     * 根据咨询师ID查询咨询记录列表
     * 
     * @param consultantId 咨询师ID
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByConsultantId(Long consultantId);

    /**
     * 根据订单ID查询咨询记录
     * 
     * @param orderId 订单ID
     * @return 咨询记录信息
     */
    PsyConsultationRecord selectRecordByOrderId(Long orderId);

    /**
     * 新增咨询记录
     * 
     * @param record 咨询记录信息
     * @return 结果
     */
    int insertRecord(PsyConsultationRecord record);

    /**
     * 修改咨询记录
     * 
     * @param record 咨询记录信息
     * @return 结果
     */
    int updateRecord(PsyConsultationRecord record);

    /**
     * 删除咨询记录
     * 
     * @param id 咨询记录ID
     * @return 结果
     */
    int deleteRecordById(Long id);

    /**
     * 批量删除咨询记录
     * 
     * @param ids 需要删除的咨询记录ID
     * @return 结果
     */
    int deleteRecordByIds(Long[] ids);

    /**
     * 统计用户咨询次数
     * 
     * @param userId 用户ID
     * @return 咨询次数
     */
    int countUserConsultations(Long userId);

    /**
     * 统计咨询师咨询次数
     * 
     * @param consultantId 咨询师ID
     * @return 咨询次数
     */
    int countConsultantConsultations(Long consultantId);

    /**
     * 统计用户咨询总时长
     * 
     * @param userId 用户ID
     * @return 总时长(分钟)
     */
    int sumUserConsultationDuration(Long userId);

    /**
     * 统计咨询师咨询总时长
     * 
     * @param consultantId 咨询师ID
     * @return 总时长(分钟)
     */
    int sumConsultantConsultationDuration(Long consultantId);

    /**
     * 计算咨询师平均评分
     * 
     * @param consultantId 咨询师ID
     * @return 平均评分
     */
    BigDecimal calculateConsultantAvgRating(Long consultantId);

    /**
     * 查询用户与咨询师的咨询记录
     * 
     * @param userId 用户ID
     * @param consultantId 咨询师ID
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByUserAndConsultant(@Param("userId") Long userId, @Param("consultantId") Long consultantId);

    /**
     * 查询指定时间段的咨询记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 咨询记录集合
     */
    List<PsyConsultationRecord> selectRecordsByTimeRange(@Param("startTime") Date startTime, @Param("endTime") Date endTime);

    /**
     * 更新咨询记录状态
     * 
     * @param id 记录ID
     * @param actualStartTime 实际开始时间
     * @param actualEndTime 实际结束时间
     * @param duration 实际时长
     * @return 结果
     */
    int updateRecordStatus(@Param("id") Long id, @Param("actualStartTime") Date actualStartTime, 
                          @Param("actualEndTime") Date actualEndTime, @Param("duration") Integer duration);

    /**
     * 更新用户评价
     * 
     * @param id 记录ID
     * @param userRating 用户评价
     * @return 结果
     */
    int updateUserRating(@Param("id") Long id, @Param("userRating") Integer userRating);
}
