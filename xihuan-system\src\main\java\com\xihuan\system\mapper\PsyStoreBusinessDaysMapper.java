package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.store.PsyStoreBusinessDays;

import java.util.List;

/**
 * 门店营业日配置Mapper接口
 */
public interface PsyStoreBusinessDaysMapper {
    /**
     * 查询门店营业日配置列表
     * 
     * @param psyStoreBusinessDays 门店营业日配置信息
     * @return 门店营业日配置集合
     */
    List<PsyStoreBusinessDays> selectPsyStoreBusinessDaysList(PsyStoreBusinessDays psyStoreBusinessDays);

    /**
     * 查询门店营业日配置信息
     * 
     * @param id 营业日配置主键
     * @return 门店营业日配置信息
     */
    PsyStoreBusinessDays selectPsyStoreBusinessDaysById(Long id);

    /**
     * 根据门店ID查询营业日配置
     * 
     * @param storeId 门店ID
     * @return 门店营业日配置信息
     */
    PsyStoreBusinessDays selectPsyStoreBusinessDaysByStoreId(Long storeId);

    /**
     * 新增门店营业日配置
     * 
     * @param psyStoreBusinessDays 门店营业日配置信息
     * @return 结果
     */
    int insertPsyStoreBusinessDays(PsyStoreBusinessDays psyStoreBusinessDays);

    /**
     * 修改门店营业日配置
     * 
     * @param psyStoreBusinessDays 门店营业日配置信息
     * @return 结果
     */
    int updatePsyStoreBusinessDays(PsyStoreBusinessDays psyStoreBusinessDays);

    /**
     * 删除门店营业日配置
     * 
     * @param id 营业日配置主键
     * @return 结果
     */
    int deletePsyStoreBusinessDaysById(Long id);

    /**
     * 根据门店ID删除营业日配置
     * 
     * @param storeId 门店ID
     * @return 结果
     */
    int deletePsyStoreBusinessDaysByStoreId(Long storeId);
} 