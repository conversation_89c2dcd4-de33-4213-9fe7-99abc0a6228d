package com.xihuan.system.service.wxService;

import com.xihuan.common.core.domain.entity.PsyAppointment;
import com.xihuan.system.domain.dto.PsyAppointmentTimeDTO;

import java.util.Date;
import java.util.List;

public interface PsyAppointmentService {
    /**
     * 生成预约时段
     */
    void genSlots();

    /**
     * 获取咨询师可用时段
     */
    List<PsyAppointmentTimeDTO> getAvailableTimes(Long counselorId, int days);

    /**
     * 查询公共时段
     */
    List<PsyAppointment> selectPublicSlots(Date startDate, Date endDate);

    /**
     * 获取格式化的公共时段
     */
    List<PsyAppointmentTimeDTO> getPublicTimesFormatted(Date startDate, Date endDate);
}