package com.xihuan.system.service.task;

import com.xihuan.common.utils.spring.SpringUtils;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.ISysConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDate;

/**
 * 时间槽定时任务服务
 * 
 * <AUTHOR>
 */
@Service
public class PsyTimeSlotTaskService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyTimeSlotTaskService.class);
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;

    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;

    /**
     * 生成未来时间槽
     * 供Quartz定时任务调用
     */
    public void generateFutureTimeSlots() {
        logger.info("开始执行定时任务：生成未来时间槽");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate startDate = LocalDate.now().plusDays(1); // 从明天开始
            LocalDate endDate = startDate.plusDays(6); // 生成7天的时间槽

            int count = timeSlotService.generateSlotsForAllCounselors(startDate, endDate);

            // 生成系统公共时间槽
            int systemSlotCount = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, 1L);

            logger.info("定时任务完成：成功生成 {} 个咨询师时间槽和 {} 个系统时间槽，日期范围：{} 到 {}",
                count, systemSlotCount, startDate, endDate);
        } catch (Exception e) {
            logger.error("生成未来时间槽定时任务执行失败", e);
        }
    }

    /**
     * 清理过期时间槽
     * 供Quartz定时任务调用
     */
    public void cleanExpiredTimeSlots() {
        logger.info("开始执行定时任务：清理过期时间槽");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate beforeDate = LocalDate.now().minusDays(7);
            int count = timeSlotService.cleanExpiredSlots(beforeDate);

            // 清理过期的系统时间槽
            int systemSlotCount = systemTimeSlotService.cleanExpiredSystemSlots(beforeDate, 1L);

            logger.info("定时任务完成：成功清理 {} 个过期咨询师时间槽和 {} 个过期系统时间槽，清理日期：{} 之前",
                count, systemSlotCount, beforeDate);
        } catch (Exception e) {
            logger.error("清理过期时间槽定时任务执行失败", e);
        }
    }

    /**
     * 更新过期时间槽状态
     * 供Quartz定时任务调用
     */
    public void updateExpiredSlotStatus() {
        logger.info("开始执行定时任务：更新过期时间槽状态");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);

            int count = timeSlotService.updateExpiredSlotStatus();

            if (count > 0) {
                logger.info("定时任务完成：成功更新 {} 个过期时间槽状态", count);
            }
        } catch (Exception e) {
            logger.error("更新过期时间槽状态定时任务执行失败", e);
        }
    }

    /**
     * 更新过期时间槽状态（支持延后过期配置）
     * 供Quartz定时任务调用
     */
    public void updateExpiredSlotStatusWithDelay() {
        logger.info("开始执行定时任务：更新过期时间槽状态（支持延后配置）");

        try {
            // 动态获取服务Bean
            IPsyTimeSlotService timeSlotService = SpringUtils.getBean(IPsyTimeSlotService.class);

            int count = timeSlotService.updateExpiredSlotStatusWithDelay();

            if (count > 0) {
                logger.info("定时任务完成：成功更新 {} 个过期时间槽状态（已考虑延后配置）", count);
            }
        } catch (Exception e) {
            logger.error("更新过期时间槽状态（延后配置）定时任务执行失败", e);
        }
    }

    /**
     * 检查是否启用延后过期功能
     *
     * @return true-启用延后2小时，false-不延后
     */
    public boolean isDelayExpirationEnabled() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
        } catch (Exception e) {
            logger.warn("获取延后过期配置失败，使用默认值：false", e);
            return false;
        }
    }

    /**
     * 获取延后过期的小时数
     *
     * @return 延后小时数，默认2小时
     */
    public int getDelayExpirationHours() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            return Integer.parseInt(configValue);
        } catch (Exception e) {
            logger.warn("获取延后过期小时数配置失败，使用默认值：2小时", e);
            return 2;
        }
    }

    /**
     * 更新系统时间槽可用性统计
     * 供Quartz定时任务调用
     */
    public void updateSystemSlotAvailability() {
        logger.info("开始执行定时任务：更新系统时间槽可用性统计");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate today = LocalDate.now();
            LocalDate tomorrow = today.plusDays(1);

            // 更新今天和明天的系统时间槽统计
            int todayCount = systemTimeSlotService.updateAvailabilityStats(today, 1L);
            int tomorrowCount = systemTimeSlotService.updateAvailabilityStats(tomorrow, 1L);

            // 同时更新系统时间槽的过期状态
            int expiredCount = systemTimeSlotService.updateExpiredSlotStatusWithDelay(1L);

            logger.info("定时任务完成：更新了今天 {} 个和明天 {} 个系统时间槽的可用性统计，更新了 {} 个过期状态",
                todayCount, tomorrowCount, expiredCount);
        } catch (Exception e) {
            logger.error("更新系统时间槽可用性统计定时任务执行失败", e);
        }
    }

    /**
     * 生成系统公共时间槽
     * 供Quartz定时任务调用
     */
    public void generateSystemTimeSlots() {
        logger.info("开始执行定时任务：生成系统公共时间槽");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate startDate = LocalDate.now().plusDays(1); // 从明天开始
            LocalDate endDate = startDate.plusDays(6); // 生成7天的时间槽

            int count = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, 1L);

            logger.info("定时任务完成：成功生成 {} 个系统时间槽，日期范围：{} 到 {}", count, startDate, endDate);
        } catch (Exception e) {
            logger.error("生成系统公共时间槽定时任务执行失败", e);
        }
    }

    /**
     * 更新系统公共时间槽过期状态
     * 供Quartz定时任务调用
     */
    public void updateSystemSlotExpiredStatus() {
        logger.info("开始执行定时任务：更新系统公共时间槽过期状态");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            int count = systemTimeSlotService.updateExpiredSlotStatusWithDelay(1L);

            logger.info("定时任务完成：成功更新 {} 个系统公共时间槽的过期状态", count);
        } catch (Exception e) {
            logger.error("更新系统公共时间槽过期状态定时任务执行失败", e);
        }
    }

    /**
     * 清理过期的系统时间槽
     * 供Quartz定时任务调用
     */
    public void cleanExpiredSystemSlots() {
        logger.info("开始执行定时任务：清理过期系统时间槽");

        try {
            // 动态获取服务Bean
            IPsySystemTimeSlotService systemTimeSlotService = SpringUtils.getBean(IPsySystemTimeSlotService.class);

            LocalDate beforeDate = LocalDate.now().minusDays(7);
            int count = systemTimeSlotService.cleanExpiredSystemSlots(beforeDate, 1L);

            logger.info("定时任务完成：成功清理 {} 个过期系统时间槽，清理日期：{} 之前", count, beforeDate);
        } catch (Exception e) {
            logger.error("清理过期系统时间槽定时任务执行失败", e);
        }
    }

    /**
     * 手动触发生成时间槽（用于测试或紧急情况）
     *
     * @param days 生成未来多少天的时间槽
     * @return 生成的时间槽数量
     */
    public int manualGenerateTimeSlots(int days) {
        logger.info("手动触发生成时间槽，天数：{}", days);

        try {
            // 动态获取服务Bean（手动触发时也可能存在注入问题）
            IPsyTimeSlotService timeSlotService = this.timeSlotService != null ?
                this.timeSlotService : SpringUtils.getBean(IPsyTimeSlotService.class);

            LocalDate startDate = LocalDate.now().plusDays(1);
            LocalDate endDate = startDate.plusDays(days - 1);

            int count = timeSlotService.generateSlotsForAllCounselors(startDate, endDate);

            logger.info("手动生成时间槽完成：成功生成 {} 个时间槽", count);
            return count;
        } catch (Exception e) {
            logger.error("手动生成时间槽失败", e);
            return 0;
        }
    }

    /**
     * 手动触发清理过期时间槽
     *
     * @param days 清理多少天前的时间槽
     * @return 清理的时间槽数量
     */
    public int manualCleanExpiredSlots(int days) {
        logger.info("手动触发清理过期时间槽，天数：{}", days);

        try {
            // 动态获取服务Bean（手动触发时也可能存在注入问题）
            IPsyTimeSlotService timeSlotService = this.timeSlotService != null ?
                this.timeSlotService : SpringUtils.getBean(IPsyTimeSlotService.class);

            LocalDate beforeDate = LocalDate.now().minusDays(days);
            int count = timeSlotService.cleanExpiredSlots(beforeDate);

            logger.info("手动清理过期时间槽完成：成功清理 {} 个时间槽", count);
            return count;
        } catch (Exception e) {
            logger.error("手动清理过期时间槽失败", e);
            return 0;
        }
    }

    /**
     * 获取定时任务状态信息
     *
     * @return 状态信息
     */
    public String getTaskStatus() {
        return "时间槽定时任务服务运行正常";
    }

}
