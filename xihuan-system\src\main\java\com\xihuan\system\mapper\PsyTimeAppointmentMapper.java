package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyTimeAppointment;
import com.xihuan.common.core.domain.entity.PsyTimeAppointmentSlot;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 预约记录Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyTimeAppointmentMapper {
    
    /**
     * 查询预约记录列表
     * 
     * @param appointment 预约记录
     * @return 预约记录集合
     */
    List<PsyTimeAppointment> selectAppointmentList(PsyTimeAppointment appointment);
    
    /**
     * 根据ID查询预约记录
     * 
     * @param id 预约记录主键
     * @return 预约记录
     */
    PsyTimeAppointment selectAppointmentById(String id);
    
    /**
     * 根据用户ID查询预约记录
     * 
     * @param userId 用户ID
     * @param status 预约状态（可选）
     * @return 预约记录集合
     */
    List<PsyTimeAppointment> selectAppointmentsByUserId(
        @Param("userId") Long userId,
        @Param("status") Integer status
    );
    
    /**
     * 根据咨询师ID查询预约记录
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 预约记录集合
     */
    List<PsyTimeAppointment> selectAppointmentsByCounselor(
        @Param("counselorId") Long counselorId,
        @Param("startDate") String startDate,
        @Param("endDate") String endDate
    );
    
    /**
     * 新增预约记录
     * 
     * @param appointment 预约记录
     * @return 结果
     */
    int insertAppointment(PsyTimeAppointment appointment);
    
    /**
     * 修改预约记录
     * 
     * @param appointment 预约记录
     * @return 结果
     */
    int updateAppointment(PsyTimeAppointment appointment);
    
    /**
     * 更新预约状态
     * 
     * @param id 预约ID
     * @param status 新状态
     * @return 结果
     */
    int updateAppointmentStatus(@Param("id") String id, @Param("status") Integer status);
    
    /**
     * 更新支付状态
     * 
     * @param id 预约ID
     * @param paymentStatus 支付状态
     * @param paymentId 支付单号
     * @return 结果
     */
    int updatePaymentStatus(
        @Param("id") String id,
        @Param("paymentStatus") Integer paymentStatus,
        @Param("paymentId") String paymentId
    );
    
    /**
     * 删除预约记录
     * 
     * @param id 预约记录主键
     * @return 结果
     */
    int deleteAppointmentById(String id);
    
    /**
     * 批量删除预约记录
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteAppointmentByIds(String[] ids);
    
    /**
     * 检查时间槽是否已被预约
     * 
     * @param slotId 时间槽ID
     * @return 预约记录数量
     */
    int checkSlotBooked(@Param("slotId") Long slotId);
    
    /**
     * 生成预约ID
     * 
     * @return 新的预约ID
     */
    String generateAppointmentId();
}
