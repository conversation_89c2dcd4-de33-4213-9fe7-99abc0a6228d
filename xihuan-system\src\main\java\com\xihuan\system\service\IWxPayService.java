package com.xihuan.system.service;

import com.xihuan.system.domain.dto.WxPayDTO;

/**
 * 微信支付服务接口
 */
public interface IWxPayService {

    /**
     * 创建支付订单
     *
     * @param request 创建支付订单请求
     * @return 支付订单响应
     */
    WxPayDTO.PayOrderResponse createPayOrder(WxPayDTO.CreatePayOrderRequest request);

    /**
     * 查询支付订单
     *
     * @param request 查询订单请求
     * @return 查询订单响应
     */
    WxPayDTO.QueryOrderResponse queryOrder(WxPayDTO.QueryOrderRequest request);

    /**
     * 申请退款
     *
     * @param request 申请退款请求
     * @return 申请退款响应
     */
    WxPayDTO.RefundResponse refund(WxPayDTO.RefundRequest request);

    /**
     * 查询退款
     *
     * @param outRefundNo 商户退款单号
     * @return 退款查询响应
     */
    WxPayDTO.RefundResponse queryRefund(String outRefundNo);

    /**
     * 处理支付回调通知
     *
     * @param notifyData 回调通知数据
     * @return 处理结果
     */
    boolean handlePayNotify(String notifyData);

    /**
     * 处理退款回调通知
     *
     * @param notifyData 回调通知数据
     * @return 处理结果
     */
    boolean handleRefundNotify(String notifyData);

    /**
     * 关闭订单
     *
     * @param outTradeNo 商户订单号
     * @return 关闭结果
     */
    boolean closeOrder(String outTradeNo);

    /**
     * 验证回调签名
     *
     * @param timestamp 时间戳
     * @param nonce 随机字符串
     * @param body 请求体
     * @param signature 签名
     * @return 验证结果
     */
    boolean verifyNotifySignature(String timestamp, String nonce, String body, String signature);

    /**
     * 解密回调数据
     *
     * @param associatedData 附加数据
     * @param nonce 随机串
     * @param ciphertext 数据密文
     * @return 解密后的数据
     */
    String decryptNotifyData(String associatedData, String nonce, String ciphertext);
}
