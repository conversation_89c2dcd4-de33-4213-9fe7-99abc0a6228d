package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsySearchSuggestion;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 搜索建议Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsySearchSuggestionMapper {
    
    /**
     * 查询搜索建议
     *
     * @param id 搜索建议主键
     * @return 搜索建议
     */
    PsySearchSuggestion selectSearchSuggestionById(Long id);

    /**
     * 查询搜索建议列表
     *
     * @param searchSuggestion 搜索建议
     * @return 搜索建议集合
     */
    List<PsySearchSuggestion> selectSearchSuggestionList(PsySearchSuggestion searchSuggestion);

    /**
     * 新增搜索建议
     *
     * @param searchSuggestion 搜索建议
     * @return 结果
     */
    int insertSearchSuggestion(PsySearchSuggestion searchSuggestion);

    /**
     * 修改搜索建议
     *
     * @param searchSuggestion 搜索建议
     * @return 结果
     */
    int updateSearchSuggestion(PsySearchSuggestion searchSuggestion);

    /**
     * 删除搜索建议
     *
     * @param id 搜索建议主键
     * @return 结果
     */
    int deleteSearchSuggestionById(Long id);

    /**
     * 批量删除搜索建议
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    int deleteSearchSuggestionByIds(Long[] ids);
    
    /**
     * 根据关键词前缀查询建议
     *
     * @param keyword 关键词前缀
     * @param limit 限制数量
     * @return 建议列表
     */
    List<String> selectSuggestionsByKeyword(@Param("keyword") String keyword, @Param("limit") Integer limit);
    
    /**
     * 增加搜索次数
     *
     * @param keyword 关键词
     * @return 结果
     */
    int incrementSearchCount(@Param("keyword") String keyword);
    
    /**
     * 获取高优先级建议
     *
     * @param limit 限制数量
     * @return 建议列表
     */
    List<String> selectHighPrioritySuggestions(@Param("limit") Integer limit);
}
