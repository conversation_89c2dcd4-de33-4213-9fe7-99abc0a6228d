package com.xihuan.system.service.wxServiceImpl;

import com.xihuan.common.core.domain.TreeSelect;
import com.xihuan.common.core.domain.entity.PsyCategory;
import com.xihuan.common.core.domain.entity.PsyProduct;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.system.mapper.PsyCategoryMapper;
import com.xihuan.system.service.wxService.PsyCategoryService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class PsyCategoryServiceImpl implements PsyCategoryService {

    @Autowired
    private PsyCategoryMapper categoryMapper;


    /**
     * 查询分类列表（非树形）
     * @param category 查询条件
     * @return 分类列表
     */
    @Override
    public List<PsyCategory> selectCategoryList(PsyCategory category) {
        List<PsyCategory> categories = categoryMapper.selectCategoryList(category);
        return buildTreeStructure(categories);
    }

    /**
     * 新增分类
     * @param category 分类信息
     * @return 结果
     * @throws ServiceException 同名分类存在时抛出
     */
    @Transactional
    @Override
    public int insertCategory(PsyCategory category) {
        // 校验同一父节点下分类名称是否重复
        checkCategoryUnique(category);
        // 校验父分类状态
        checkParentStatus(category.getParentId());
        return categoryMapper.insertCategory(category);
    }

    /**
     * 修改分类信息
     * @param category 分类信息
     * @return 结果
     * @throws ServiceException 同名分类存在/父分类状态异常时抛出
     */
    @Transactional
    @Override
    public int updateCategory(PsyCategory category) {
        // 禁止修改父分类为自己
        if (category.getCategoryId().equals(category.getParentId())) {
            throw new ServiceException("父分类不能选择自己");
        }
        // 校验唯一性
        checkCategoryUnique(category);
        // 校验父分类状态
        checkParentStatus(category.getParentId());
        return categoryMapper.updateCategory(category);
    }

    /**
     * 构建前端所需树结构
     * @param categories 分类列表
     * @return 树结构列表
     */
    @Override
    public List<TreeSelect> buildCategoryTree(List<PsyCategory> categories) {
        // 过滤空值和无效数据
        List<PsyCategory> validCategories = categories.stream()
                .filter(c -> c != null && c.getCategoryId() != null)
                .collect(Collectors.toList());

        Map<Long, PsyCategory> categoryMap = validCategories.stream()
                .collect(Collectors.toMap(PsyCategory::getCategoryId, c -> c));

        List<PsyCategory> rootCategories = new ArrayList<>();
        validCategories.forEach(category -> {
            if (category.getParentId() == null || category.getParentId() == 0L) {
                rootCategories.add(category);
            } else {
                PsyCategory parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    parent.getChildren().add(category);
                }
            }
        });

        // 排序时处理空值
        rootCategories.sort(Comparator.comparingInt(c -> c.getOrderNum() != null ? c.getOrderNum() : 0));
        return rootCategories.stream()
                .map(TreeSelect::new)
                .collect(Collectors.toList());
    }

    /**
     * 校验分类名称唯一性
     * @param category 待校验分类
     * @throws ServiceException 名称冲突时抛出
     */
    private void checkCategoryUnique(PsyCategory category) {
        PsyCategory check = new PsyCategory();
        check.setParentId(category.getParentId());
        check.setCategoryName(category.getCategoryName());
        check.setCategoryId(category.getCategoryId()); // 排除自身

        if (categoryMapper.checkCategoryUnique(check) > 0) {
            throw new ServiceException("同层级下已存在相同分类名称");
        }
    }

    /**
     * 校验父分类状态
     * @param parentId 父分类ID
     * @throws ServiceException 父分类被禁用或不存在时抛出
     */
    private void checkParentStatus(Long parentId) {
        if (parentId != 0) {
            PsyCategory parent = categoryMapper.selectCategoryById(parentId);
            if (parent == null) {
                throw new ServiceException("父分类不存在");
            }
            if (PsyCategory.CATEGORY_DISABLED.equals(parent.getStatus())) {
                throw new ServiceException("父分类已停用，无法操作");
            }
        }
    }

    // 新增方法实现
    @Override
    public PsyCategory getCategoryWithProducts(Long categoryId) {
        PsyCategory category = categoryMapper.selectCategoryById(categoryId);
        if (category == null) {
            throw new ServiceException("分类不存在");
        }
        List<PsyCategory> categoryWithProducts = categoryMapper.selectCategoryWithProducts(categoryId);
        if (!CollectionUtils.isEmpty(categoryWithProducts)) {
            // 合并产品信息
            category.setProducts(categoryWithProducts.get(0).getProducts());
        }
        return category;
    }

    @Override
    public List<PsyCategory> getChildrenCategories(Long parentId) {
        List<PsyCategory> children = categoryMapper.selectChildrenByParentId(parentId);
        // 按显示顺序排序
        return children.stream()
                .sorted(Comparator.comparingInt(PsyCategory::getOrderNum))
                .collect(Collectors.toList());
    }

    // 增强原有删除方法
    @Transactional
    @Override
    public int deleteCategoryById(Long categoryId) {
        // 新增子分类检查
        List<PsyCategory> children = this.getChildrenCategories(categoryId);
        if (!children.isEmpty()) {
            throw new ServiceException("存在子分类，请先删除子分类");
        }

        // 原有产品关联检查
        if (categoryMapper.checkExistProducts(categoryId) > 0) {
            throw new ServiceException("分类下存在关联产品，禁止删除");
        }

        return categoryMapper.deleteCategoryById(categoryId);
    }

    /**
     * 构建带产品的分类树
     */
    public List<TreeSelect> buildCategoryTreeWithProducts(List<PsyCategory> categories) {
        // 1. 获取完整分类数据（包含产品）
        List<PsyCategory> fullCategories = categoryMapper.selectCategoryListWithProducts();

        // 2. 建立分类索引
        Map<Long, PsyCategory> categoryMap = new HashMap<>();
        fullCategories.forEach(c -> {
            if (!categoryMap.containsKey(c.getCategoryId())) {
                c.setProducts(new ArrayList<>()); // 初始化产品列表
                categoryMap.put(c.getCategoryId(), c);
            }
            // 合并产品数据
            if (c.getProducts() != null && !c.getProducts().isEmpty()) {
                categoryMap.get(c.getCategoryId()).getProducts().addAll(c.getProducts());
            }
        });

        // 3. 构建树结构（同原方法）
        List<PsyCategory> tree = buildTreeStructure(new ArrayList<>(categoryMap.values()));

        // 4. 转换为TreeSelect并携带产品信息
        return tree.stream()
                .map(c -> convertToTreeSelectWithProducts(c))
                .collect(Collectors.toList());
    }

    /**
     * TreeSelect扩展产品信息转换
     */
    private TreeSelect convertToTreeSelectWithProducts(PsyCategory category) {
        TreeSelect treeSelect = new TreeSelect();
        treeSelect.setId(category.getCategoryId());
        treeSelect.setLabel(category.getCategoryName());

        // 携带产品信息
        Map<String, Object> extraData = new HashMap<>();
        extraData.put("products", category.getProducts());
        treeSelect.setExtra(extraData);

        // 递归处理子节点
        if (!CollectionUtils.isEmpty(category.getChildren())) {
            List<TreeSelect> children = category.getChildren().stream()
                    .map(this::convertToTreeSelectWithProducts)
                    .collect(Collectors.toList());
            treeSelect.setChildren(children);
        }

        return treeSelect;
    }

    /**
     * 构建树形结构
     */
    private List<PsyCategory> buildTreeStructure(List<PsyCategory> allCategories) {
        Map<Long, PsyCategory> categoryMap = new HashMap<>();
        List<PsyCategory> rootCategories = new ArrayList<>();

        // 1. 建立映射关系
        for (PsyCategory category : allCategories) {
            categoryMap.put(category.getCategoryId(), category);
        }

        // 2. 构建树形结构
        for (PsyCategory category : allCategories) {
            if (category.getParentId() == 0L) {
                rootCategories.add(category);
            } else {
                PsyCategory parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    parent.getChildren().add(category);
                }
            }
        }

        // 3. 递归排序
        sortTreeNodes(rootCategories);
        return rootCategories;
    }

    /**
     * 递归排序树节点
     */
    private void sortTreeNodes(List<PsyCategory> nodes) {
        if (nodes == null || nodes.isEmpty()) {
            return;
        }
        
        // 按orderNum排序
        nodes.sort(Comparator.comparing(PsyCategory::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())));
        
        // 递归排序子节点
        for (PsyCategory node : nodes) {
            sortTreeNodes(node.getChildren());
        }
    }

    @Override
    public List<PsyCategory> selectCategoryListWithProducts() {
        List<PsyCategory> categories = categoryMapper.selectCategoryListWithProducts();
        
        // 1. 建立分类索引,合并产品数据
        Map<Long, PsyCategory> categoryMap = new HashMap<>();
        for (PsyCategory category : categories) {
            PsyCategory existingCategory = categoryMap.get(category.getCategoryId());
            if (existingCategory == null) {
                categoryMap.put(category.getCategoryId(), category);
            } else {
                // 合并产品数据
                if (category.getProducts() != null && !category.getProducts().isEmpty()) {
                    // 检查产品是否已存在
                    for (PsyProduct product : category.getProducts()) {
                        if (product.getProductId() != null) {
                            boolean exists = existingCategory.getProducts().stream()
                                    .anyMatch(p -> p.getProductId().equals(product.getProductId()));
                            if (!exists) {
                                existingCategory.getProducts().add(product);
                            }
                        }
                    }
                }
            }
        }

        // 2. 构建树形结构
        List<PsyCategory> rootCategories = new ArrayList<>();
        for (PsyCategory category : categoryMap.values()) {
            if (category.getParentId() == 0L) {
                rootCategories.add(category);
            } else {
                PsyCategory parent = categoryMap.get(category.getParentId());
                if (parent != null) {
                    parent.getChildren().add(category);
                }
            }
        }

        // 3. 递归排序
        sortTreeNodes(rootCategories);
        return rootCategories;
    }

    /**
     * 查询分类树及其对应的所有产品
     * 团单包含产品，咨询师包含咨询师，课程包含课程，测评包含测评，冥想包含冥想
     */
    @Override
    public Map<String, Object> selectCategoryTreeWithAllProducts() {
        Map<String, Object> result = new HashMap<>();
        List<Map<String, Object>> categoryList = new ArrayList<>();

        // 查询所有顶级分类
        List<PsyCategory> topCategories = getTopLevelCategories();

        for (PsyCategory category : topCategories) {
            Map<String, Object> categoryData = new HashMap<>();
            categoryData.put("categoryId", category.getCategoryId());
            categoryData.put("categoryName", category.getCategoryName());
            categoryData.put("parentId", category.getParentId());
            categoryData.put("orderNum", category.getOrderNum());
            categoryData.put("status", category.getStatus());

            // 根据分类ID获取对应的产品
            Object products = getProductsByCategoryId(category.getCategoryId());
            categoryData.put("products", products);

            // 获取子分类及其产品
            List<Map<String, Object>> children = getChildCategoriesWithProducts(category.getCategoryId());
            categoryData.put("children", children);

            categoryList.add(categoryData);
        }

        result.put("categories", categoryList);
        result.put("totalCount", categoryList.size());

        return result;
    }

    /**
     * 获取顶级分类列表
     */
    private List<PsyCategory> getTopLevelCategories() {
        PsyCategory condition = new PsyCategory();
        condition.setParentId(0L);
        condition.setStatus("0"); // 只查询启用的分类

        List<PsyCategory> allCategories = categoryMapper.selectCategoryList(condition);
        return allCategories.stream()
            .filter(cat -> cat.getParentId() == 0L)
            .sorted(Comparator.comparing(PsyCategory::getOrderNum, Comparator.nullsLast(Comparator.naturalOrder())))
            .collect(Collectors.toList());
    }

    /**
     * 获取子分类及其产品
     */
    private List<Map<String, Object>> getChildCategoriesWithProducts(Long parentId) {
        List<Map<String, Object>> children = new ArrayList<>();

        List<PsyCategory> childCategories = categoryMapper.selectChildrenByParentId(parentId);

        for (PsyCategory child : childCategories) {
            if ("0".equals(child.getStatus())) { // 只处理启用的分类
                Map<String, Object> childData = new HashMap<>();
                childData.put("categoryId", child.getCategoryId());
                childData.put("categoryName", child.getCategoryName());
                childData.put("parentId", child.getParentId());
                childData.put("orderNum", child.getOrderNum());
                childData.put("status", child.getStatus());

                // 获取该子分类的产品
                Object products = getProductsByCategoryId(child.getCategoryId());
                childData.put("products", products);

                children.add(childData);
            }
        }

        return children.stream()
            .sorted((a, b) -> {
                Integer orderA = (Integer) a.get("orderNum");
                Integer orderB = (Integer) b.get("orderNum");
                return Integer.compare(orderA != null ? orderA : 999, orderB != null ? orderB : 999);
            })
            .collect(Collectors.toList());
    }

    /**
     * 根据分类ID获取对应的产品
     */
    private Object getProductsByCategoryId(Long categoryId) {
        // 根据分类ID判断产品类型
        switch (categoryId.intValue()) {
            case 11: // 团单 - 返回产品
                return getProductsByCategory(categoryId);

            case 12: // 咨询师 - 返回咨询师列表
                return getCounselorsByCategory(categoryId);

            case 13: // 课程 - 返回课程列表
                return getCoursesByCategory(categoryId);

            case 14: // 冥想 - 返回冥想列表
                return getMeditationsByCategory(categoryId);

            case 16: // 测评 - 返回测评列表
                return null;

            case 17: // 儿童类测评
            case 18: // 健康类测评
            case 19: // 精神科测评
            case 20: // 人格类测评
            case 21: // 职业类测评
            case 22: // 学生类测评
                return null;

            default:
                return new ArrayList<>();
        }
    }

    /**
     * 获取产品列表
     */
    private List<Map<String, Object>> getProductsByCategory(Long categoryId) {
        // 这里需要调用产品服务，暂时返回空列表
        // TODO: 实现产品查询逻辑
        return new ArrayList<>();
    }

    /**
     * 获取咨询师列表
     */
    private List<Map<String, Object>> getCounselorsByCategory(Long categoryId) {
        // 这里需要调用咨询师服务，暂时返回空列表
        // TODO: 实现咨询师查询逻辑
        return new ArrayList<>();
    }

    /**
     * 获取课程列表
     */
    private List<Map<String, Object>> getCoursesByCategory(Long categoryId) {
        // 这里需要调用课程服务，暂时返回空列表
        // TODO: 实现课程查询逻辑
        return new ArrayList<>();
    }

    /**
     * 获取冥想列表
     */
    private List<Map<String, Object>> getMeditationsByCategory(Long categoryId) {
        // 这里需要调用冥想服务，暂时返回空列表
        // TODO: 实现冥想查询逻辑
        return new ArrayList<>();
    }

}
