package com.xihuan.common.core.domain.entity;

import lombok.Data;

import java.util.List;

/**
 * 问题选项配置实体类
 */
@Data
public class PsyMatchQuestionOption {
    
    /** 主键ID */
    private Long id;
    
    /** 关联问题ID */
    private Long questionId;
    
    /** 选项文本 */
    private String optionText;
    
    /** 选项值 */
    private String valueCode;
    
    /** 关联标签类型 */
    private String tagType;
    
    /** 推荐标记 */
    private String recommendTag;
    
    /** 警告文本 */
    private String warningText;

    /** 父级ID */
    private Long parentId;
    
    /** 排序 */
    private Integer sort;
    
    /** 关联的咨询师ID列表 */
    private List<Long> consultantIds;
} 