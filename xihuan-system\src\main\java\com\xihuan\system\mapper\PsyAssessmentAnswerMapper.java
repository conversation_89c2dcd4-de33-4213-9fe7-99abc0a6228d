package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 心理测评答案Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentAnswerMapper {
    
    /**
     * 查询答案列表
     * 
     * @param answer 答案信息
     * @return 答案集合
     */
    List<PsyAssessmentAnswer> selectAnswerList(PsyAssessmentAnswer answer);

    /**
     * 根据ID查询答案
     * 
     * @param id 答案ID
     * @return 答案信息
     */
    PsyAssessmentAnswer selectAnswerById(Long id);

    /**
     * 查询答案详情（包含题目、选项等信息）
     * 
     * @param id 答案ID
     * @return 答案详情
     */
    PsyAssessmentAnswer selectAnswerWithDetails(Long id);

    /**
     * 根据测评记录ID查询答案列表
     * 
     * @param recordId 测评记录ID
     * @return 答案集合
     */
    List<PsyAssessmentAnswer> selectAnswersByRecordId(Long recordId);

    /**
     * 根据测评记录ID查询答案列表（包含题目、选项信息）
     * 
     * @param recordId 测评记录ID
     * @return 答案集合
     */
    List<PsyAssessmentAnswer> selectAnswersWithDetailsByRecordId(Long recordId);

    /**
     * 根据题目ID查询答案列表
     * 
     * @param questionId 题目ID
     * @return 答案集合
     */
    List<PsyAssessmentAnswer> selectAnswersByQuestionId(Long questionId);

    /**
     * 新增答案
     * 
     * @param answer 答案信息
     * @return 结果
     */
    int insertAnswer(PsyAssessmentAnswer answer);

    /**
     * 修改答案
     * 
     * @param answer 答案信息
     * @return 结果
     */
    int updateAnswer(PsyAssessmentAnswer answer);

    /**
     * 删除答案
     * 
     * @param id 答案ID
     * @return 结果
     */
    int deleteAnswerById(Long id);

    /**
     * 批量删除答案
     * 
     * @param ids 需要删除的答案ID
     * @return 结果
     */
    int deleteAnswerByIds(Long[] ids);

    /**
     * 根据测评记录ID删除答案
     * 
     * @param recordId 测评记录ID
     * @return 结果
     */
    int deleteAnswersByRecordId(Long recordId);

    /**
     * 查询用户在指定题目的答案
     * 
     * @param recordId 测评记录ID
     * @param questionId 题目ID
     * @return 答案信息
     */
    PsyAssessmentAnswer selectAnswerByRecordAndQuestion(@Param("recordId") Long recordId, @Param("questionId") Long questionId);

    /**
     * 批量插入答案
     * 
     * @param answers 答案列表
     * @return 结果
     */
    int batchInsertAnswers(List<PsyAssessmentAnswer> answers);

    /**
     * 统计测评记录的答案数量
     * 
     * @param recordId 测评记录ID
     * @return 答案数量
     */
    int countAnswersByRecordId(Long recordId);

    /**
     * 计算测评记录的总分
     * 
     * @param recordId 测评记录ID
     * @return 总分
     */
    java.math.BigDecimal calculateTotalScore(Long recordId);

    /**
     * 计算维度得分
     * 
     * @param recordId 测评记录ID
     * @param dimension 维度名称
     * @return 维度得分
     */
    java.math.BigDecimal calculateDimensionScore(@Param("recordId") Long recordId, @Param("dimension") String dimension);

    /**
     * 查询答案统计信息
     * 
     * @param recordId 测评记录ID
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectAnswerStats(Long recordId);

    /**
     * 查询题目答案分布统计
     * 
     * @param questionId 题目ID
     * @return 统计信息
     */
    List<java.util.Map<String, Object>> selectQuestionAnswerStats(Long questionId);
}
