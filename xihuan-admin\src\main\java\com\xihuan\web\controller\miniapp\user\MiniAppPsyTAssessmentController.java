package com.xihuan.web.controller.miniapp.user;

import com.xihuan.common.annotation.Log;
import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTScale;
import com.xihuan.common.core.domain.entity.PsyTAssessmentRecord;
import com.xihuan.common.core.domain.entity.PsyTAnswerRecord;
import com.xihuan.common.core.page.TableDataInfo;
import com.xihuan.common.enums.BusinessType;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.system.service.IPsyTScaleService;
import com.xihuan.system.service.IPsyTAssessmentRecordService;
import com.xihuan.system.service.IPsyTAssessmentService;
import com.xihuan.system.service.IPsyTAnswerRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 小程序用户端新心理测评Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/psy-assessment")
public class MiniAppPsyTAssessmentController extends BaseController {
    
    @Autowired
    private IPsyTScaleService scaleService;
    
    @Autowired
    private IPsyTAssessmentRecordService recordService;
    
    @Autowired
    private IPsyTAssessmentService assessmentService;
    
    @Autowired
    private IPsyTAnswerRecordService answerService;

    /**
     * 查询量表列表
     */
    @GetMapping("/scale/list")
    public TableDataInfo listScales(PsyTScale scale) {
        startPage();
        // 只查询已发布的量表
        scale.setStatus(PsyTScale.STATUS_ENABLED);
        List<PsyTScale> list = scaleService.selectScaleList(scale);
        return getDataTable(list);
    }

    /**
     * 获取量表详情
     */
    @GetMapping("/scale/{id}")
    public AjaxResult getScaleDetail(@PathVariable("id") Long id) {
        // 更新查看次数
        scaleService.updateViewCount(id);
        
        PsyTScale scale = scaleService.selectScaleWithDetails(id);
        if (scale == null || !PsyTScale.STATUS_ENABLED.equals(scale.getStatus())) {
            return error("量表不存在或已下架");
        }
        
        return success(scale);
    }

    /**
     * 搜索量表
     */
    @GetMapping("/scale/search")
    public TableDataInfo searchScales(
            @RequestParam(value = "keyword", required = false) String keyword,
            @RequestParam(value = "scoringType", required = false) String scoringType,
            @RequestParam(value = "payMode", required = false) Integer payMode) {
        startPage();
        List<PsyTScale> list = scaleService.searchScales(keyword, scoringType, payMode, null);
        
        // 如果有关键词，更新搜索次数
        if (StringUtils.isNotEmpty(keyword)) {
            for (PsyTScale scale : list) {
                scaleService.updateSearchCount(scale.getId());
            }
        }
        
        return getDataTable(list);
    }

    /**
     * 查询热门量表
     */
    @GetMapping("/scale/hot")
    public AjaxResult getHotScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectHotScales(limit);
        return success(list);
    }

    /**
     * 查询免费量表
     */
    @GetMapping("/scale/free")
    public AjaxResult getFreeScales() {
        List<PsyTScale> list = scaleService.selectFreeScales();
        return success(list);
    }

    /**
     * 查询推荐量表
     */
    @GetMapping("/scale/recommend")
    public AjaxResult getRecommendScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectRecommendScales(getUserId(), limit);
        return success(list);
    }

    /**
     * 查询最新量表
     */
    @GetMapping("/scale/latest")
    public AjaxResult getLatestScales(@RequestParam(value = "limit", defaultValue = "10") Integer limit) {
        List<PsyTScale> list = scaleService.selectLatestScales(limit);
        return success(list);
    }

    /**
     * 查询用户收藏的量表
     */
    @GetMapping("/scale/favorite")
    public TableDataInfo getFavoriteScales() {
        startPage();
        List<PsyTScale> list = scaleService.selectFavoriteScalesByUserId(getUserId());
        return getDataTable(list);
    }

    /**
     * 开始测评
     */
    @Log(title = "小程序测评", businessType = BusinessType.INSERT)
    @PostMapping("/start")
    public AjaxResult startAssessment(@RequestParam Long scaleId) {
        Map<String, Object> result = assessmentService.startAssessment(getUserId(), scaleId, "personal");
        return success(result);
    }

    /**
     * 获取测评题目
     */
    @GetMapping("/{sessionId}/question/{questionNo}")
    public AjaxResult getQuestion(
            @PathVariable("sessionId") String sessionId,
            @PathVariable("questionNo") Integer questionNo) {
        Map<String, Object> question = assessmentService.getAssessmentQuestion(sessionId, questionNo);
        return success(question);
    }

    /**
     * 提交答案
     */
    @Log(title = "小程序测评", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/answer")
    public AjaxResult submitAnswer(
            @PathVariable("sessionId") String sessionId,
            @RequestParam Long questionId,
            @RequestParam String answerContent,
            @RequestParam(required = false) Integer responseTime) {
        Map<String, Object> result = assessmentService.submitAnswer(sessionId, questionId, answerContent, responseTime);
        return success(result);
    }

    /**
     * 批量提交答案
     */
    @Log(title = "小程序测评", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/batch-answer")
    public AjaxResult batchSubmitAnswers(
            @PathVariable("sessionId") String sessionId,
            @RequestBody List<PsyTAnswerRecord> answers) {
        Map<String, Object> result = assessmentService.batchSubmitAnswers(sessionId, answers);
        return success(result);
    }

    /**
     * 获取下一题
     */
    @GetMapping("/{sessionId}/next")
    public AjaxResult getNextQuestion(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> question = assessmentService.getNextQuestion(sessionId);
        return success(question);
    }

    /**
     * 获取上一题
     */
    @GetMapping("/{sessionId}/previous")
    public AjaxResult getPreviousQuestion(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> question = assessmentService.getPreviousQuestion(sessionId);
        return success(question);
    }

    /**
     * 跳转到指定题目
     */
    @GetMapping("/{sessionId}/jump/{questionNo}")
    public AjaxResult jumpToQuestion(
            @PathVariable("sessionId") String sessionId,
            @PathVariable("questionNo") Integer questionNo) {
        Map<String, Object> question = assessmentService.jumpToQuestion(sessionId, questionNo);
        return success(question);
    }

    /**
     * 保存测评进度
     */
    @Log(title = "小程序测评", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/save")
    public AjaxResult saveProgress(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.saveProgress(sessionId);
        return success(result);
    }

    /**
     * 恢复测评
     */
    @PostMapping("/{sessionId}/resume")
    public AjaxResult resumeAssessment(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.resumeAssessment(sessionId);
        return success(result);
    }

    /**
     * 暂停测评
     */
    @Log(title = "小程序测评", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/pause")
    public AjaxResult pauseAssessment(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.pauseAssessment(sessionId);
        return success(result);
    }

    /**
     * 完成测评
     */
    @Log(title = "小程序测评", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/complete")
    public AjaxResult completeAssessment(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.completeAssessment(sessionId);
        return success(result);
    }

    /**
     * 放弃测评
     */
    @Log(title = "小程序测评", businessType = BusinessType.UPDATE)
    @PostMapping("/{sessionId}/abandon")
    public AjaxResult abandonAssessment(
            @PathVariable("sessionId") String sessionId,
            @RequestParam(required = false) String reason) {
        Map<String, Object> result = assessmentService.abandonAssessment(sessionId, reason);
        return success(result);
    }

    /**
     * 获取测评进度
     */
    @GetMapping("/{sessionId}/progress")
    public AjaxResult getProgress(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> progress = assessmentService.getAssessmentProgress(sessionId);
        return success(progress);
    }

    /**
     * 获取测评状态
     */
    @GetMapping("/{sessionId}/status")
    public AjaxResult getStatus(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> status = assessmentService.getAssessmentStatus(sessionId);
        return success(status);
    }

    /**
     * 生成测评报告
     */
    @PostMapping("/record/{recordId}/report")
    public AjaxResult generateReport(
            @PathVariable("recordId") Long recordId,
            @RequestParam(defaultValue = "1") Integer reportLevel) {
        Map<String, Object> report = assessmentService.generateReport(recordId, reportLevel);
        return success(report);
    }

    /**
     * 获取测评建议
     */
    @GetMapping("/record/{recordId}/suggestions")
    public AjaxResult getSuggestions(@PathVariable("recordId") Long recordId) {
        Map<String, Object> suggestions = assessmentService.getAssessmentSuggestions(recordId);
        return success(suggestions);
    }

    /**
     * 查询用户测评记录
     */
    @GetMapping("/record/list")
    public TableDataInfo getRecords() {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectRecordsByUserId(getUserId());
        return getDataTable(list);
    }

    /**
     * 查询用户在指定量表的测评记录
     */
    @GetMapping("/record/scale/{scaleId}")
    public TableDataInfo getScaleRecords(@PathVariable("scaleId") Long scaleId) {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectUserRecordsByScaleId(getUserId(), scaleId);
        return getDataTable(list);
    }

    /**
     * 查询进行中的测评记录
     */
    @GetMapping("/record/in-progress")
    public AjaxResult getInProgressRecords() {
        List<PsyTAssessmentRecord> list = recordService.selectInProgressRecords(getUserId());
        return success(list);
    }

    /**
     * 查询已完成的测评记录
     */
    @GetMapping("/record/completed")
    public TableDataInfo getCompletedRecords() {
        startPage();
        List<PsyTAssessmentRecord> list = recordService.selectCompletedRecords(getUserId());
        return getDataTable(list);
    }

    /**
     * 获取测评记录详情
     */
    @GetMapping("/record/{recordId}")
    public AjaxResult getRecordDetail(@PathVariable("recordId") Long recordId) {
        PsyTAssessmentRecord record = recordService.selectRecordWithDetails(recordId);
        if (record == null || !getUserId().equals(record.getUserId())) {
            return error("测评记录不存在或无权限访问");
        }
        return success(record);
    }

    /**
     * 获取测评历史
     */
    @GetMapping("/history")
    public AjaxResult getHistory(@RequestParam Long scaleId) {
        List<Map<String, Object>> history = assessmentService.getAssessmentHistory(getUserId(), scaleId);
        return success(history);
    }

    /**
     * 比较测评结果
     */
    @PostMapping("/compare")
    public AjaxResult compareResults(@RequestBody List<Long> recordIds) {
        // 验证所有记录都属于当前用户
        for (Long recordId : recordIds) {
            PsyTAssessmentRecord record = recordService.selectRecordById(recordId);
            if (record == null || !getUserId().equals(record.getUserId())) {
                return error("测评记录不存在或无权限访问");
            }
        }
        
        Map<String, Object> comparison = assessmentService.compareAssessmentResults(recordIds);
        return success(comparison);
    }

    /**
     * 分享测评结果
     */
    @PostMapping("/record/{recordId}/share")
    public AjaxResult shareResult(
            @PathVariable("recordId") Long recordId,
            @RequestParam String shareType) {
        // 验证记录属于当前用户
        PsyTAssessmentRecord record = recordService.selectRecordById(recordId);
        if (record == null || !getUserId().equals(record.getUserId())) {
            return error("测评记录不存在或无权限访问");
        }
        
        Map<String, Object> shareInfo = assessmentService.shareAssessmentResult(recordId, shareType);
        return success(shareInfo);
    }

    /**
     * 获取用户测评概览
     */
    @GetMapping("/overview")
    public AjaxResult getUserOverview() {
        Map<String, Object> overview = assessmentService.getUserAssessmentOverview(getUserId());
        return success(overview);
    }

    /**
     * 预览测评
     */
    @GetMapping("/scale/{scaleId}/preview")
    public AjaxResult previewAssessment(@PathVariable("scaleId") Long scaleId) {
        Map<String, Object> preview = assessmentService.previewAssessment(scaleId);
        return success(preview);
    }

    /**
     * 标记题目
     */
    @PostMapping("/{sessionId}/mark")
    public AjaxResult markQuestion(
            @PathVariable("sessionId") String sessionId,
            @RequestParam Long questionId,
            @RequestParam Boolean marked) {
        Map<String, Object> result = assessmentService.markQuestion(sessionId, questionId, marked);
        return success(result);
    }

    /**
     * 获取已标记题目
     */
    @GetMapping("/{sessionId}/marked")
    public AjaxResult getMarkedQuestions(@PathVariable("sessionId") String sessionId) {
        List<Map<String, Object>> marked = assessmentService.getMarkedQuestions(sessionId);
        return success(marked);
    }

    /**
     * 检查测评完整性
     */
    @GetMapping("/{sessionId}/check-completeness")
    public AjaxResult checkCompleteness(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> result = assessmentService.checkAssessmentCompleteness(sessionId);
        return success(result);
    }

    /**
     * 获取测评摘要
     */
    @GetMapping("/{sessionId}/summary")
    public AjaxResult getSummary(@PathVariable("sessionId") String sessionId) {
        Map<String, Object> summary = assessmentService.getAssessmentSummary(sessionId);
        return success(summary);
    }

    /**
     * 查询用户测评统计
     */
    @GetMapping("/stats")
    public AjaxResult getUserStats() {
        Map<String, Object> stats = recordService.selectUserRecordStats(getUserId());
        return success(stats);
    }
}
