package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyAssessmentReview;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 心理测评评价Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface PsyAssessmentReviewMapper {
    
    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectReviewList(PsyAssessmentReview review);

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    PsyAssessmentReview selectReviewById(Long id);

    /**
     * 查询评价详情（包含用户、量表等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    PsyAssessmentReview selectReviewWithDetails(Long id);

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectReviewsByUserId(Long userId);

    /**
     * 根据量表ID查询评价列表
     * 
     * @param scaleId 量表ID
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectReviewsByScaleId(Long scaleId);

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int insertReview(PsyAssessmentReview review);

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    int updateReview(PsyAssessmentReview review);

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    int deleteReviewById(Long id);

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    int deleteReviewByIds(Long[] ids);

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param status 审核状态
     * @param auditBy 审核人
     * @param auditRemark 审核备注
     * @return 结果
     */
    int auditReview(@Param("id") Long id, 
                   @Param("status") Integer status, 
                   @Param("auditBy") String auditBy, 
                   @Param("auditRemark") String auditRemark);

    /**
     * 更新点赞数
     * 
     * @param id 评价ID
     * @param increment 增量（1为点赞，-1为取消点赞）
     * @return 结果
     */
    int updateLikeCount(@Param("id") Long id, @Param("increment") Integer increment);

    /**
     * 查询用户是否已点赞
     * 
     * @param reviewId 评价ID
     * @param userId 用户ID
     * @return 数量
     */
    int checkUserLiked(@Param("reviewId") Long reviewId, @Param("userId") Long userId);

    /**
     * 查询量表的评价统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectReviewStats(Long scaleId);

    /**
     * 查询用户的评价统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    java.util.Map<String, Object> selectUserReviewStats(Long userId);

    /**
     * 查询待审核评价数量
     * 
     * @return 数量
     */
    int countPendingReviews();

    /**
     * 查询最新评价列表
     * 
     * @param limit 限制数量
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectLatestReviews(@Param("limit") Integer limit);

    /**
     * 查询热门评价列表
     * 
     * @param limit 限制数量
     * @return 评价集合
     */
    List<PsyAssessmentReview> selectHotReviews(@Param("limit") Integer limit);

    /**
     * 统计评价数量
     * 
     * @param review 查询条件
     * @return 数量
     */
    int countReviews(PsyAssessmentReview review);
}
