package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyAssessmentOrder;
import com.xihuan.common.core.domain.entity.PsyAssessmentScale;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.StringUtils;
import com.xihuan.common.utils.bean.BeanUtils;
import com.xihuan.system.domain.dto.PsyAssessmentDTO;
import com.xihuan.system.mapper.PsyAssessmentOrderMapper;
import com.xihuan.system.service.IPsyAssessmentOrderService;
import com.xihuan.system.service.IPsyAssessmentScaleService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 心理测评订单Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyAssessmentOrderServiceImpl implements IPsyAssessmentOrderService {
    
    @Autowired
    private PsyAssessmentOrderMapper orderMapper;
    
    @Autowired
    private IPsyAssessmentScaleService scaleService;

    /**
     * 查询订单列表
     * 
     * @param order 订单信息
     * @return 订单集合
     */
    @Override
    public List<PsyAssessmentOrder> selectOrderList(PsyAssessmentOrder order) {
        return orderMapper.selectOrderList(order);
    }

    /**
     * 根据ID查询订单
     * 
     * @param id 订单ID
     * @return 订单信息
     */
    @Override
    public PsyAssessmentOrder selectOrderById(Long id) {
        return orderMapper.selectOrderById(id);
    }

    /**
     * 查询订单详情（包含用户、量表等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    @Override
    public PsyAssessmentOrder selectOrderWithDetails(Long id) {
        return orderMapper.selectOrderWithDetails(id);
    }

    /**
     * 根据订单编号查询订单
     * 
     * @param orderNo 订单编号
     * @return 订单信息
     */
    @Override
    public PsyAssessmentOrder selectOrderByOrderNo(String orderNo) {
        return orderMapper.selectOrderByOrderNo(orderNo);
    }

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<PsyAssessmentOrder> selectOrdersByUserId(Long userId) {
        return orderMapper.selectOrdersByUserId(userId);
    }

    /**
     * 新增订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertOrder(PsyAssessmentOrder order) {
        // 设置默认值
        if (order.getPaymentStatus() == null) {
            order.setPaymentStatus(PsyAssessmentOrder.PAYMENT_STATUS_PENDING);
        }
        if (order.getOrderStatus() == null) {
            order.setOrderStatus(PsyAssessmentOrder.ORDER_STATUS_PENDING);
        }
        if (order.getDelFlag() == null) {
            order.setDelFlag(PsyAssessmentOrder.DEL_FLAG_NORMAL);
        }
        
        // 设置过期时间（30分钟后过期）
        if (order.getExpireTime() == null) {
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.MINUTE, 30);
            order.setExpireTime(calendar.getTime());
        }
        
        order.setCreateTime(DateUtils.getNowDate());
        return orderMapper.insertOrder(order);
    }

    /**
     * 修改订单
     * 
     * @param order 订单信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateOrder(PsyAssessmentOrder order) {
        order.setUpdateTime(DateUtils.getNowDate());
        return orderMapper.updateOrder(order);
    }

    /**
     * 删除订单
     * 
     * @param ids 需要删除的订单ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteOrderByIds(Long[] ids) {
        return orderMapper.deleteOrderByIds(ids);
    }

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param couponId 优惠券ID
     * @return 订单信息
     */
    @Override
    @Transactional
    public PsyAssessmentOrder createOrder(Long userId, Long scaleId, Long couponId) {
        // 检查量表是否存在
        PsyAssessmentScale scale = scaleService.selectScaleById(scaleId);
        if (scale == null || !scale.isPublished()) {
            throw new ServiceException("量表不存在或未发布");
        }
        
        // 检查是否免费量表
        if (scale.isFreeScale()) {
            throw new ServiceException("免费量表无需购买");
        }
        
        // 检查是否已购买
        if (checkUserPurchased(userId, scaleId)) {
            throw new ServiceException("您已购买过该量表");
        }
        
        // 计算订单金额
        Map<String, BigDecimal> amountInfo = calculateOrderAmount(scaleId, couponId);
        
        // 生成订单
        PsyAssessmentOrder order = new PsyAssessmentOrder();
        order.setOrderNo(generateOrderNo());
        order.setUserId(userId);
        order.setScaleId(scaleId);
        order.setScaleName(scale.getScaleName());
        order.setOriginalPrice(amountInfo.get("originalPrice"));
        order.setActualPrice(amountInfo.get("actualPrice"));
        order.setDiscountAmount(amountInfo.get("discountAmount"));
        order.setCouponId(couponId);
        order.setPaymentStatus(PsyAssessmentOrder.PAYMENT_STATUS_PENDING);
        order.setOrderStatus(PsyAssessmentOrder.ORDER_STATUS_PENDING);
        
        insertOrder(order);
        
        return order;
    }

    /**
     * 支付订单
     * 
     * @param orderNo 订单编号
     * @param paymentMethod 支付方式
     * @return 结果
     */
    @Override
    @Transactional
    public int payOrder(String orderNo, String paymentMethod) {
        PsyAssessmentOrder order = selectOrderByOrderNo(orderNo);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        if (!order.isPending()) {
            throw new ServiceException("订单状态不正确");
        }
        
        // 检查是否过期
        if (order.getExpireTime() != null && order.getExpireTime().before(new Date())) {
            throw new ServiceException("订单已过期");
        }
        
        // 更新订单状态
        return orderMapper.updatePaymentStatus(orderNo, PsyAssessmentOrder.PAYMENT_STATUS_PAID, new Date());
    }

    /**
     * 完成订单
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    @Override
    @Transactional
    public int completeOrder(String orderNo) {
        return orderMapper.completeOrder(orderNo);
    }

    /**
     * 取消订单
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelOrder(String orderNo) {
        return orderMapper.cancelOrder(orderNo);
    }

    /**
     * 申请退款
     * 
     * @param orderNo 订单编号
     * @param refundReason 退款原因
     * @return 结果
     */
    @Override
    @Transactional
    public int applyRefund(String orderNo, String refundReason) {
        PsyAssessmentOrder order = selectOrderByOrderNo(orderNo);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        if (!order.isPaid()) {
            throw new ServiceException("订单未支付，无法退款");
        }
        
        return orderMapper.applyRefund(orderNo, order.getActualPrice(), refundReason);
    }

    /**
     * 处理退款
     * 
     * @param orderNo 订单编号
     * @return 结果
     */
    @Override
    @Transactional
    public int processRefund(String orderNo) {
        return orderMapper.processRefund(orderNo, new Date());
    }

    /**
     * 查询用户在指定量表的有效订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 订单信息
     */
    @Override
    public PsyAssessmentOrder selectValidOrderByUserAndScale(Long userId, Long scaleId) {
        return orderMapper.selectValidOrderByUserAndScale(userId, scaleId);
    }

    /**
     * 检查用户是否已购买量表
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 是否已购买
     */
    @Override
    public boolean checkUserPurchased(Long userId, Long scaleId) {
        PsyAssessmentOrder order = selectValidOrderByUserAndScale(userId, scaleId);
        return order != null && order.isPaid();
    }

    /**
     * 查询待支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<PsyAssessmentOrder> selectPendingOrders(Long userId) {
        return orderMapper.selectPendingOrders(userId);
    }

    /**
     * 查询已支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<PsyAssessmentOrder> selectPaidOrders(Long userId) {
        return orderMapper.selectPaidOrders(userId);
    }

    /**
     * 查询已完成订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<PsyAssessmentOrder> selectCompletedOrders(Long userId) {
        return orderMapper.selectCompletedOrders(userId);
    }

    /**
     * 查询已取消订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    @Override
    public List<PsyAssessmentOrder> selectCancelledOrders(Long userId) {
        return orderMapper.selectCancelledOrders(userId);
    }

    /**
     * 处理过期订单
     * 
     * @return 处理数量
     */
    @Override
    @Transactional
    public int handleExpiredOrders() {
        List<PsyAssessmentOrder> expiredOrders = orderMapper.selectExpiredOrders();
        int count = 0;
        
        for (PsyAssessmentOrder order : expiredOrders) {
            if (cancelOrder(order.getOrderNo()) > 0) {
                count++;
            }
        }
        
        return count;
    }

    /**
     * 查询订单统计信息
     * 
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectOrderStats() {
        return orderMapper.selectOrderStats();
    }

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectUserOrderStats(Long userId) {
        return orderMapper.selectUserOrderStats(userId);
    }

    /**
     * 查询量表订单统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    @Override
    public Map<String, Object> selectScaleOrderStats(Long scaleId) {
        return orderMapper.selectScaleOrderStats(scaleId);
    }

    /**
     * 查询订单收入统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectOrderIncomeStats(Integer days) {
        Calendar calendar = Calendar.getInstance();
        Date endDate = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, -days);
        Date startDate = calendar.getTime();
        
        return orderMapper.selectOrderIncomeStats(startDate, endDate);
    }

    /**
     * 查询热销量表
     * 
     * @param limit 限制数量
     * @return 量表集合
     */
    @Override
    public List<Map<String, Object>> selectHotSalesScales(Integer limit) {
        return orderMapper.selectHotSalesScales(limit);
    }

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    @Override
    public String generateOrderNo() {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = sdf.format(new Date());
        String random = String.valueOf((int) (Math.random() * 9000) + 1000);
        return "PSY" + timestamp + random;
    }

    /**
     * 计算订单金额
     * 
     * @param scaleId 量表ID
     * @param couponId 优惠券ID
     * @return 订单金额信息
     */
    @Override
    public Map<String, BigDecimal> calculateOrderAmount(Long scaleId, Long couponId) {
        Map<String, BigDecimal> result = new HashMap<>();
        
        // 获取量表价格
        PsyAssessmentScale scale = scaleService.selectScaleById(scaleId);
        BigDecimal originalPrice = scale.getPrice() != null ? scale.getPrice() : BigDecimal.ZERO;
        
        // 计算优惠金额
        BigDecimal discountAmount = BigDecimal.ZERO;
        if (couponId != null) {
            // TODO: 计算优惠券优惠金额
        }
        
        // 计算实付金额
        BigDecimal actualPrice = originalPrice.subtract(discountAmount);
        if (actualPrice.compareTo(BigDecimal.ZERO) < 0) {
            actualPrice = BigDecimal.ZERO;
        }
        
        result.put("originalPrice", originalPrice);
        result.put("discountAmount", discountAmount);
        result.put("actualPrice", actualPrice);
        
        return result;
    }

    /**
     * 获取订单DTO
     * 
     * @param id 订单ID
     * @return 订单DTO
     */
    @Override
    public PsyAssessmentDTO.OrderDTO getOrderDTO(Long id) {
        PsyAssessmentOrder order = selectOrderWithDetails(id);
        if (order == null) {
            return null;
        }
        
        PsyAssessmentDTO.OrderDTO dto = new PsyAssessmentDTO.OrderDTO();
        BeanUtils.copyBeanProp(dto, order);
        
        // 设置扩展字段
        dto.setPaymentStatusDesc(order.getPaymentStatusDesc());
        dto.setOrderStatusDesc(order.getOrderStatusDesc());
        dto.setCreateTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, order.getCreateTime()));
        if (order.getPayTime() != null) {
            dto.setPayTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, order.getPayTime()));
        }
        if (order.getExpireTime() != null) {
            dto.setExpireTime(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD_HH_MM_SS, order.getExpireTime()));
        }
        
        return dto;
    }

    /**
     * 获取订单列表DTO
     * 
     * @param order 查询条件
     * @return 订单DTO列表
     */
    @Override
    public List<PsyAssessmentDTO.OrderDTO> getOrderListDTO(PsyAssessmentOrder order) {
        List<PsyAssessmentOrder> orderList = selectOrderList(order);
        List<PsyAssessmentDTO.OrderDTO> dtoList = new ArrayList<>();
        
        for (PsyAssessmentOrder item : orderList) {
            PsyAssessmentDTO.OrderDTO dto = getOrderDTO(item.getId());
            if (dto != null) {
                dtoList.add(dto);
            }
        }
        
        return dtoList;
    }

    /**
     * 统计订单数量
     * 
     * @param order 查询条件
     * @return 数量
     */
    @Override
    public int countOrders(PsyAssessmentOrder order) {
        return orderMapper.countOrders(order);
    }

    /**
     * 查询订单趋势统计
     * 
     * @param days 天数
     * @return 统计信息
     */
    @Override
    public List<Map<String, Object>> selectOrderTrend(Integer days) {
        return orderMapper.selectOrderTrend(days);
    }
}
