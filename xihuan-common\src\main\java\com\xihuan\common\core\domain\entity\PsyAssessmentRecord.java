package com.xihuan.common.core.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.xihuan.common.annotation.Excel;
import com.xihuan.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 心理测评记录表对象 psy_t_record
 * 
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class PsyAssessmentRecord extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    @Excel(name = "记录ID", cellType = Excel.ColumnType.NUMERIC)
    private Long id;

    /** 用户ID */
    @Excel(name = "用户ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    /** 量表ID */
    @Excel(name = "量表ID", cellType = Excel.ColumnType.NUMERIC)
    @NotNull(message = "量表ID不能为空")
    private Long scaleId;

    /** 测评会话ID */
    @Excel(name = "测评会话ID")
    @NotBlank(message = "测评会话ID不能为空")
    @Size(max = 64, message = "测评会话ID不能超过64个字符")
    private String sessionId;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 测评时长(秒) */
    @Excel(name = "测评时长")
    @Min(value = 0, message = "测评时长不能为负数")
    private Integer duration;

    /** 总分 */
    @Excel(name = "总分")
    @DecimalMin(value = "0.00", message = "总分不能为负数")
    private BigDecimal totalScore;

    /** 满分 */
    @Excel(name = "满分")
    @DecimalMin(value = "0.00", message = "满分不能为负数")
    private BigDecimal maxScore;

    /** 得分率(%) */
    @Excel(name = "得分率")
    @DecimalMin(value = "0.00", message = "得分率不能为负数")
    @DecimalMax(value = "100.00", message = "得分率不能超过100")
    private BigDecimal percentage;

    /** 状态(0=进行中 1=已完成 2=已中断) */
    @Excel(name = "状态", readConverterExp = "0=进行中,1=已完成,2=已中断")
    private Integer status;

    /** 结果等级 */
    @Excel(name = "结果等级")
    @Size(max = 20, message = "结果等级不能超过20个字符")
    private String resultLevel;

    /** 结果描述 */
    @Excel(name = "结果描述")
    private String resultDescription;

    /** 建议 */
    @Excel(name = "建议")
    private String suggestions;

    /** 维度得分(JSON格式) */
    @Excel(name = "维度得分")
    private String dimensionScores;

    /** 是否匿名(0=否 1=是) */
    @Excel(name = "是否匿名", readConverterExp = "0=否,1=是")
    private Integer isAnonymous;

    /** IP地址 */
    @Excel(name = "IP地址")
    @Size(max = 50, message = "IP地址不能超过50个字符")
    private String ipAddress;

    /** 用户代理 */
    @Excel(name = "用户代理")
    @Size(max = 500, message = "用户代理不能超过500个字符")
    private String userAgent;

    /** 删除标志(0=正常 1=删除) */
    @Excel(name = "删除标志", readConverterExp = "0=正常,1=删除")
    private Integer delFlag;

    // 关联对象
    /** 用户信息 */
    private SysUser user;

    /** 量表信息 */
    private PsyAssessmentScale scale;

    /** 答案列表 */
    private List<PsyAssessmentAnswer> answers;

    /** 评价信息 */
    private PsyAssessmentReview review;

    /** 订单信息 */
    private PsyAssessmentOrder order;

    // 扩展字段
    /** 用户昵称 */
    private String nickName;

    /** 量表名称 */
    private String scaleName;

    /** 完成进度 */
    private BigDecimal progress;

    /** 当前题目序号 */
    private Integer currentQuestionNo;

    /** 剩余时间(秒) */
    private Integer remainingTime;

    // 常量定义
    /** 状态：进行中 */
    public static final Integer STATUS_IN_PROGRESS = 0;
    
    /** 状态：已完成 */
    public static final Integer STATUS_COMPLETED = 1;
    
    /** 状态：已中断 */
    public static final Integer STATUS_INTERRUPTED = 2;

    /** 匿名：否 */
    public static final Integer ANONYMOUS_NO = 0;
    
    /** 匿名：是 */
    public static final Integer ANONYMOUS_YES = 1;

    /** 删除标志：正常 */
    public static final Integer DEL_FLAG_NORMAL = 0;
    
    /** 删除标志：删除 */
    public static final Integer DEL_FLAG_DELETED = 1;

    /**
     * 获取状态描述
     */
    public String getStatusDesc() {
        if (status == null) return "";
        switch (status) {
            case 0: return "进行中";
            case 1: return "已完成";
            case 2: return "已中断";
            default: return "未知";
        }
    }

    /**
     * 获取匿名描述
     */
    public String getAnonymousDesc() {
        if (isAnonymous == null) return "";
        return isAnonymous == 1 ? "是" : "否";
    }

    /**
     * 是否进行中
     */
    public boolean isInProgress() {
        return STATUS_IN_PROGRESS.equals(status);
    }

    /**
     * 是否已完成
     */
    public boolean isCompleted() {
        return STATUS_COMPLETED.equals(status);
    }

    /**
     * 是否已中断
     */
    public boolean isInterrupted() {
        return STATUS_INTERRUPTED.equals(status);
    }

    /**
     * 是否匿名
     */
    public boolean isAnonymousRecord() {
        return ANONYMOUS_YES.equals(isAnonymous);
    }

    /**
     * 是否已删除
     */
    public boolean isDeleted() {
        return DEL_FLAG_DELETED.equals(delFlag);
    }

    /**
     * 获取格式化的测评时长
     */
    public String getFormattedDuration() {
        if (duration == null || duration == 0) {
            return "0分钟";
        }
        
        int minutes = duration / 60;
        int seconds = duration % 60;
        
        if (minutes > 0) {
            return minutes + "分钟" + (seconds > 0 ? seconds + "秒" : "");
        } else {
            return seconds + "秒";
        }
    }

    /**
     * 获取格式化的得分率
     */
    public String getFormattedPercentage() {
        if (percentage == null) {
            return "0%";
        }
        return percentage.setScale(1, BigDecimal.ROUND_HALF_UP) + "%";
    }
}
