package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyConsultantReview;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyConsultantReviewMapper;
import com.xihuan.system.service.IPsyConsultantReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 咨询师评价表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyConsultantReviewServiceImpl implements IPsyConsultantReviewService {
    
    @Autowired
    private PsyConsultantReviewMapper reviewMapper;

    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    @Override
    public List<PsyConsultantReview> selectReviewList(PsyConsultantReview review) {
        return reviewMapper.selectReviewList(review);
    }

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    @Override
    public PsyConsultantReview selectReviewById(Long id) {
        return reviewMapper.selectReviewById(id);
    }

    /**
     * 查询评价详情（包含用户、咨询师等信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    @Override
    public PsyConsultantReview selectReviewWithDetails(Long id) {
        return reviewMapper.selectReviewWithDetails(id);
    }

    /**
     * 根据咨询师ID查询评价列表
     * 
     * @param consultantId 咨询师ID
     * @return 评价集合
     */
    @Override
    public List<PsyConsultantReview> selectReviewsByConsultantId(Long consultantId) {
        return reviewMapper.selectReviewsByConsultantId(consultantId);
    }

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    @Override
    public List<PsyConsultantReview> selectReviewsByUserId(Long userId) {
        return reviewMapper.selectReviewsByUserId(userId);
    }

    /**
     * 根据咨询记录ID查询评价
     * 
     * @param recordId 咨询记录ID
     * @return 评价信息
     */
    @Override
    public PsyConsultantReview selectReviewByRecordId(Long recordId) {
        return reviewMapper.selectReviewByRecordId(recordId);
    }

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    public int insertReview(PsyConsultantReview review) {
        review.setReviewTime(DateUtils.getNowDate());
        review.setDelFlag("0");
        review.setAdminCheck(0); // 默认待审核
        
        // 如果没有设置评分，默认为5分
        if (review.getRating() == null) {
            review.setRating(new BigDecimal("5.0"));
        }
        
        return reviewMapper.insertReview(review);
    }

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    public int updateReview(PsyConsultantReview review) {
        review.setUpdateTime(DateUtils.getNowDate());
        return reviewMapper.updateReview(review);
    }

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewById(Long id) {
        return reviewMapper.deleteReviewById(id);
    }

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewByIds(Long[] ids) {
        return reviewMapper.deleteReviewByIds(ids);
    }

    /**
     * 计算咨询师平均评分
     * 
     * @param consultantId 咨询师ID
     * @return 平均评分
     */
    @Override
    public BigDecimal calculateAverageRating(Long consultantId) {
        return reviewMapper.calculateAverageRating(consultantId);
    }

    /**
     * 统计咨询师评价数量
     * 
     * @param consultantId 咨询师ID
     * @return 评价数量
     */
    @Override
    public int countReviewsByConsultantId(Long consultantId) {
        return reviewMapper.countReviewsByConsultantId(consultantId);
    }

    /**
     * 检查用户是否已评价咨询记录
     * 
     * @param userId 用户ID
     * @param recordId 咨询记录ID
     * @return 是否已评价
     */
    @Override
    public boolean checkUserReviewed(Long userId, Long recordId) {
        int count = reviewMapper.checkUserReviewed(userId, recordId);
        return count > 0;
    }

    /**
     * 审核评价
     * 
     * @param id 评价ID
     * @param adminCheck 审核状态
     * @param updateBy 更新者
     * @return 结果
     */
    @Override
    public int auditReview(Long id, Integer adminCheck, String updateBy) {
        return reviewMapper.auditReview(id, adminCheck, updateBy);
    }

    /**
     * 咨询师回复评价
     * 
     * @param id 评价ID
     * @param consultantReply 回复内容
     * @param updateBy 更新者
     * @return 结果
     */
    @Override
    public int replyReview(Long id, String consultantReply, String updateBy) {
        return reviewMapper.replyReview(id, consultantReply, updateBy);
    }

    /**
     * 查询待审核评价列表
     * 
     * @return 评价集合
     */
    @Override
    public List<PsyConsultantReview> selectPendingReviews() {
        return reviewMapper.selectPendingReviews();
    }

    /**
     * 查询已通过审核的评价列表
     * 
     * @param consultantId 咨询师ID
     * @return 评价集合
     */
    @Override
    public List<PsyConsultantReview> selectApprovedReviews(Long consultantId) {
        return reviewMapper.selectApprovedReviews(consultantId);
    }

    /**
     * 统计各星级评价数量
     * 
     * @param consultantId 咨询师ID
     * @return 统计结果
     */
    @Override
    public Map<String, Object> getRatingDistribution(Long consultantId) {
        List<Map<String, Object>> distribution = reviewMapper.countRatingDistribution(consultantId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("distribution", distribution);
        
        // 计算总评价数
        int totalCount = countReviewsByConsultantId(consultantId);
        result.put("totalCount", totalCount);
        
        // 计算平均评分
        BigDecimal avgRating = calculateAverageRating(consultantId);
        result.put("avgRating", avgRating);
        
        return result;
    }

    /**
     * 更新咨询师评分信息
     * 
     * @param consultantId 咨询师ID
     * @return 结果
     */
    @Override
    public int updateConsultantRating(Long consultantId) {
        // 这里需要更新咨询师表中的评分信息
        // 由于咨询师表的具体结构未知，这里返回1表示成功
        // 实际实现时需要调用咨询师Service来更新评分
        return 1;
    }
}
