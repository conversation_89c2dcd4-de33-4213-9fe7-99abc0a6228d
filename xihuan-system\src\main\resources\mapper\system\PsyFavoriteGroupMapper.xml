<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyFavoriteGroupMapper">
    
    <resultMap type="PsyFavoriteGroup" id="FavoriteGroupResult">
        <id     property="groupId"           column="group_id"          />
        <result property="userId"            column="user_id"           />
        <result property="groupName"         column="group_name"        />
        <result property="groupIcon"         column="group_icon"        />
        <result property="groupColor"        column="group_color"       />
        <result property="description"       column="description"       />
        <result property="sort"              column="sort"              />
        <result property="isDefault"         column="is_default"        />
        <result property="isPublic"          column="is_public"         />
        <result property="favoriteCount"     column="favorite_count"    />
        <result property="status"            column="status"            />
        <result property="delFlag"           column="del_flag"          />
        <result property="createTime"        column="create_time"       />
        <result property="updateTime"        column="update_time"       />
    </resultMap>
    
    <resultMap type="PsyFavoriteGroupRel" id="FavoriteGroupRelResult">
        <id     property="favoriteId"        column="favorite_id"       />
        <id     property="groupId"           column="group_id"          />
        <result property="sort"              column="sort"              />
        <result property="createTime"        column="create_time"       />
    </resultMap>
    
    <sql id="selectFavoriteGroupVo">
        SELECT group_id, user_id, group_name, group_icon, group_color, description, sort,
               is_default, is_public, favorite_count, status, del_flag, create_time, update_time
        FROM psy_favorite_group
    </sql>
    
    <select id="selectGroupList" parameterType="PsyFavoriteGroup" resultMap="FavoriteGroupResult">
        <include refid="selectFavoriteGroupVo"/>
        <where>
            del_flag = '0'
            <if test="userId != null">
                AND user_id = #{userId}
            </if>
            <if test="isPublic != null">
                AND is_public = #{isPublic}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY sort ASC, create_time DESC
    </select>
    
    <select id="selectGroupById" parameterType="Long" resultMap="FavoriteGroupResult">
        <include refid="selectFavoriteGroupVo"/>
        WHERE group_id = #{groupId} AND del_flag = '0'
    </select>
    
    <insert id="insertGroup" parameterType="PsyFavoriteGroup" useGeneratedKeys="true" keyProperty="groupId">
        INSERT INTO psy_favorite_group (
            user_id, group_name, 
            <if test="groupIcon != null and groupIcon != ''">group_icon,</if>
            <if test="groupColor != null and groupColor != ''">group_color,</if>
            <if test="description != null and description != ''">description,</if>
            <if test="sort != null">sort,</if>
            <if test="isDefault != null">is_default,</if>
            <if test="isPublic != null">is_public,</if>
            <if test="favoriteCount != null">favorite_count,</if>
            <if test="status != null">status,</if>
            create_time
        ) VALUES (
            #{userId}, #{groupName},
            <if test="groupIcon != null and groupIcon != ''">#{groupIcon},</if>
            <if test="groupColor != null and groupColor != ''">#{groupColor},</if>
            <if test="description != null and description != ''">#{description},</if>
            <if test="sort != null">#{sort},</if>
            <if test="isDefault != null">#{isDefault},</if>
            <if test="isPublic != null">#{isPublic},</if>
            <if test="favoriteCount != null">#{favoriteCount},</if>
            <if test="status != null">#{status},</if>
            NOW()
        )
    </insert>
    
    <update id="updateGroup" parameterType="PsyFavoriteGroup">
        UPDATE psy_favorite_group
        <trim prefix="SET" suffixOverrides=",">
            <if test="groupName != null and groupName != ''">group_name = #{groupName},</if>
            <if test="groupIcon != null">group_icon = #{groupIcon},</if>
            <if test="groupColor != null">group_color = #{groupColor},</if>
            <if test="description != null">description = #{description},</if>
            <if test="sort != null">sort = #{sort},</if>
            <if test="isDefault != null">is_default = #{isDefault},</if>
            <if test="isPublic != null">is_public = #{isPublic},</if>
            <if test="status != null">status = #{status},</if>
            update_time = NOW()
        </trim>
        WHERE group_id = #{groupId}
    </update>
    
    <update id="deleteGroupById" parameterType="Long">
        UPDATE psy_favorite_group SET del_flag = '2' WHERE group_id = #{groupId}
    </update>
    
    <update id="deleteGroupByIds" parameterType="Long[]">
        UPDATE psy_favorite_group SET del_flag = '2'
        WHERE group_id IN
        <foreach collection="array" item="groupId" open="(" separator="," close=")">
            #{groupId}
        </foreach>
    </update>
    
    <select id="selectDefaultGroup" parameterType="Long" resultMap="FavoriteGroupResult">
        <include refid="selectFavoriteGroupVo"/>
        WHERE user_id = #{userId} AND is_default = 1 AND del_flag = '0'
        LIMIT 1
    </select>
    
    <insert id="createDefaultGroup" parameterType="Long">
        INSERT INTO psy_favorite_group (
            user_id, group_name, is_default, is_public, favorite_count, status, create_time
        ) VALUES (
            #{userId}, '默认收藏夹', 1, 0, 0, 1, NOW()
        )
    </insert>
    
    <update id="updateGroupFavoriteCount">
        UPDATE psy_favorite_group
        SET favorite_count = #{count},
            update_time = NOW()
        WHERE group_id = #{groupId}
    </update>
    
    <!-- 分组关系相关操作 -->
    <select id="selectGroupRelList" resultMap="FavoriteGroupRelResult">
        SELECT favorite_id, group_id, sort, create_time
        FROM psy_favorite_group_rel
        <where>
            <if test="favoriteId != null">
                AND favorite_id = #{favoriteId}
            </if>
            <if test="groupId != null">
                AND group_id = #{groupId}
            </if>
        </where>
        ORDER BY sort ASC
    </select>
    
    <insert id="insertGroupRel" parameterType="PsyFavoriteGroupRel">
        INSERT INTO psy_favorite_group_rel (
            favorite_id, group_id, 
            <if test="sort != null">sort,</if>
            create_time
        ) VALUES (
            #{favoriteId}, #{groupId},
            <if test="sort != null">#{sort},</if>
            NOW()
        )
    </insert>
    
    <delete id="deleteGroupRel">
        DELETE FROM psy_favorite_group_rel
        WHERE favorite_id = #{favoriteId}
        <if test="groupId != null">
            AND group_id = #{groupId}
        </if>
    </delete>
    
    <delete id="deleteGroupRelByFavoriteIds" parameterType="Long[]">
        DELETE FROM psy_favorite_group_rel
        WHERE favorite_id IN
        <foreach collection="array" item="favoriteId" open="(" separator="," close=")">
            #{favoriteId}
        </foreach>
    </delete>
    
    <select id="selectGroupFavorites" resultType="Map">
        SELECT 
            f.favorite_id,
            f.user_id,
            f.target_type,
            f.target_id,
            f.target_title,
            f.target_image,
            f.tags,
            f.notes,
            f.is_public,
            f.favorite_time,
            f.view_count,
            r.sort as group_sort,
            CASE f.target_type
                WHEN 1 THEN '咨询师'
                WHEN 2 THEN '课程'
                WHEN 3 THEN '冥想'
                WHEN 4 THEN '测评'
                ELSE '未知'
            END as target_type_name
        FROM psy_favorite_group_rel r
        JOIN psy_user_favorite f ON r.favorite_id = f.favorite_id
        WHERE r.group_id = #{groupId}
        AND f.user_id = #{userId}
        AND f.del_flag = '0'
        ORDER BY r.sort ASC, f.favorite_time DESC
    </select>
    
</mapper>
