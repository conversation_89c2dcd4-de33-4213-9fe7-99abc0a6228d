package com.xihuan.framework.websocket;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.xihuan.common.core.domain.entity.PsyMessage;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@ServerEndpoint("/websocket/{userId}")
@Component
public class WebSocketServer {
    private static final Logger log = LoggerFactory.getLogger(WebSocketServer.class);
    
    /**
     * 用于存放所有在线客户端
     */
    private static final Map<Long, WebSocketServer> clients = new ConcurrentHashMap<>();
    
    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    
    /**
     * 当前连接用户ID
     */
    private Long userId;
    
    /**
     * 最后一次心跳时间
     */
    private long lastHeartbeat;
    
    /**
     * 心跳计数器
     */
    private AtomicInteger heartbeatMissCount = new AtomicInteger(0);
    
    /**
     * 心跳超时时间（毫秒）
     */
    private static final long HEARTBEAT_TIMEOUT = 30000; // 30秒
    
    /**
     * 心跳最大丢失次数
     */
    private static final int MAX_HEARTBEAT_MISS = 3;
    
    /**
     * 心跳检测间隔（毫秒）
     */
    private static final long HEARTBEAT_INTERVAL = 10000; // 10秒
    
    /**
     * 心跳检测任务
     */
    private static final ScheduledExecutorService heartbeatExecutor = Executors.newSingleThreadScheduledExecutor();
    
    /**
     * 静态代码块，启动心跳检测任务
     */
    static {
        // 启动心跳检测任务
        heartbeatExecutor.scheduleAtFixedRate(() -> {
            try {
                checkHeartbeats();
            } catch (Exception e) {
                log.error("心跳检测任务异常", e);
            }
        }, HEARTBEAT_INTERVAL, HEARTBEAT_INTERVAL, TimeUnit.MILLISECONDS);
    }
    
    /**
     * 消息处理器（静态注入，WebSocket不能自动注入）
     */
    private static WebSocketMessageHandler messageHandler;
    
    @Autowired
    public void setMessageHandler(WebSocketMessageHandler messageHandler) {
        WebSocketServer.messageHandler = messageHandler;
    }

    /**
     * 连接建立成功调用的方法
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("userId") Long userId) {
        this.session = session;
        this.userId = userId;
        this.lastHeartbeat = System.currentTimeMillis();
        this.heartbeatMissCount.set(0);
        
        // 如果已经存在相同用户的连接，先移除旧连接
        if (clients.containsKey(userId)) {
            try {
                clients.get(userId).session.close();
            } catch (IOException e) {
                log.error("关闭旧连接异常", e);
            }
            clients.remove(userId);
        }
        
        clients.put(userId, this);
        log.info("用户连接:{}，当前在线人数:{}", userId, clients.size());
        
        // 发送连接成功消息
        try {
            JSONObject connectMessage = new JSONObject();
            connectMessage.put("type", "connect");
            connectMessage.put("message", "连接成功");
            
            session.getBasicRemote().sendText(connectMessage.toJSONString());
        } catch (IOException e) {
            log.error("发送连接成功消息失败", e);
        }
    }

    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        if (userId != null) {
            clients.remove(userId);
            log.info("用户退出:{}，当前在线人数:{}", userId, clients.size());
        }
    }

    /**
     * 收到客户端消息后调用的方法
     */
    @OnMessage
    public void onMessage(String message) {
        // 更新最后心跳时间
        this.lastHeartbeat = System.currentTimeMillis();
        this.heartbeatMissCount.set(0);
        
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            String type = jsonObject.getString("type");
            
            // 心跳消息特殊处理
            if ("heartbeat".equals(type)) {
                handleHeartbeat();
                return;
            }
            
            log.info("收到来自用户{}的消息:{}", userId, message);
            // 交由消息处理器处理
            if (messageHandler != null) {
                messageHandler.handleMessage(message, userId);
            } else {
                log.error("消息处理器未注入");
            }
        } catch (Exception e) {
            log.error("处理消息异常", e);
        }
    }

    /**
     * 处理心跳消息
     */
    private void handleHeartbeat() {
        try {
            JSONObject heartbeatResponse = new JSONObject();
            heartbeatResponse.put("type", "heartbeat");
            heartbeatResponse.put("timestamp", System.currentTimeMillis());
            
            session.getBasicRemote().sendText(heartbeatResponse.toJSONString());
        } catch (IOException e) {
            log.error("发送心跳响应失败", e);
        }
    }
    
    /**
     * 检查所有连接的心跳状态
     */
    private static void checkHeartbeats() {
        long now = System.currentTimeMillis();
        
        clients.forEach((userId, server) -> {
            // 检查最后心跳时间
            if (now - server.lastHeartbeat > HEARTBEAT_TIMEOUT) {
                int missCount = server.heartbeatMissCount.incrementAndGet();
                
                if (missCount >= MAX_HEARTBEAT_MISS) {
                    log.info("用户{}心跳超时，断开连接", userId);
                    try {
                        server.session.close();
                    } catch (IOException e) {
                        log.error("关闭超时连接异常", e);
                    }
                } else {
                    // 发送心跳检测消息
                    try {
                        JSONObject heartbeatCheck = new JSONObject();
                        heartbeatCheck.put("type", "heartbeat_check");
                        heartbeatCheck.put("timestamp", now);
                        
                        server.session.getBasicRemote().sendText(heartbeatCheck.toJSONString());
                        log.debug("向用户{}发送心跳检测", userId);
                    } catch (IOException e) {
                        log.error("发送心跳检测失败", e);
                    }
                }
            }
        });
    }

    /**
     * 发生错误时调用
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("用户{}发生错误", userId, error);
    }

    /**
     * 发送消息到指定用户
     */
    public static void sendMessage(Long userId, PsyMessage message) {
        WebSocketServer server = clients.get(userId);
        if (server != null && server.session.isOpen()) {
            try {
                server.session.getBasicRemote().sendText(JSON.toJSONString(message));
                log.info("发送消息到用户{}成功", userId);
            } catch (IOException e) {
                log.error("发送消息给用户{}失败", userId, e);
            }
        } else {
            log.info("用户{}不在线", userId);
        }
    }
    
    /**
     * 发送普通文本消息到指定用户
     */
    public static void sendTextMessage(Long userId, String type, String content) {
        WebSocketServer server = clients.get(userId);
        if (server != null && server.session.isOpen()) {
            try {
                JSONObject message = new JSONObject();
                message.put("type", type);
                message.put("content", content);
                message.put("timestamp", System.currentTimeMillis());
                
                server.session.getBasicRemote().sendText(message.toJSONString());
                log.info("发送{}消息到用户{}成功", type, userId);
            } catch (IOException e) {
                log.error("发送{}消息给用户{}失败", type, userId, e);
            }
        } else {
            log.info("用户{}不在线", userId);
        }
    }
    
    /**
     * 获取当前在线用户数量
     */
    public static int getOnlineCount() {
        return clients.size();
    }
    
    /**
     * 判断用户是否在线
     */
    public static boolean isOnline(Long userId) {
        return clients.containsKey(userId);
    }
}