<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyConsultantReviewMapper">

    <resultMap id="BaseResultMap" type="PsyConsultantReview">
        <id column="id" property="id"/>
        <result column="consultant_id" property="consultantId"/>
        <result column="user_id" property="userId"/>
        <result column="record_id" property="recordId"/>
        <result column="rating" property="rating"/>
        <result column="content" property="content"/>
        <result column="is_anonymous" property="isAnonymous"/>
        <result column="consult_type" property="consultType"/>
        <result column="review_time" property="reviewTime"/>
        <result column="admin_check" property="adminCheck"/>
        <result column="consultant_reply" property="consultantReply"/>
        <result column="del_flag" property="delFlag"/>
        <result column="create_by" property="createBy"/>
        <result column="update_by" property="updateBy"/>
        <result column="update_time" property="updateTime"/>
        <result column="remark" property="remark"/>
    </resultMap>

    <resultMap id="ReviewWithDetailsMap" type="PsyConsultantReview" extends="BaseResultMap">
        <association property="user" javaType="SysUser">
            <id column="user_id" property="userId"/>
            <result column="user_name" property="userName"/>
            <result column="nick_name" property="nickName"/>
        </association>
        <association property="consultant" javaType="PsyConsultant">
            <id column="consultant_id" property="id"/>
            <result column="consultant_name" property="name"/>
            <result column="consultant_title" property="title"/>
        </association>
        <association property="record" javaType="PsyConsultationRecord">
            <id column="record_id" property="id"/>
            <result column="record_start_time" property="startTime"/>
            <result column="record_duration" property="duration"/>
        </association>
    </resultMap>

    <select id="selectReviewList" parameterType="PsyConsultantReview" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_review
        <where>
            del_flag = '0'
            <if test="consultantId != null">AND consultant_id = #{consultantId}</if>
            <if test="userId != null">AND user_id = #{userId}</if>
            <if test="recordId != null">AND record_id = #{recordId}</if>
            <if test="rating != null">AND rating = #{rating}</if>
            <if test="isAnonymous != null">AND is_anonymous = #{isAnonymous}</if>
            <if test="consultType != null and consultType != ''">AND consult_type = #{consultType}</if>
            <if test="adminCheck != null">AND admin_check = #{adminCheck}</if>
        </where>
        ORDER BY review_time DESC
    </select>

    <select id="selectReviewById" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_review WHERE id = #{id} AND del_flag = '0'
    </select>

    <select id="selectReviewWithDetails" resultMap="ReviewWithDetailsMap">
        SELECT 
            r.*,
            u.user_name, u.nick_name,
            c.name AS consultant_name, c.title AS consultant_title,
            rec.start_time AS record_start_time, rec.duration AS record_duration
        FROM psy_consultant_review r
        LEFT JOIN sys_user u ON r.user_id = u.user_id
        LEFT JOIN psy_consultant c ON r.consultant_id = c.id
        LEFT JOIN psy_consultation_record rec ON r.record_id = rec.id
        WHERE r.id = #{id} AND r.del_flag = '0'
    </select>

    <select id="selectReviewsByConsultantId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_review 
        WHERE consultant_id = #{consultantId} AND del_flag = '0'
        ORDER BY review_time DESC
    </select>

    <select id="selectReviewsByUserId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_review 
        WHERE user_id = #{userId} AND del_flag = '0'
        ORDER BY review_time DESC
    </select>

    <select id="selectReviewByRecordId" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_review 
        WHERE record_id = #{recordId} AND del_flag = '0'
    </select>

    <select id="selectPendingReviews" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_review 
        WHERE admin_check = 0 AND del_flag = '0'
        ORDER BY review_time ASC
    </select>

    <select id="selectApprovedReviews" resultMap="BaseResultMap">
        SELECT * FROM psy_consultant_review 
        WHERE consultant_id = #{consultantId} AND admin_check = 1 AND del_flag = '0'
        ORDER BY review_time DESC
    </select>

    <insert id="insertReview" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_consultant_review (
            consultant_id, user_id, record_id, rating, content, is_anonymous,
            consult_type, review_time, admin_check, consultant_reply, del_flag,
            create_by, update_by, update_time, remark
        ) VALUES (
            #{consultantId}, #{userId}, #{recordId}, #{rating}, #{content}, #{isAnonymous},
            #{consultType}, #{reviewTime}, #{adminCheck}, #{consultantReply}, #{delFlag},
            #{createBy}, #{updateBy}, #{updateTime}, #{remark}
        )
    </insert>

    <update id="updateReview" parameterType="PsyConsultantReview">
        UPDATE psy_consultant_review
        <set>
            <if test="rating != null">rating = #{rating},</if>
            <if test="content != null">content = #{content},</if>
            <if test="isAnonymous != null">is_anonymous = #{isAnonymous},</if>
            <if test="consultantReply != null">consultant_reply = #{consultantReply},</if>
            <if test="adminCheck != null">admin_check = #{adminCheck},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <update id="deleteReviewById">
        UPDATE psy_consultant_review SET del_flag = '2' WHERE id = #{id}
    </update>

    <update id="deleteReviewByIds">
        UPDATE psy_consultant_review SET del_flag = '2'
        WHERE id IN
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="auditReview">
        UPDATE psy_consultant_review
        SET admin_check = #{adminCheck}, update_by = #{updateBy}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <update id="replyReview">
        UPDATE psy_consultant_review
        SET consultant_reply = #{consultantReply}, update_by = #{updateBy}, update_time = NOW()
        WHERE id = #{id}
    </update>

    <select id="calculateAverageRating" resultType="java.math.BigDecimal">
        SELECT COALESCE(AVG(rating), 0) FROM psy_consultant_review 
        WHERE consultant_id = #{consultantId} AND admin_check = 1 AND del_flag = '0'
    </select>

    <select id="countReviewsByConsultantId" resultType="int">
        SELECT COUNT(*) FROM psy_consultant_review 
        WHERE consultant_id = #{consultantId} AND admin_check = 1 AND del_flag = '0'
    </select>

    <select id="checkUserReviewed" resultType="int">
        SELECT COUNT(*) FROM psy_consultant_review 
        WHERE user_id = #{userId} AND record_id = #{recordId} AND del_flag = '0'
    </select>

    <select id="countRatingDistribution" resultType="java.util.Map">
        SELECT 
            FLOOR(rating) AS rating_level,
            COUNT(*) AS count
        FROM psy_consultant_review 
        WHERE consultant_id = #{consultantId} AND admin_check = 1 AND del_flag = '0'
        GROUP BY FLOOR(rating)
        ORDER BY rating_level DESC
    </select>

</mapper>
