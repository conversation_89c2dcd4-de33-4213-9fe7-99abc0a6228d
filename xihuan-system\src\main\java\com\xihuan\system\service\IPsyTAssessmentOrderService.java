package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTAssessmentOrder;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 测评订单Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTAssessmentOrderService {
    
    /**
     * 查询测评订单列表
     * 
     * @param order 测评订单信息
     * @return 测评订单集合
     */
    List<PsyTAssessmentOrder> selectOrderList(PsyTAssessmentOrder order);

    /**
     * 根据ID查询测评订单
     * 
     * @param id 测评订单ID
     * @return 测评订单信息
     */
    PsyTAssessmentOrder selectOrderById(Long id);

    /**
     * 根据订单编号查询测评订单
     * 
     * @param orderNo 订单编号
     * @return 测评订单信息
     */
    PsyTAssessmentOrder selectOrderByOrderNo(String orderNo);

    /**
     * 查询订单详情（包含量表、用户等信息）
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    PsyTAssessmentOrder selectOrderWithDetails(Long id);

    /**
     * 根据用户ID查询订单列表
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectOrdersByUserId(Long userId);

    /**
     * 根据量表ID查询订单列表
     * 
     * @param scaleId 量表ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectOrdersByScaleId(Long scaleId);

    /**
     * 新增测评订单
     * 
     * @param order 测评订单信息
     * @return 结果
     */
    int insertOrder(PsyTAssessmentOrder order);

    /**
     * 修改测评订单
     * 
     * @param order 测评订单信息
     * @return 结果
     */
    int updateOrder(PsyTAssessmentOrder order);

    /**
     * 删除测评订单
     * 
     * @param ids 需要删除的测评订单ID
     * @return 结果
     */
    int deleteOrderByIds(Long[] ids);

    /**
     * 创建订单
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param couponId 优惠券ID
     * @return 订单信息
     */
    PsyTAssessmentOrder createOrder(Long userId, Long scaleId, Long couponId);

    /**
     * 支付订单
     * 
     * @param orderNo 订单编号
     * @param paymentMethod 支付方式
     * @param transactionId 交易流水号
     * @return 结果
     */
    int payOrder(String orderNo, String paymentMethod, String transactionId);

    /**
     * 取消订单
     * 
     * @param orderNo 订单编号
     * @param reason 取消原因
     * @return 结果
     */
    int cancelOrder(String orderNo, String reason);

    /**
     * 退款订单
     * 
     * @param orderNo 订单编号
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 结果
     */
    int refundOrder(String orderNo, BigDecimal refundAmount, String reason);

    /**
     * 检查用户是否已购买量表
     * 
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @return 是否已购买
     */
    boolean checkUserPurchased(Long userId, Long scaleId);

    /**
     * 查询待支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectPendingOrders(Long userId);

    /**
     * 查询已支付订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectPaidOrders(Long userId);

    /**
     * 查询已取消订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectCancelledOrders(Long userId);

    /**
     * 查询已退款订单
     * 
     * @param userId 用户ID
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectRefundedOrders(Long userId);

    /**
     * 更新订单状态
     * 
     * @param id 订单ID
     * @param status 状态
     * @return 结果
     */
    int updateOrderStatus(Long id, Integer status);

    /**
     * 更新订单支付信息
     * 
     * @param id 订单ID
     * @param transactionId 交易流水号
     * @param paymentMethod 支付方式
     * @return 结果
     */
    int updateOrderPaymentInfo(Long id, String transactionId, String paymentMethod);

    /**
     * 统计用户订单数量
     * 
     * @param userId 用户ID
     * @return 数量
     */
    int countOrdersByUserId(Long userId);

    /**
     * 统计量表订单数量
     * 
     * @param scaleId 量表ID
     * @return 数量
     */
    int countOrdersByScaleId(Long scaleId);

    /**
     * 查询订单统计信息
     * 
     * @return 统计信息
     */
    Map<String, Object> selectOrderStats();

    /**
     * 查询用户订单统计
     * 
     * @param userId 用户ID
     * @return 统计信息
     */
    Map<String, Object> selectUserOrderStats(Long userId);

    /**
     * 查询量表订单统计
     * 
     * @param scaleId 量表ID
     * @return 统计信息
     */
    Map<String, Object> selectScaleOrderStats(Long scaleId);

    /**
     * 查询订单趋势统计
     * 
     * @param days 天数
     * @return 趋势统计
     */
    List<Map<String, Object>> selectOrderTrendStats(Integer days);

    /**
     * 查询收入统计
     * 
     * @param year 年份
     * @param month 月份
     * @return 收入统计
     */
    Map<String, Object> selectRevenueStats(Integer year, Integer month);

    /**
     * 查询支付方式统计
     * 
     * @return 支付方式统计
     */
    List<Map<String, Object>> selectPaymentMethodStats();

    /**
     * 查询超时未支付订单
     * 
     * @param timeoutHours 超时小时数
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> selectTimeoutOrders(Integer timeoutHours);

    /**
     * 自动取消超时订单
     * 
     * @param timeoutHours 超时小时数
     * @return 取消数量
     */
    int autoCancelTimeoutOrders(Integer timeoutHours);

    /**
     * 搜索订单
     * 
     * @param keyword 关键词
     * @param userId 用户ID
     * @param scaleId 量表ID
     * @param status 状态
     * @return 订单集合
     */
    List<PsyTAssessmentOrder> searchOrders(String keyword, Long userId, Long scaleId, Integer status);

    /**
     * 查询热销量表排行
     * 
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectHotScaleRanking(Integer limit);

    /**
     * 查询用户消费排行
     * 
     * @param limit 限制数量
     * @return 排行信息
     */
    List<Map<String, Object>> selectUserConsumptionRanking(Integer limit);

    /**
     * 生成订单编号
     * 
     * @return 订单编号
     */
    String generateOrderNo();

    /**
     * 计算订单金额
     * 
     * @param scaleId 量表ID
     * @param couponId 优惠券ID
     * @return 订单金额信息
     */
    Map<String, Object> calculateOrderAmount(Long scaleId, Long couponId);

    /**
     * 验证订单支付权限
     * 
     * @param orderNo 订单编号
     * @param userId 用户ID
     * @return 验证结果
     */
    boolean validatePaymentPermission(String orderNo, Long userId);

    /**
     * 订单支付回调处理
     * 
     * @param orderNo 订单编号
     * @param paymentData 支付数据
     * @return 处理结果
     */
    Map<String, Object> handlePaymentCallback(String orderNo, Map<String, Object> paymentData);
}
