package com.xihuan.system.mapper;

import com.xihuan.common.core.domain.entity.PsyCourseImageResource;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 图片资源表Mapper接口
 * 
 * <AUTHOR>
 */
public interface PsyCourseImageResourceMapper {
    
    /**
     * 查询图片资源列表
     * 
     * @param imageResource 图片资源信息
     * @return 图片资源集合
     */
    List<PsyCourseImageResource> selectImageResourceList(PsyCourseImageResource imageResource);

    /**
     * 根据ID查询图片资源
     * 
     * @param id 图片资源ID
     * @return 图片资源信息
     */
    PsyCourseImageResource selectImageResourceById(Long id);

    /**
     * 根据资源ID和资源类型查询图片资源列表
     * 
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @return 图片资源集合
     */
    List<PsyCourseImageResource> selectImageResourceByResourceId(@Param("resourceId") Long resourceId, @Param("resourceType") Integer resourceType);

    /**
     * 新增图片资源
     * 
     * @param imageResource 图片资源信息
     * @return 结果
     */
    int insertImageResource(PsyCourseImageResource imageResource);

    /**
     * 批量新增图片资源
     * 
     * @param imageResourceList 图片资源列表
     * @return 结果
     */
    int batchInsertImageResource(List<PsyCourseImageResource> imageResourceList);

    /**
     * 修改图片资源
     * 
     * @param imageResource 图片资源信息
     * @return 结果
     */
    int updateImageResource(PsyCourseImageResource imageResource);

    /**
     * 删除图片资源
     * 
     * @param id 图片资源ID
     * @return 结果
     */
    int deleteImageResourceById(Long id);

    /**
     * 批量删除图片资源
     * 
     * @param ids 需要删除的图片资源ID
     * @return 结果
     */
    int deleteImageResourceByIds(Long[] ids);

    /**
     * 根据资源ID和资源类型删除图片资源
     * 
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @return 结果
     */
    int deleteImageResourceByResourceId(@Param("resourceId") Long resourceId, @Param("resourceType") Integer resourceType);

    /**
     * 设置主图
     * 
     * @param id 图片资源ID
     * @param resourceId 资源ID
     * @param resourceType 资源类型
     * @return 结果
     */
    int setMainImage(@Param("id") Long id, @Param("resourceId") Long resourceId, @Param("resourceType") Integer resourceType);

    /**
     * 更新图片排序
     * 
     * @param id 图片资源ID
     * @param imageOrder 排序值
     * @return 结果
     */
    int updateImageOrder(@Param("id") Long id, @Param("imageOrder") Integer imageOrder);
}
