<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentOptionMapper">

    <!-- 结果映射 -->
    <resultMap id="OptionResultMap" type="PsyAssessmentOption">
        <id property="id" column="id"/>
        <result property="questionId" column="question_id"/>
        <result property="optionText" column="option_text"/>
        <result property="optionValue" column="option_value"/>
        <result property="score" column="score"/>
        <result property="orderNum" column="order_num"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 查询选项列表 -->
    <select id="selectOptionList" parameterType="PsyAssessmentOption" resultMap="OptionResultMap">
        SELECT * FROM psy_t_option
        WHERE del_flag = 0
        <if test="questionId != null">
            AND question_id = #{questionId}
        </if>
        <if test="optionText != null and optionText != ''">
            AND option_text LIKE CONCAT('%', #{optionText}, '%')
        </if>
        <if test="optionValue != null and optionValue != ''">
            AND option_value = #{optionValue}
        </if>
        ORDER BY question_id, order_num
    </select>

    <!-- 根据ID查询选项 -->
    <select id="selectOptionById" parameterType="Long" resultMap="OptionResultMap">
        SELECT * FROM psy_t_option WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据题目ID查询选项列表 -->
    <select id="selectOptionsByQuestionId" parameterType="Long" resultMap="OptionResultMap">
        SELECT * FROM psy_t_option 
        WHERE question_id = #{questionId} AND del_flag = 0
        ORDER BY order_num
    </select>

    <!-- 新增选项 -->
    <insert id="insertOption" parameterType="PsyAssessmentOption" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_option (
            question_id, option_text, option_value, score, order_num,
            del_flag, create_by, create_time, update_by, update_time
        ) VALUES (
            #{questionId}, #{optionText}, #{optionValue}, #{score}, #{orderNum},
            #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate()
        )
    </insert>

    <!-- 修改选项 -->
    <update id="updateOption" parameterType="PsyAssessmentOption">
        UPDATE psy_t_option
        <set>
            <if test="questionId != null">question_id = #{questionId},</if>
            <if test="optionText != null">option_text = #{optionText},</if>
            <if test="optionValue != null">option_value = #{optionValue},</if>
            <if test="score != null">score = #{score},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除选项 -->
    <update id="deleteOptionById" parameterType="Long">
        UPDATE psy_t_option SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除选项 -->
    <update id="deleteOptionByIds" parameterType="Long">
        UPDATE psy_t_option SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据题目ID删除选项 -->
    <update id="deleteOptionsByQuestionId" parameterType="Long">
        UPDATE psy_t_option SET del_flag = 1 WHERE question_id = #{questionId}
    </update>

    <!-- 批量插入选项 -->
    <insert id="batchInsertOptions" parameterType="java.util.List">
        INSERT INTO psy_t_option (
            question_id, option_text, option_value, score, order_num,
            del_flag, create_by, create_time, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.questionId}, #{item.optionText}, #{item.optionValue}, #{item.score}, #{item.orderNum},
                #{item.delFlag}, #{item.createBy}, sysdate(), #{item.updateBy}, sysdate()
            )
        </foreach>
    </insert>

    <!-- 更新选项顺序 -->
    <update id="updateOptionOrder">
        UPDATE psy_t_option SET order_num = #{orderNum} WHERE id = #{id}
    </update>

    <!-- 检查选项是否被使用 -->
    <select id="checkOptionInUse" parameterType="Long" resultType="int">
        SELECT COUNT(1) FROM psy_t_answer WHERE option_id = #{id} AND del_flag = 0
    </select>

    <!-- 查询选项统计信息 -->
    <select id="selectOptionStats" parameterType="Long" resultType="java.util.Map">
        SELECT 
            o.id,
            o.option_text,
            o.option_value,
            o.score,
            COUNT(a.id) as answer_count,
            ROUND(COUNT(a.id) * 100.0 / (
                SELECT COUNT(1) FROM psy_t_answer 
                WHERE question_id = #{questionId} AND del_flag = 0
            ), 2) as percentage
        FROM psy_t_option o
        LEFT JOIN psy_t_answer a ON o.id = a.option_id AND a.del_flag = 0
        WHERE o.question_id = #{questionId} AND o.del_flag = 0
        GROUP BY o.id, o.option_text, o.option_value, o.score
        ORDER BY o.order_num
    </select>
</mapper>
