package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsySystemTimeSlot;
import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.common.utils.spring.SpringUtils;
import com.xihuan.system.mapper.PsySystemTimeSlotMapper;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.ISysConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 系统公共时间槽Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsySystemTimeSlotServiceImpl implements IPsySystemTimeSlotService {
    
    @Autowired
    private PsySystemTimeSlotMapper systemTimeSlotMapper;
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;
    
    @Autowired
    private IPsyTimeRangeService timeRangeService;

    /**
     * 查询系统时间槽列表
     */
    @Override
    public List<PsySystemTimeSlot> selectSystemTimeSlotList(PsySystemTimeSlot systemTimeSlot) {
        return systemTimeSlotMapper.selectSystemTimeSlotList(systemTimeSlot);
    }

    /**
     * 根据ID查询系统时间槽
     */
    @Override
    public PsySystemTimeSlot selectSystemTimeSlotById(Long id) {
        return systemTimeSlotMapper.selectSystemTimeSlotById(id);
    }

    /**
     * 查询指定日期范围的系统时间槽
     */
    @Override
    public List<PsySystemTimeSlot> selectSlotsByDateRange(LocalDate startDate, LocalDate endDate, Long centerId) {
        return systemTimeSlotMapper.selectSlotsByDateRange(startDate.toString(), endDate.toString(), centerId);
    }

    /**
     * 查询指定日期的系统时间槽
     */
    @Override
    public List<PsySystemTimeSlot> selectSlotsByDate(LocalDate date, Long centerId) {
        return systemTimeSlotMapper.selectSlotsByDate(date.toString(), centerId);
    }

    /**
     * 查询有可用咨询师的时间槽
     */
    @Override
    public List<PsySystemTimeSlot> selectAvailableSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        return systemTimeSlotMapper.selectAvailableSlots(startDate.toString(), endDate.toString(), centerId);
    }

    /**
     * 获取格式化的系统时间槽数据（按日期和时间段分组）
     */
    @Override
    public Map<String, Object> getFormattedSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        List<PsySystemTimeSlot> slots = selectSlotsByDateRange(startDate, endDate, centerId);
        
        Map<String, Object> result = new HashMap<>();
        
        if (CollectionUtils.isEmpty(slots)) {
            result.put("dates", new ArrayList<>());
            result.put("totalDays", 0);
            result.put("totalSlots", 0);
            result.put("availableSlots", 0);
            return result;
        }

        // 按日期分组
        Map<String, List<PsySystemTimeSlot>> slotsByDate = slots.stream()
            .collect(Collectors.groupingBy(PsySystemTimeSlot::getDateKey));

        List<Map<String, Object>> dateList = new ArrayList<>();
        int totalSlots = 0;
        int availableSlots = 0;
        
        for (Map.Entry<String, List<PsySystemTimeSlot>> entry : slotsByDate.entrySet()) {
            String dateKey = entry.getKey();
            List<PsySystemTimeSlot> daySlots = entry.getValue();
            
            Map<String, Object> dayData = buildDayData(dateKey, daySlots);
            dateList.add(dayData);
            
            totalSlots += daySlots.size();
            availableSlots += (int) daySlots.stream().filter(slot -> slot.getHasAvailable()).count();
        }

        // 按日期排序
        dateList.sort((a, b) -> ((String) a.get("date")).compareTo((String) b.get("date")));
        
        result.put("dates", dateList);
        result.put("totalDays", dateList.size());
        result.put("totalSlots", totalSlots);
        result.put("availableSlots", availableSlots);
        
        return result;
    }

    /**
     * 新增系统时间槽
     */
    @Override
    public int insertSystemTimeSlot(PsySystemTimeSlot systemTimeSlot) {
        systemTimeSlot.setCreateTime(DateUtils.getNowDate());
        return systemTimeSlotMapper.insertSystemTimeSlot(systemTimeSlot);
    }

    /**
     * 批量新增系统时间槽
     */
    @Override
    @Transactional
    public int batchInsertSystemTimeSlots(List<PsySystemTimeSlot> systemTimeSlots) {
        if (CollectionUtils.isEmpty(systemTimeSlots)) {
            return 0;
        }
        
        // 设置创建时间
        Date now = DateUtils.getNowDate();
        systemTimeSlots.forEach(slot -> slot.setCreateTime(now));
        
        return systemTimeSlotMapper.batchInsertSystemTimeSlots(systemTimeSlots);
    }

    /**
     * 修改系统时间槽
     */
    @Override
    public int updateSystemTimeSlot(PsySystemTimeSlot systemTimeSlot) {
        systemTimeSlot.setUpdateTime(DateUtils.getNowDate());
        return systemTimeSlotMapper.updateSystemTimeSlot(systemTimeSlot);
    }

    /**
     * 删除系统时间槽信息
     */
    @Override
    public int deleteSystemTimeSlotById(Long id) {
        return systemTimeSlotMapper.deleteSystemTimeSlotById(id);
    }

    /**
     * 批量删除系统时间槽
     */
    @Override
    public int deleteSystemTimeSlotByIds(Long[] ids) {
        return systemTimeSlotMapper.deleteSystemTimeSlotByIds(ids);
    }

    /**
     * 生成指定日期范围的系统时间槽
     */
    @Override
    @Transactional
    public int generateSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        List<PsySystemTimeSlot> systemSlots = new ArrayList<>();
        
        LocalDate currentDate = startDate;
        while (!currentDate.isAfter(endDate)) {
            List<PsySystemTimeSlot> daySlots = generateSystemTimeSlotsForDate(currentDate, centerId);
            systemSlots.addAll(daySlots);
            currentDate = currentDate.plusDays(1);
        }
        
        if (!systemSlots.isEmpty()) {
            return batchInsertSystemTimeSlots(systemSlots);
        }
        
        return 0;
    }

    /**
     * 更新系统时间槽的可用性统计
     */
    @Override
    @Transactional
    public int updateAvailabilityStats(LocalDate date, Long centerId) {
        return systemTimeSlotMapper.batchUpdateAvailabilityStats(date.toString(), centerId);
    }

    /**
     * 清理过期的系统时间槽
     */
    @Override
    @Transactional
    public int cleanExpiredSystemSlots(LocalDate beforeDate, Long centerId) {
        return systemTimeSlotMapper.deleteSlotsByDateRange("1900-01-01", beforeDate.toString(), centerId);
    }

    /**
     * 重新生成系统时间槽（先清理再生成）
     */
    @Override
    @Transactional
    public int regenerateSystemTimeSlots(LocalDate startDate, LocalDate endDate, Long centerId) {
        // 先删除指定日期范围的系统时间槽
        systemTimeSlotMapper.deleteSlotsByDateRange(startDate.toString(), endDate.toString(), centerId);
        
        // 重新生成
        return generateSystemTimeSlots(startDate, endDate, centerId);
    }

    /**
     * 为指定日期生成系统时间槽
     */
    private List<PsySystemTimeSlot> generateSystemTimeSlotsForDate(LocalDate date, Long centerId) {
        List<PsySystemTimeSlot> systemSlots = new ArrayList<>();
        
        // 查询该日期所有咨询师的时间槽
        List<PsyTimeSlot> counselorSlots = timeSlotService.selectAvailableSlotsByDate(date, centerId, null);
        
        if (CollectionUtils.isEmpty(counselorSlots)) {
            return systemSlots;
        }
        
        // 按时间分组统计
        Map<LocalTime, List<PsyTimeSlot>> slotsByTime = counselorSlots.stream()
            .collect(Collectors.groupingBy(PsyTimeSlot::getStartTime));
        
        for (Map.Entry<LocalTime, List<PsyTimeSlot>> entry : slotsByTime.entrySet()) {
            LocalTime startTime = entry.getKey();
            List<PsyTimeSlot> timeSlots = entry.getValue();
            
            PsySystemTimeSlot systemSlot = createSystemTimeSlot(date, startTime, timeSlots, centerId);
            systemSlots.add(systemSlot);
        }
        
        return systemSlots;
    }

    /**
     * 创建系统时间槽
     */
    private PsySystemTimeSlot createSystemTimeSlot(LocalDate date, LocalTime startTime, 
                                                   List<PsyTimeSlot> timeSlots, Long centerId) {
        PsySystemTimeSlot systemSlot = new PsySystemTimeSlot();
        
        systemSlot.setCenterId(centerId);
        systemSlot.setDateKey(date.toString());
        systemSlot.setWeekDay(getWeekDay(date));
        systemSlot.setStartTime(startTime);
        systemSlot.setEndTime(startTime.plusMinutes(15));
        
        // 统计可用咨询师数量
        long availableCount = timeSlots.stream()
            .filter(slot -> slot.getStatus() == 0) // 可用状态
            .count();
        
        systemSlot.setAvailableCounselors((int) availableCount);
        systemSlot.setTotalCounselors(timeSlots.size());
        systemSlot.setHasAvailable(availableCount > 0);
        
        // 设置时间段ID
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeByHour(startTime.getHour());
        if (timeRange != null) {
            systemSlot.setRangeId(timeRange.getId());
        }
        
        systemSlot.setDelFlag(0);
        systemSlot.setStatus(0); // 默认可用状态

        return systemSlot;
    }

    /**
     * 构建日期数据
     */
    private Map<String, Object> buildDayData(String dateKey, List<PsySystemTimeSlot> daySlots) {
        Map<String, Object> dayData = new HashMap<>();
        LocalDate date = LocalDate.parse(dateKey);
        
        dayData.put("date", dateKey);
        dayData.put("weekDay", getWeekDay(date));
        dayData.put("isToday", date.equals(LocalDate.now()));
        
        // 按时间段分组
        Map<Long, List<PsySystemTimeSlot>> slotsByRange = daySlots.stream()
            .collect(Collectors.groupingBy(PsySystemTimeSlot::getRangeId));
        
        List<Map<String, Object>> timeRanges = new ArrayList<>();
        for (Map.Entry<Long, List<PsySystemTimeSlot>> entry : slotsByRange.entrySet()) {
            Map<String, Object> rangeData = buildTimeRangeData(entry.getKey(), entry.getValue());
            timeRanges.add(rangeData);
        }
        
        // 按时间段开始时间排序
        timeRanges.sort((a, b) -> {
            Integer startHourA = (Integer) a.get("startHour");
            Integer startHourB = (Integer) b.get("startHour");
            return startHourA.compareTo(startHourB);
        });
        
        dayData.put("timeRanges", timeRanges);
        dayData.put("totalSlots", daySlots.size());
        dayData.put("availableSlots", (int) daySlots.stream().filter(slot -> slot.getHasAvailable()).count());
        
        return dayData;
    }

    /**
     * 构建时间段数据
     */
    private Map<String, Object> buildTimeRangeData(Long rangeId, List<PsySystemTimeSlot> rangeSlots) {
        Map<String, Object> rangeData = new HashMap<>();
        
        // 获取时间段信息
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeById(rangeId);
        if (timeRange != null) {
            rangeData.put("rangeName", timeRange.getName());
            rangeData.put("iconUrl", timeRange.getIconUrl());
            rangeData.put("startHour", timeRange.getStartHour());
            rangeData.put("endHour", timeRange.getEndHour());
        }
        
        // 转换时间槽数据
        List<Map<String, Object>> slots = rangeSlots.stream()
            .map(this::convertToSlotData)
            .sorted((a, b) -> ((LocalTime) a.get("startTime")).compareTo((LocalTime) b.get("startTime")))
            .collect(Collectors.toList());
        
        rangeData.put("slots", slots);
        rangeData.put("totalSlots", rangeSlots.size());
        rangeData.put("availableSlots", (int) rangeSlots.stream().filter(slot -> slot.getHasAvailable()).count());
        
        return rangeData;
    }

    /**
     * 转换为时间槽数据
     */
    private Map<String, Object> convertToSlotData(PsySystemTimeSlot slot) {
        Map<String, Object> slotData = new HashMap<>();
        slotData.put("slotId", slot.getId());
        slotData.put("startTime", slot.getStartTime());
        slotData.put("endTime", slot.getEndTime());
        slotData.put("timeDisplay", slot.getStartTime() + "-" + slot.getEndTime());
        slotData.put("availableCounselors", slot.getAvailableCounselors());
        slotData.put("totalCounselors", slot.getTotalCounselors());
        slotData.put("hasAvailable", slot.getHasAvailable());
        slotData.put("status", slot.getStatus());
        slotData.put("statusText", getStatusText(slot.getStatus()));
        slotData.put("availabilityText", slot.getAvailableCounselors() + "/" + slot.getTotalCounselors() + "可用");

        return slotData;
    }

    /**
     * 获取星期几
     */
    private String getWeekDay(LocalDate date) {
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        return weekDays[date.getDayOfWeek().getValue() - 1];
    }

    /**
     * 获取状态文本
     */
    private String getStatusText(Integer status) {
        if (status == null) {
            return "未知";
        }
        switch (status) {
            case 0:
                return "可用";
            case 2:
                return "已过期";
            default:
                return "未知";
        }
    }

    /**
     * 批量更新系统时间槽状态
     */
    @Override
    @Transactional
    public int batchUpdateSlotStatus(List<Long> slotIds, Integer status) {
        if (CollectionUtils.isEmpty(slotIds)) {
            return 0;
        }
        return systemTimeSlotMapper.batchUpdateSlotStatus(slotIds, status);
    }

    /**
     * 更新过期的系统时间槽状态
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatus(Long centerId, Integer delayHours) {
        return systemTimeSlotMapper.updateExpiredSlotStatus(centerId, delayHours);
    }

    /**
     * 更新过期的系统时间槽状态（支持延后配置）
     */
    @Override
    @Transactional
    public int updateExpiredSlotStatusWithDelay(Long centerId) {
        // 获取延后配置
        boolean isDelayEnabled = isDelayExpirationEnabled();
        int delayHours = isDelayEnabled ? getDelayExpirationHours() : 0;

        return updateExpiredSlotStatus(centerId, delayHours);
    }

    /**
     * 检查是否启用延后过期功能
     */
    private boolean isDelayExpirationEnabled() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.enabled");
            return "true".equalsIgnoreCase(configValue) || "1".equals(configValue);
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 获取延后过期的小时数
     */
    private int getDelayExpirationHours() {
        try {
            ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
            String configValue = configService.selectConfigByKey("psy.slot.delay.expiration.hours");
            return Integer.parseInt(configValue);
        } catch (Exception e) {
            return 2; // 默认2小时
        }
    }
}
