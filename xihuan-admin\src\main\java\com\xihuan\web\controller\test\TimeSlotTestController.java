package com.xihuan.web.controller.test;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.system.service.IPsyTimeSlotService;
import com.xihuan.system.service.IPsySystemTimeSlotService;
import com.xihuan.system.service.task.PsyTimeSlotTaskService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;

/**
 * 时间槽生成测试控制器
 * 用于测试和调试时间槽生成功能
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/test/timeslot")
public class TimeSlotTestController extends BaseController {
    
    private static final Logger logger = LoggerFactory.getLogger(TimeSlotTestController.class);
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;
    
    @Autowired
    private IPsySystemTimeSlotService systemTimeSlotService;
    
    @Autowired
    private PsyTimeSlotTaskService taskService;
    
    /**
     * 手动触发生成咨询师时间槽
     */
    @PostMapping("/generate/counselor")
    public AjaxResult generateCounselorSlots(@RequestParam(defaultValue = "7") int days) {
        try {
            logger.info("手动触发生成咨询师时间槽，天数：{}", days);
            
            LocalDate startDate = LocalDate.now().plusDays(1);
            LocalDate endDate = startDate.plusDays(days - 1);
            
            int count = timeSlotService.generateSlotsForAllCounselors(startDate, endDate);
            
            logger.info("生成咨询师时间槽完成，共生成 {} 个时间槽", count);
            
            return AjaxResult.success("成功生成 " + count + " 个咨询师时间槽");
        } catch (Exception e) {
            logger.error("生成咨询师时间槽失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发生成系统时间槽
     */
    @PostMapping("/generate/system")
    public AjaxResult generateSystemSlots(@RequestParam(defaultValue = "7") int days) {
        try {
            logger.info("手动触发生成系统时间槽，天数：{}", days);
            
            LocalDate startDate = LocalDate.now().plusDays(1);
            LocalDate endDate = startDate.plusDays(days - 1);
            
            int count = systemTimeSlotService.generateSystemTimeSlots(startDate, endDate, 1L);
            
            logger.info("生成系统时间槽完成，共生成 {} 个时间槽", count);
            
            return AjaxResult.success("成功生成 " + count + " 个系统时间槽");
        } catch (Exception e) {
            logger.error("生成系统时间槽失败", e);
            return AjaxResult.error("生成失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发定时任务：生成未来时间槽
     */
    @PostMapping("/task/future")
    public AjaxResult triggerFutureTask() {
        try {
            logger.info("手动触发定时任务：生成未来时间槽");
            
            taskService.generateFutureTimeSlots();
            
            return AjaxResult.success("定时任务执行完成");
        } catch (Exception e) {
            logger.error("执行定时任务失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }
    
    /**
     * 手动触发定时任务：生成系统公共时间槽
     */
    @PostMapping("/task/system")
    public AjaxResult triggerSystemTask() {
        try {
            logger.info("手动触发定时任务：生成系统公共时间槽");
            
            taskService.generateSystemTimeSlots();
            
            return AjaxResult.success("定时任务执行完成");
        } catch (Exception e) {
            logger.error("执行定时任务失败", e);
            return AjaxResult.error("执行失败：" + e.getMessage());
        }
    }
    
    /**
     * 查询时间槽统计信息
     */
    @GetMapping("/stats")
    public AjaxResult getStats() {
        try {
            // 这里可以添加统计查询逻辑
            return AjaxResult.success("统计信息查询功能待实现");
        } catch (Exception e) {
            logger.error("查询统计信息失败", e);
            return AjaxResult.error("查询失败：" + e.getMessage());
        }
    }
}
