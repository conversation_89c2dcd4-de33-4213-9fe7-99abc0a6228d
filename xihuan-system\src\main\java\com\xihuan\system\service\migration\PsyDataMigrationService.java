package com.xihuan.system.service.migration;

import com.xihuan.common.core.domain.entity.PsyAppointment;
import com.xihuan.common.core.domain.entity.PsyTimeRange;
import com.xihuan.common.core.domain.entity.PsyTimeSlot;
import com.xihuan.system.mapper.PsyAppointmentMapper;
import com.xihuan.system.service.IPsyTimeRangeService;
import com.xihuan.system.service.IPsyTimeSlotService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 心理咨询数据迁移服务
 * 用于将旧的预约数据迁移到新的时间管理系统
 * 
 * <AUTHOR>
 */
@Service
public class PsyDataMigrationService {
    
    private static final Logger logger = LoggerFactory.getLogger(PsyDataMigrationService.class);
    
    @Autowired
    private PsyAppointmentMapper oldAppointmentMapper;
    
    @Autowired
    private IPsyTimeRangeService timeRangeService;
    
    @Autowired
    private IPsyTimeSlotService timeSlotService;

    /**
     * 执行完整的数据迁移
     * 
     * @return 迁移结果统计
     */
    @Transactional
    public MigrationResult executeFullMigration() {
        logger.info("开始执行心理咨询数据迁移");
        
        MigrationResult result = new MigrationResult();
        
        try {
            // 1. 初始化时间段定义
            int timeRangeCount = initializeTimeRanges();
            result.setTimeRangeCount(timeRangeCount);
            logger.info("初始化时间段定义完成，创建了 {} 个时间段", timeRangeCount);
            
            // 2. 迁移旧的预约数据为时间槽
            int timeSlotCount = migrateAppointmentsToTimeSlots();
            result.setTimeSlotCount(timeSlotCount);
            logger.info("迁移预约数据为时间槽完成，创建了 {} 个时间槽", timeSlotCount);
            
            // 3. 备份旧数据
            int backupCount = backupOldAppointments();
            result.setBackupCount(backupCount);
            logger.info("备份旧预约数据完成，备份了 {} 条记录", backupCount);
            
            result.setSuccess(true);
            result.setMessage("数据迁移成功完成");
            
        } catch (Exception e) {
            logger.error("数据迁移失败", e);
            result.setSuccess(false);
            result.setMessage("数据迁移失败：" + e.getMessage());
            throw e; // 回滚事务
        }
        
        logger.info("心理咨询数据迁移完成：{}", result);
        return result;
    }

    /**
     * 初始化时间段定义
     */
    private int initializeTimeRanges() {
        return timeRangeService.initDefaultTimeRanges();
    }

    /**
     * 将旧的预约数据迁移为时间槽
     */
    private int migrateAppointmentsToTimeSlots() {
        // 查询所有有效的旧预约数据
        PsyAppointment query = new PsyAppointment();
        query.setDelFlag(0);
        List<PsyAppointment> oldAppointments = oldAppointmentMapper.selectByCounselorAndDate(
            null, new Date(), new Date()
        );
        
        if (oldAppointments == null || oldAppointments.isEmpty()) {
            logger.info("没有找到需要迁移的旧预约数据");
            return 0;
        }
        
        List<PsyTimeSlot> newTimeSlots = new ArrayList<>();
        
        for (PsyAppointment oldAppointment : oldAppointments) {
            try {
                List<PsyTimeSlot> slots = convertAppointmentToTimeSlots(oldAppointment);
                newTimeSlots.addAll(slots);
            } catch (Exception e) {
                logger.warn("转换预约记录失败，预约ID：{}，错误：{}", 
                    oldAppointment.getAppointmentId(), e.getMessage());
            }
        }
        
        if (!newTimeSlots.isEmpty()) {
            return timeSlotService.batchInsertTimeSlots(newTimeSlots);
        }
        
        return 0;
    }

    /**
     * 将单个预约记录转换为时间槽
     */
    private List<PsyTimeSlot> convertAppointmentToTimeSlots(PsyAppointment appointment) {
        List<PsyTimeSlot> slots = new ArrayList<>();
        
        // 解析时间段字符串（如："09:00-10:00"）
        String timeSlot = appointment.getTimeSlot();
        if (timeSlot == null || !timeSlot.contains("-")) {
            logger.warn("无效的时间段格式：{}", timeSlot);
            return slots;
        }
        
        String[] times = timeSlot.split("-");
        if (times.length != 2) {
            logger.warn("无效的时间段格式：{}", timeSlot);
            return slots;
        }
        
        try {
            LocalTime startTime = LocalTime.parse(times[0].trim());
            LocalTime endTime = LocalTime.parse(times[1].trim());
            LocalDate appointmentDate = appointment.getAppointmentDate().toInstant()
                .atZone(java.time.ZoneId.systemDefault()).toLocalDate();
            
            // 按15分钟间隔生成时间槽
            LocalTime currentTime = startTime;
            while (currentTime.isBefore(endTime)) {
                LocalTime slotEndTime = currentTime.plusMinutes(15);
                if (slotEndTime.isAfter(endTime)) {
                    break;
                }
                
                PsyTimeSlot slot = createTimeSlotFromAppointment(
                    appointment, appointmentDate, currentTime, slotEndTime
                );
                slots.add(slot);
                
                currentTime = slotEndTime;
            }
            
        } catch (Exception e) {
            logger.warn("解析时间失败，预约ID：{}，时间段：{}，错误：{}", 
                appointment.getAppointmentId(), timeSlot, e.getMessage());
        }
        
        return slots;
    }

    /**
     * 从预约记录创建时间槽
     */
    private PsyTimeSlot createTimeSlotFromAppointment(
            PsyAppointment appointment, LocalDate date, LocalTime startTime, LocalTime endTime) {
        
        PsyTimeSlot slot = new PsyTimeSlot();
        slot.setCounselorId(appointment.getCounselorId());
        slot.setDateKey(date.toString());
        slot.setWeekDay(getWeekDay(date));
        slot.setStartTime(startTime);
        slot.setEndTime(endTime);
        slot.setCenterId(1L); // 默认咨询中心ID
        slot.setIsPublic(appointment.getIsPublic());
        
        // 根据预约状态设置时间槽状态
        if ("1".equals(appointment.getStatus())) {
            slot.setStatus(1); // 已预约
        } else if ("2".equals(appointment.getStatus())) {
            slot.setStatus(2); // 已过期
        } else {
            slot.setStatus(0); // 可用
        }
        
        // 设置时间段ID
        PsyTimeRange timeRange = timeRangeService.selectTimeRangeByHour(startTime.getHour());
        if (timeRange != null) {
            slot.setRangeId(timeRange.getId());
        }
        
        slot.setDelFlag(0);
        slot.setCreateTime(new Date());
        
        return slot;
    }

    /**
     * 备份旧的预约数据
     */
    private int backupOldAppointments() {
        // 这里可以实现将旧数据导出到备份表或文件
        // 暂时返回0，表示没有执行备份操作
        logger.info("旧预约数据备份功能待实现");
        return 0;
    }

    /**
     * 获取星期几
     */
    private String getWeekDay(LocalDate date) {
        String[] weekDays = {"周一", "周二", "周三", "周四", "周五", "周六", "周日"};
        return weekDays[date.getDayOfWeek().getValue() - 1];
    }

    /**
     * 迁移结果统计
     */
    public static class MigrationResult {
        private boolean success;
        private String message;
        private int timeRangeCount;
        private int timeSlotCount;
        private int backupCount;

        // Getters and Setters
        public boolean isSuccess() { return success; }
        public void setSuccess(boolean success) { this.success = success; }
        
        public String getMessage() { return message; }
        public void setMessage(String message) { this.message = message; }
        
        public int getTimeRangeCount() { return timeRangeCount; }
        public void setTimeRangeCount(int timeRangeCount) { this.timeRangeCount = timeRangeCount; }
        
        public int getTimeSlotCount() { return timeSlotCount; }
        public void setTimeSlotCount(int timeSlotCount) { this.timeSlotCount = timeSlotCount; }
        
        public int getBackupCount() { return backupCount; }
        public void setBackupCount(int backupCount) { this.backupCount = backupCount; }

        @Override
        public String toString() {
            return String.format("MigrationResult{success=%s, message='%s', timeRangeCount=%d, timeSlotCount=%d, backupCount=%d}",
                success, message, timeRangeCount, timeSlotCount, backupCount);
        }
    }
}
