package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyCourseReview;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyCourseReviewMapper;
import com.xihuan.system.service.IPsyCourseReviewService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.List;

/**
 * 课程评价表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyCourseReviewServiceImpl implements IPsyCourseReviewService {
    
    @Autowired
    private PsyCourseReviewMapper reviewMapper;

    /**
     * 查询评价列表
     * 
     * @param review 评价信息
     * @return 评价集合
     */
    @Override
    public List<PsyCourseReview> selectReviewList(PsyCourseReview review) {
        return reviewMapper.selectReviewList(review);
    }

    /**
     * 根据ID查询评价
     * 
     * @param id 评价ID
     * @return 评价信息
     */
    @Override
    public PsyCourseReview selectReviewById(Long id) {
        return reviewMapper.selectReviewById(id);
    }

    /**
     * 查询评价详情（包含课程和用户信息）
     * 
     * @param id 评价ID
     * @return 评价详情
     */
    @Override
    public PsyCourseReview selectReviewWithDetails(Long id) {
        return reviewMapper.selectReviewWithDetails(id);
    }

    /**
     * 根据课程ID查询评价列表
     * 
     * @param courseId 课程ID
     * @return 评价集合
     */
    @Override
    public List<PsyCourseReview> selectReviewsByCourseId(Long courseId) {
        return reviewMapper.selectReviewsByCourseId(courseId);
    }

    /**
     * 根据用户ID查询评价列表
     * 
     * @param userId 用户ID
     * @return 评价集合
     */
    @Override
    public List<PsyCourseReview> selectReviewsByUserId(Long userId) {
        return reviewMapper.selectReviewsByUserId(userId);
    }

    /**
     * 新增评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    public int insertReview(PsyCourseReview review) {
        review.setCreateTime(DateUtils.getNowDate());
        review.setDelFlag(0);
        
        // 如果没有设置评分，默认为5星
        if (review.getRating() == null) {
            review.setRating(5);
        }
        
        return reviewMapper.insertReview(review);
    }

    /**
     * 修改评价
     * 
     * @param review 评价信息
     * @return 结果
     */
    @Override
    public int updateReview(PsyCourseReview review) {
        review.setUpdateTime(DateUtils.getNowDate());
        return reviewMapper.updateReview(review);
    }

    /**
     * 删除评价
     * 
     * @param id 评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewById(Long id) {
        return reviewMapper.deleteReviewById(id);
    }

    /**
     * 批量删除评价
     * 
     * @param ids 需要删除的评价ID
     * @return 结果
     */
    @Override
    public int deleteReviewByIds(Long[] ids) {
        return reviewMapper.deleteReviewByIds(ids);
    }

    /**
     * 计算课程平均评分
     * 
     * @param courseId 课程ID
     * @return 平均评分
     */
    @Override
    public BigDecimal calculateAverageRating(Long courseId) {
        return reviewMapper.calculateAverageRating(courseId);
    }

    /**
     * 统计课程评价数量
     * 
     * @param courseId 课程ID
     * @return 评价数量
     */
    @Override
    public int countReviewsByCourseId(Long courseId) {
        return reviewMapper.countReviewsByCourseId(courseId);
    }

    /**
     * 检查用户是否已评价课程
     * 
     * @param userId 用户ID
     * @param courseId 课程ID
     * @return 是否已评价
     */
    @Override
    public boolean checkUserReviewed(Long userId, Long courseId) {
        int count = reviewMapper.checkUserReviewed(userId, courseId);
        return count > 0;
    }
}
