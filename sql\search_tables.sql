-- 搜索记录表
CREATE TABLE `psy_search_record` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '搜索记录ID',
  `user_id` bigint(20) DEFAULT NULL COMMENT '用户ID（可为空，支持匿名搜索）',
  `keyword` varchar(200) NOT NULL COMMENT '搜索关键词',
  `search_type` varchar(50) DEFAULT 'all' COMMENT '搜索类型：all/consultant/course/meditation/assessment',
  `result_count` int(11) DEFAULT 0 COMMENT '搜索结果数量',
  `search_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '搜索时间',
  `ip_address` varchar(50) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `del_flag` char(1) DEFAULT '0' COMMENT '删除标志（0代表存在 2代表删除）',
  PRIMARY KEY (`id`),
  KEY `idx_keyword` (`keyword`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_search_time` (`search_time`),
  KEY `idx_search_type` (`search_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索记录表';

-- 热门搜索表
CREATE TABLE `psy_hot_search` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '热门搜索ID',
  `keyword` varchar(200) NOT NULL COMMENT '搜索关键词',
  `search_type` varchar(50) DEFAULT 'all' COMMENT '搜索类型',
  `search_count` int(11) DEFAULT 1 COMMENT '搜索次数',
  `last_search_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '最后搜索时间',
  `hot_score` decimal(10,2) DEFAULT 0.00 COMMENT '热度分数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_keyword_type` (`keyword`, `search_type`),
  KEY `idx_hot_score` (`hot_score`),
  KEY `idx_search_count` (`search_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='热门搜索表';

-- 搜索建议表
CREATE TABLE `psy_search_suggestion` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '搜索建议ID',
  `keyword` varchar(200) NOT NULL COMMENT '建议关键词',
  `suggestion_type` varchar(50) DEFAULT 'auto' COMMENT '建议类型：auto/manual/hot',
  `search_count` int(11) DEFAULT 0 COMMENT '被搜索次数',
  `priority` int(11) DEFAULT 0 COMMENT '优先级（数字越大优先级越高）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_keyword` (`keyword`),
  KEY `idx_priority` (`priority`),
  KEY `idx_search_count` (`search_count`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='搜索建议表';
