package com.xihuan.system.service;

import com.xihuan.common.core.domain.entity.PsyTimeAppointment;
import com.xihuan.common.core.domain.dto.PsyAppointmentRequestDTO;
import java.util.List;
import java.util.Map;

/**
 * 预约记录Service接口
 * 
 * <AUTHOR>
 */
public interface IPsyTimeAppointmentService {
    
    /**
     * 查询预约记录列表
     * 
     * @param appointment 预约记录
     * @return 预约记录集合
     */
    List<PsyTimeAppointment> selectAppointmentList(PsyTimeAppointment appointment);
    
    /**
     * 根据ID查询预约记录
     * 
     * @param id 预约记录主键
     * @return 预约记录
     */
    PsyTimeAppointment selectAppointmentById(String id);
    
    /**
     * 根据用户ID查询预约记录
     * 
     * @param userId 用户ID
     * @param status 预约状态（可选）
     * @return 预约记录集合
     */
    List<PsyTimeAppointment> selectAppointmentsByUserId(Long userId, Integer status);
    
    /**
     * 根据咨询师ID查询预约记录
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 预约记录集合
     */
    List<PsyTimeAppointment> selectAppointmentsByCounselor(Long counselorId, String startDate, String endDate);
    
    /**
     * 创建预约
     * 
     * @param requestDTO 预约请求
     * @return 预约记录ID
     */
    String createAppointment(PsyAppointmentRequestDTO requestDTO);
    
    /**
     * 修改预约记录
     * 
     * @param appointment 预约记录
     * @return 结果
     */
    int updateAppointment(PsyTimeAppointment appointment);
    
    /**
     * 确认预约
     * 
     * @param appointmentId 预约ID
     * @return 结果
     */
    int confirmAppointment(String appointmentId);
    
    /**
     * 取消预约
     * 
     * @param appointmentId 预约ID
     * @param reason 取消原因
     * @return 结果
     */
    int cancelAppointment(String appointmentId, String reason);
    
    /**
     * 完成预约
     * 
     * @param appointmentId 预约ID
     * @return 结果
     */
    int completeAppointment(String appointmentId);
    
    /**
     * 标记缺席
     * 
     * @param appointmentId 预约ID
     * @return 结果
     */
    int markAbsent(String appointmentId);
    
    /**
     * 更新支付状态
     * 
     * @param appointmentId 预约ID
     * @param paymentStatus 支付状态
     * @param paymentId 支付单号
     * @return 结果
     */
    int updatePaymentStatus(String appointmentId, Integer paymentStatus, String paymentId);
    
    /**
     * 删除预约记录
     * 
     * @param id 预约记录主键
     * @return 结果
     */
    int deleteAppointmentById(String id);
    
    /**
     * 批量删除预约记录
     * 
     * @param ids 需要删除的预约记录主键集合
     * @return 结果
     */
    int deleteAppointmentByIds(String[] ids);
    
    /**
     * 检查时间槽是否可预约
     * 
     * @param slotIds 时间槽ID列表
     * @return 是否可预约
     */
    boolean checkSlotsAvailable(List<Long> slotIds);
    
    /**
     * 检查用户是否有冲突的预约
     * 
     * @param userId 用户ID
     * @param dateKey 日期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否有冲突
     */
    boolean checkUserConflict(Long userId, String dateKey, String startTime, String endTime);
    
    /**
     * 获取用户的预约统计
     * 
     * @param userId 用户ID
     * @return 预约统计信息
     */
    Map<String, Object> getUserAppointmentStats(Long userId);
    
    /**
     * 获取咨询师的预约统计
     * 
     * @param counselorId 咨询师ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 预约统计信息
     */
    Map<String, Object> getCounselorAppointmentStats(Long counselorId, String startDate, String endDate);
}
