package com.xihuan.web.controller.miniapp;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyTabbarMenu;
import com.xihuan.common.core.domain.model.LoginUser;
import com.xihuan.framework.web.service.TokenService;
import com.xihuan.system.service.wxService.IPsyTabbarMenuService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;

/**
 * 小程序导航菜单Controller（用户端）
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/miniapp/user/tabbar")
public class MiniAppTabbarController extends BaseController {
    
    @Autowired
    private IPsyTabbarMenuService menuService;
    
    @Autowired
    private TokenService tokenService;

    /**
     * 获取小程序导航菜单列表
     */
    @GetMapping("/list")
    public AjaxResult list(HttpServletRequest request) {
        try {
            // 获取当前用户权限
            LoginUser loginUser = null;
            List<String> userPermissions = new ArrayList<>();
            
            try {
                loginUser = tokenService.getLoginUser(request);
                if (loginUser != null && loginUser.getPermissions() != null) {
                    userPermissions.addAll(loginUser.getPermissions());
                }
            } catch (Exception e) {
                // 未登录用户，使用默认权限
                userPermissions.add("wxuser");
            }
            
            // 如果没有权限，默认给小程序用户权限
            if (userPermissions.isEmpty()) {
                userPermissions.add("wxuser");
            }
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(userPermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取小程序导航菜单失败", e);
            return error("获取导航菜单失败");
        }
    }

    /**
     * 根据权限获取菜单
     */
    @GetMapping("/listByPermissions")
    public AjaxResult listByPermissions(@RequestParam String permissions) {
        try {
            String[] permissionArray = permissions.split(",");
            List<String> permissionList = new ArrayList<>();
            for (String permission : permissionArray) {
                String trimmed = permission.trim();
                if (!trimmed.isEmpty()) {
                    permissionList.add(trimmed);
                }
            }
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(permissionList);
            return success(list);
        } catch (Exception e) {
            logger.error("根据权限获取菜单失败", e);
            return error("获取菜单失败");
        }
    }

//    /**
//     * 获取菜单详情
//     */
//    @GetMapping("/{id}")
//    public AjaxResult getInfo(@PathVariable Long id) {
//        try {
//            PsyTabbarMenu menu = menuService.selectMenuById(id);
//            if (menu == null) {
//                return error("菜单不存在");
//            }
//            return success(menu);
//        } catch (Exception e) {
//            logger.error("获取菜单详情失败", e);
//            return error("获取菜单详情失败");
//        }
//    }

    /**
     * 获取所有启用的菜单
     */
    @GetMapping("/all")
    public AjaxResult getAllMenus() {
        try {
            PsyTabbarMenu menu = new PsyTabbarMenu();
            menu.setStatus("0"); // 只获取启用的菜单
            List<PsyTabbarMenu> list = menuService.selectMenuList(menu);
            return success(list);
        } catch (Exception e) {
            logger.error("获取所有菜单失败", e);
            return error("获取菜单失败");
        }
    }

    /**
     * 获取用户可访问的菜单
     */
    @GetMapping("/userMenus")
    public AjaxResult getUserMenus(HttpServletRequest request) {
        try {
            LoginUser loginUser = tokenService.getLoginUser(request);
            if (loginUser == null) {
                return error("用户未登录");
            }
            
            List<String> userPermissions = new ArrayList<>();
            if (loginUser.getPermissions() != null) {
                userPermissions.addAll(loginUser.getPermissions());
            }
            
            // 添加默认的小程序用户权限
            userPermissions.add("wxuser");
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(userPermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取用户菜单失败", e);
            return error("获取用户菜单失败");
        }
    }

    /**
     * 获取首页菜单（无需权限）
     */
    @GetMapping("/home")
    public AjaxResult getHomeMenus() {
        try {
            // 获取无权限要求的菜单或公共菜单
            List<String> publicPermissions = new ArrayList<>();
            publicPermissions.add(""); // 空权限表示公共菜单
            publicPermissions.add("wxuser"); // 小程序用户权限
            
            List<PsyTabbarMenu> list = menuService.selectMenuByPermissions(publicPermissions);
            return success(list);
        } catch (Exception e) {
            logger.error("获取首页菜单失败", e);
            return error("获取首页菜单失败");
        }
    }

    /**
     * 根据菜单索引获取菜单
     */
    @GetMapping("/index/{index}")
    public AjaxResult getMenuByIndex(@PathVariable Integer index) {
        try {
            PsyTabbarMenu menu = new PsyTabbarMenu();
            menu.setIndex(index);
            List<PsyTabbarMenu> list = menuService.selectMenuList(menu);
            
            if (list.isEmpty()) {
                return error("菜单不存在");
            }
            
            return success(list.get(0));
        } catch (Exception e) {
            logger.error("根据索引获取菜单失败", e);
            return error("获取菜单失败");
        }
    }

//    /**
//     * 检查菜单权限
//     */
//    @PostMapping("/checkPermission")
//    public AjaxResult checkPermission(@RequestParam Long menuId, HttpServletRequest request) {
//        try {
//            PsyTabbarMenu menu = menuService.selectMenuById(menuId);
//            if (menu == null) {
//                return error("菜单不存在");
//            }
//
//            // 如果菜单无权限要求，直接返回有权限
//            if (menu.getPermissions() == null || menu.getPermissions().trim().isEmpty()) {
//                return success(true);
//            }
//
//            // 获取用户权限
//            LoginUser loginUser = null;
//            try {
//                loginUser = tokenService.getLoginUser(request);
//            } catch (Exception e) {
//                // 未登录用户
//            }
//
//            if (loginUser == null || loginUser.getPermissions() == null) {
//                return success(false);
//            }
//
//            // 检查用户是否有菜单所需权限
//            String[] requiredPermissions = menu.getPermissions().split(",");
//            for (String requiredPermission : requiredPermissions) {
//                if (loginUser.getPermissions().contains(requiredPermission.trim())) {
//                    return success(true);
//                }
//            }
//
//            return success(false);
//        } catch (Exception e) {
//            logger.error("检查菜单权限失败", e);
//            return error("权限检查失败");
//        }
//    }

    /**
     * 获取菜单统计信息
     */
    @GetMapping("/statistics")
    public AjaxResult getStatistics() {
        try {
            List<PsyTabbarMenu> allMenus = menuService.selectMenuList(new PsyTabbarMenu());
            
            java.util.Map<String, Object> statistics = new java.util.HashMap<>();
            statistics.put("totalMenus", allMenus.size());
            
            long enabledMenus = allMenus.stream().filter(menu -> "0".equals(menu.getStatus())).count();
            statistics.put("enabledMenus", enabledMenus);
            
            long disabledMenus = allMenus.stream().filter(menu -> "1".equals(menu.getStatus())).count();
            statistics.put("disabledMenus", disabledMenus);
            
            return success(statistics);
        } catch (Exception e) {
            logger.error("获取菜单统计失败", e);
            return error("获取统计信息失败");
        }
    }
}
