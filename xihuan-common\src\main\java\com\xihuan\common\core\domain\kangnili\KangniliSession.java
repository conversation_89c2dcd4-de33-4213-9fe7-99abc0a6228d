package com.xihuan.common.core.domain.kangnili;

import lombok.Data;

import java.util.Date;

/**
 * 康尼利测评会话实体（对应kangnili_session表）
 */
@Data
public class KangniliSession {
    /**
     * 会话唯一标识，使用UUID生成32位字符串
     */
    private String sessionId;

    /**
     * 测评开始时间，精确到毫秒
     */
    private Date startTime;

    /**
     * 测评完成时间，未完成时为null
     */
    private Date finishTime;

    /**
     * 测评总分，根据所有题目得分累加
     */
    private Integer totalScore;

    /**
     * 结果等级，根据总分划分为low/medium/high
     */
    private String resultLevel;

    // 分享码
    private String shareCode;
}