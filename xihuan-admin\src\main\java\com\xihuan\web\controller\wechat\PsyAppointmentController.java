package com.xihuan.web.controller.wechat;

import com.xihuan.common.core.controller.BaseController;
import com.xihuan.common.core.domain.AjaxResult;
import com.xihuan.common.core.domain.entity.PsyAppointment;
import com.xihuan.system.domain.dto.PsyAppointmentTimeDTO;
import com.xihuan.system.service.wxService.PsyAppointmentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.sql.Date;
import java.util.List;

/**
 * 预约时段管理接口
 */
@RestController
@RequestMapping("/wechat/appointment")
public class PsyAppointmentController extends BaseController {

    @Autowired
    private PsyAppointmentService appointmentService;

    /**
     * 获取咨询师的可用时段
     */
    @GetMapping("/counselor/{counselorId}")
    public AjaxResult getCounselorSlots(@PathVariable("counselorId") Long counselorId,
                                       @RequestParam(required = false, defaultValue = "7") int days) {
        List<PsyAppointmentTimeDTO> slots = appointmentService.getAvailableTimes(counselorId, days);
        return success(slots);
    }

    /**
     * 获取公共预约时段
     */
    @GetMapping("/public")
    public AjaxResult getPublicSlots(
            @RequestParam(required = false, defaultValue = "7") int days) {
        // 计算日期范围
        LocalDate today = LocalDate.now();
        LocalDate endLocalDate = today.plusDays(days);
        
        // 转换为java.sql.Date
        Date startDate = Date.valueOf(today);
        Date endDate = Date.valueOf(endLocalDate);
        
        // 获取公共时段并转换为前端需要的格式
        List<PsyAppointmentTimeDTO> slots = appointmentService.getPublicTimesFormatted(startDate, endDate);
        return success(slots);
    }

    /**
     * 生成预约时段（管理员接口）
     */
    @PostMapping("/generate")
    public AjaxResult generateSlots() {
        appointmentService.genSlots();
        return success();
    }
} 