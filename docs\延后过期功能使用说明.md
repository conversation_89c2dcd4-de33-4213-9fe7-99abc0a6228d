# 时间槽延后过期功能使用说明

## 功能概述

为了解决咨询师到门店需要2小时容错时间的问题，系统新增了**延后过期功能**。启用此功能后，时间槽不会在结束时间立即过期，而是延后指定小时数后才标记为过期。

## 业务场景

- **问题**: 咨询师可能因为交通、突发情况等原因无法准时到达门店
- **需求**: 给咨询师2小时的容错时间
- **解决方案**: 时间槽在结束时间后延后2小时才标记为过期

## 功能特性

### 🕐 灵活配置
- **开关控制**: 可以启用/禁用延后过期功能
- **时间可调**: 延后时间可配置（0-24小时）
- **实时生效**: 配置修改后立即生效

### 🔧 智能判断
- **精确计算**: 基于时间槽的结束时间 + 延后小时数
- **兼容处理**: 兼容原有的过期逻辑
- **异常保护**: 配置异常时使用默认值

## 配置管理

### 1. 通过管理接口配置

#### 查看当前配置
```bash
GET /system/timeSlotConfig/delayExpiration
```

**响应示例**:
```json
{
  "code": 200,
  "data": {
    "enabled": true,
    "hours": 2,
    "description": "已启用延后过期功能，延后 2 小时"
  }
}
```

#### 设置延后过期配置
```bash
POST /system/timeSlotConfig/delayExpiration
Content-Type: application/json

{
  "enabled": true,
  "hours": 2
}
```

#### 重置为默认配置
```bash
POST /system/timeSlotConfig/delayExpiration/reset
```

#### 测试延后过期功能
```bash
POST /system/timeSlotConfig/delayExpiration/test
```

### 2. 通过数据库直接配置

```sql
-- 启用延后过期功能
UPDATE sys_config 
SET config_value = 'true' 
WHERE config_key = 'psy.slot.delay.expiration.enabled';

-- 设置延后2小时
UPDATE sys_config 
SET config_value = '2' 
WHERE config_key = 'psy.slot.delay.expiration.hours';

-- 禁用延后过期功能
UPDATE sys_config 
SET config_value = 'false' 
WHERE config_key = 'psy.slot.delay.expiration.enabled';
```

## 过期逻辑对比

### 原有逻辑（不延后）
```
时间槽结束时间: 2025-07-10 10:00
当前时间: 2025-07-10 10:01
结果: 立即标记为过期
```

### 新逻辑（延后2小时）
```
时间槽结束时间: 2025-07-10 10:00
延后时间: 2小时
过期判断时间: 2025-07-10 12:00
当前时间: 2025-07-10 11:30
结果: 仍然可用，未过期
```

## 定时任务配置

### 使用新的延后过期任务
推荐使用支持延后配置的定时任务：

```sql
-- 替换原有的过期状态更新任务
INSERT INTO sys_job (job_id, job_name, job_group, invoke_target, cron_expression, misfire_policy, concurrent, status, create_by, create_time, remark) 
VALUES (3, 'updateExpiredSlotStatusWithDelay', 'SYSTEM', 'psyTimeSlotTaskService.updateExpiredSlotStatusWithDelay()', '0 0 * * * ?', '1', '1', '0', 'admin', now(), '更新过期时间槽状态，支持延后2小时的容错配置');
```

### 任务执行逻辑
1. 获取所有可用状态的时间槽
2. 检查延后过期配置
3. 计算过期判断时间：
   - 启用延后：时间槽结束时间 + 延后小时数
   - 未启用延后：时间槽结束时间
4. 标记超过判断时间的时间槽为过期

## 使用示例

### 场景1：启用2小时延后
```bash
# 1. 启用延后过期功能
POST /system/timeSlotConfig/delayExpiration
{
  "enabled": true,
  "hours": 2
}

# 2. 测试配置
POST /system/timeSlotConfig/delayExpiration/test

# 3. 手动执行定时任务测试
# 时间槽：09:00-09:15，当前时间：10:30
# 结果：时间槽仍然可用（需要到11:15才过期）
```

### 场景2：禁用延后功能
```bash
# 1. 禁用延后过期功能
POST /system/timeSlotConfig/delayExpiration
{
  "enabled": false,
  "hours": 2
}

# 2. 手动执行定时任务测试
# 时间槽：09:00-09:15，当前时间：09:16
# 结果：时间槽立即过期
```

### 场景3：自定义延后时间
```bash
# 设置延后3小时
POST /system/timeSlotConfig/delayExpiration
{
  "enabled": true,
  "hours": 3
}
```

## 监控和日志

### 日志信息
定时任务执行时会记录详细日志：

```
2025-07-10 10:00:00 INFO - 开始执行定时任务：更新过期时间槽状态（支持延后配置）
2025-07-10 10:00:01 INFO - 更新过期时间槽状态，延后配置：true，延后小时数：2，过期时间槽数量：5
2025-07-10 10:00:02 INFO - 定时任务完成：成功更新 5 个过期时间槽状态（已考虑延后配置）
```

### 监控建议
1. **配置监控**: 监控延后过期配置的变更
2. **执行监控**: 监控定时任务的执行状态和耗时
3. **数据监控**: 监控过期时间槽的数量变化
4. **异常告警**: 配置获取失败时的告警

## 注意事项

### ⚠️ 重要提醒
1. **配置生效**: 配置修改后立即生效，无需重启服务
2. **数据一致性**: 建议在业务低峰期修改配置
3. **时间范围**: 延后小时数建议设置在0-24小时之间
4. **兼容性**: 新功能完全兼容原有逻辑

### 🔧 最佳实践
1. **测试验证**: 修改配置后使用测试接口验证
2. **逐步调整**: 建议先小范围测试，再全面启用
3. **监控观察**: 启用后密切观察系统运行状况
4. **备份配置**: 重要配置修改前先备份

## 故障排查

### 常见问题
1. **配置不生效**: 检查数据库配置是否正确
2. **时间计算错误**: 检查服务器时区设置
3. **定时任务不执行**: 检查Quartz任务配置

### 排查步骤
1. 查看配置：`GET /system/timeSlotConfig/delayExpiration`
2. 测试功能：`POST /system/timeSlotConfig/delayExpiration/test`
3. 查看日志：检查定时任务执行日志
4. 验证数据：查询数据库中的配置值

这个功能完美解决了咨询师到门店需要容错时间的问题！
