package com.xihuan.system.domain.dto;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;

/**
 * 微信支付相关DTO
 */
public class WxPayDTO {

    /**
     * 创建支付订单请求
     */
    @Data
    public static class CreatePayOrderRequest {
        
        /** 订单号 */
        @NotBlank(message = "订单号不能为空")
        private String orderNo;
        
        /** 商品描述 */
        @NotBlank(message = "商品描述不能为空")
        private String description;
        
        /** 支付金额（元） */
        @NotNull(message = "支付金额不能为空")
        @Positive(message = "支付金额必须大于0")
        private BigDecimal amount;
        
        /** 用户openid */
        @NotBlank(message = "用户openid不能为空")
        private String openid;
        
        /** 客户端IP */
        private String clientIp;
        
        /** 附加数据 */
        private String attach;
        
        /** 商品标记 */
        private String goodsTag;
        
        /** 指定支付方式 */
        private String limitPay;
        
        /** 订单优惠标记 */
        private String promotionInfo;
    }

    /**
     * 支付结果响应
     */
    @Data
    public static class PayOrderResponse {
        
        /** 小程序调起支付所需参数 */
        private PayParams payParams;
        
        /** 预支付交易会话标识 */
        private String prepayId;
        
        /** 订单号 */
        private String orderNo;
        
        /** 支付金额 */
        private BigDecimal amount;
        
        /** 过期时间 */
        private String expireTime;
    }

    /**
     * 小程序调起支付参数
     */
    @Data
    public static class PayParams {
        
        /** 小程序ID */
        private String appId;
        
        /** 时间戳 */
        private String timeStamp;
        
        /** 随机字符串 */
        private String nonceStr;
        
        /** 订单详情扩展字符串 */
        private String packageValue;
        
        /** 签名方式 */
        private String signType;
        
        /** 签名 */
        private String paySign;
    }

    /**
     * 支付回调通知
     */
    @Data
    public static class PayNotifyRequest {
        
        /** 通知ID */
        private String id;
        
        /** 通知创建时间 */
        private String createTime;
        
        /** 通知类型 */
        private String eventType;
        
        /** 通知数据类型 */
        private String resourceType;
        
        /** 通知资源数据 */
        private NotifyResource resource;
        
        /** 回调摘要 */
        private String summary;
    }

    /**
     * 通知资源数据
     */
    @Data
    public static class NotifyResource {
        
        /** 加密算法类型 */
        private String algorithm;
        
        /** 数据密文 */
        private String ciphertext;
        
        /** 附加数据 */
        private String associatedData;
        
        /** 原始类型 */
        private String originalType;
        
        /** 随机串 */
        private String nonce;
    }

    /**
     * 支付成功通知数据
     */
    @Data
    public static class PaySuccessNotify {
        
        /** 应用ID */
        private String appid;
        
        /** 商户号 */
        private String mchid;
        
        /** 商户订单号 */
        private String outTradeNo;
        
        /** 微信支付订单号 */
        private String transactionId;
        
        /** 交易类型 */
        private String tradeType;
        
        /** 交易状态 */
        private String tradeState;
        
        /** 交易状态描述 */
        private String tradeStateDesc;
        
        /** 付款银行 */
        private String bankType;
        
        /** 附加数据 */
        private String attach;
        
        /** 支付完成时间 */
        private String successTime;
        
        /** 支付者 */
        private PayerInfo payer;
        
        /** 订单金额 */
        private AmountInfo amount;
        
        /** 场景信息 */
        private SceneInfo sceneInfo;
        
        /** 优惠功能 */
        private PromotionDetail[] promotionDetail;
    }

    /**
     * 支付者信息
     */
    @Data
    public static class PayerInfo {
        
        /** 用户标识 */
        private String openid;
    }

    /**
     * 订单金额信息
     */
    @Data
    public static class AmountInfo {
        
        /** 总金额 */
        private Integer total;
        
        /** 用户支付金额 */
        private Integer payerTotal;
        
        /** 货币类型 */
        private String currency;
        
        /** 用户支付币种 */
        private String payerCurrency;
    }

    /**
     * 场景信息
     */
    @Data
    public static class SceneInfo {
        
        /** 商户端设备号 */
        private String deviceId;
    }

    /**
     * 优惠功能
     */
    @Data
    public static class PromotionDetail {
        
        /** 券ID */
        private String couponId;
        
        /** 优惠名称 */
        private String name;
        
        /** 优惠范围 */
        private String scope;
        
        /** 优惠类型 */
        private String type;
        
        /** 优惠券面额 */
        private Integer amount;
        
        /** 活动ID */
        private String stockId;
        
        /** 微信出资 */
        private Integer wechatpayContribute;
        
        /** 商户出资 */
        private Integer merchantContribute;
        
        /** 其他出资 */
        private Integer otherContribute;
        
        /** 优惠币种 */
        private String currency;
        
        /** 单品列表 */
        private GoodsDetail[] goodsDetail;
    }

    /**
     * 单品列表
     */
    @Data
    public static class GoodsDetail {
        
        /** 商品编码 */
        private String goodsId;
        
        /** 商品数量 */
        private Integer quantity;
        
        /** 商品单价 */
        private Integer unitPrice;
        
        /** 商品优惠金额 */
        private Integer discountAmount;
        
        /** 商品备注 */
        private String goodsRemark;
    }

    /**
     * 查询订单请求
     */
    @Data
    public static class QueryOrderRequest {
        
        /** 商户订单号 */
        private String outTradeNo;
        
        /** 微信支付订单号 */
        private String transactionId;
    }

    /**
     * 查询订单响应
     */
    @Data
    public static class QueryOrderResponse {
        
        /** 应用ID */
        private String appid;
        
        /** 商户号 */
        private String mchid;
        
        /** 商户订单号 */
        private String outTradeNo;
        
        /** 微信支付订单号 */
        private String transactionId;
        
        /** 交易类型 */
        private String tradeType;
        
        /** 交易状态 */
        private String tradeState;
        
        /** 交易状态描述 */
        private String tradeStateDesc;
        
        /** 付款银行 */
        private String bankType;
        
        /** 附加数据 */
        private String attach;
        
        /** 支付完成时间 */
        private String successTime;
        
        /** 支付者 */
        private PayerInfo payer;
        
        /** 订单金额 */
        private AmountInfo amount;
    }

    /**
     * 申请退款请求
     */
    @Data
    public static class RefundRequest {
        
        /** 商户订单号 */
        private String outTradeNo;
        
        /** 微信支付订单号 */
        private String transactionId;
        
        /** 商户退款单号 */
        @NotBlank(message = "商户退款单号不能为空")
        private String outRefundNo;
        
        /** 退款原因 */
        private String reason;
        
        /** 退款金额 */
        @NotNull(message = "退款金额不能为空")
        @Positive(message = "退款金额必须大于0")
        private BigDecimal refundAmount;
        
        /** 原订单金额 */
        @NotNull(message = "原订单金额不能为空")
        @Positive(message = "原订单金额必须大于0")
        private BigDecimal totalAmount;
        
        /** 退款结果回调url */
        private String notifyUrl;
    }

    /**
     * 申请退款响应
     */
    @Data
    public static class RefundResponse {
        
        /** 微信支付退款单号 */
        private String refundId;
        
        /** 商户退款单号 */
        private String outRefundNo;
        
        /** 微信支付订单号 */
        private String transactionId;
        
        /** 商户订单号 */
        private String outTradeNo;
        
        /** 退款渠道 */
        private String channel;
        
        /** 退款入账账户 */
        private String userReceivedAccount;
        
        /** 退款成功时间 */
        private String successTime;
        
        /** 退款创建时间 */
        private String createTime;
        
        /** 退款状态 */
        private String status;
        
        /** 资金账户 */
        private String fundsAccount;
        
        /** 金额信息 */
        private RefundAmountInfo amount;
        
        /** 优惠退款信息 */
        private PromotionDetail[] promotionDetail;
    }

    /**
     * 退款金额信息
     */
    @Data
    public static class RefundAmountInfo {
        
        /** 订单金额 */
        private Integer total;
        
        /** 退款金额 */
        private Integer refund;
        
        /** 用户支付金额 */
        private Integer payerTotal;
        
        /** 用户退款金额 */
        private Integer payerRefund;
        
        /** 应结退款金额 */
        private Integer settlementRefund;
        
        /** 应结订单金额 */
        private Integer settlementTotal;
        
        /** 优惠退款金额 */
        private Integer discountRefund;
        
        /** 退款币种 */
        private String currency;
    }
}
