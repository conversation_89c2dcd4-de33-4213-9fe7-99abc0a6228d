package com.xihuan.system.service;

import com.xihuan.system.domain.dto.WxPayDTO;

import java.math.BigDecimal;

/**
 * 订单支付服务接口
 */
public interface IOrderPaymentService {

    /**
     * 创建支付订单
     *
     * @param orderType 订单类型（course:课程, meditation:冥想, consultant:咨询, assessment:测评）
     * @param orderNo 订单号
     * @param openid 用户openid
     * @param clientIp 客户端IP
     * @return 支付订单响应
     */
    WxPayDTO.PayOrderResponse createPayOrder(String orderType, String orderNo, String openid, String clientIp);

    /**
     * 查询支付订单
     *
     * @param orderType 订单类型
     * @param orderNo 订单号
     * @return 查询订单响应
     */
    WxPayDTO.QueryOrderResponse queryPayOrder(String orderType, String orderNo);

    /**
     * 处理支付成功
     *
     * @param orderType 订单类型
     * @param orderNo 订单号
     * @param transactionId 微信支付交易号
     * @param paymentMethod 支付方式
     * @return 处理结果
     */
    boolean handlePaymentSuccess(String orderType, String orderNo, String transactionId, String paymentMethod);

    /**
     * 申请退款
     *
     * @param orderType 订单类型
     * @param orderNo 订单号
     * @param refundAmount 退款金额
     * @param reason 退款原因
     * @return 退款响应
     */
    WxPayDTO.RefundResponse refundOrder(String orderType, String orderNo, BigDecimal refundAmount, String reason);

    /**
     * 关闭订单
     *
     * @param orderType 订单类型
     * @param orderNo 订单号
     * @return 关闭结果
     */
    boolean closeOrder(String orderType, String orderNo);

    /**
     * 生成订单号
     *
     * @param orderType 订单类型
     * @return 订单号
     */
    String generateOrderNo(String orderType);

    /**
     * 从订单号解析订单类型
     *
     * @param orderNo 订单号
     * @return 订单类型
     */
    String parseOrderType(String orderNo);
}
