<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyHotSearchMapper">
    
    <resultMap type="PsyHotSearch" id="PsyHotSearchResult">
        <result property="id"              column="id"                />
        <result property="keyword"         column="keyword"           />
        <result property="searchType"      column="search_type"       />
        <result property="searchCount"     column="search_count"      />
        <result property="lastSearchTime"  column="last_search_time"  />
        <result property="hotScore"        column="hot_score"         />
        <result property="status"          column="status"            />
        <result property="createTime"      column="create_time"       />
        <result property="updateTime"      column="update_time"       />
    </resultMap>

    <sql id="selectPsyHotSearchVo">
        select id, keyword, search_type, search_count, last_search_time, hot_score, status, create_time, update_time from psy_hot_search
    </sql>

    <select id="selectHotSearchList" parameterType="PsyHotSearch" resultMap="PsyHotSearchResult">
        <include refid="selectPsyHotSearchVo"/>
        <where>  
            <if test="keyword != null  and keyword != ''"> and keyword like concat('%', #{keyword}, '%')</if>
            <if test="searchType != null  and searchType != ''"> and search_type = #{searchType}</if>
            <if test="searchCount != null "> and search_count = #{searchCount}</if>
            <if test="lastSearchTime != null "> and last_search_time = #{lastSearchTime}</if>
            <if test="hotScore != null "> and hot_score = #{hotScore}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
        order by hot_score desc, search_count desc
    </select>
    
    <select id="selectHotSearchById" parameterType="Long" resultMap="PsyHotSearchResult">
        <include refid="selectPsyHotSearchVo"/>
        where id = #{id}
    </select>
        
    <insert id="insertHotSearch" parameterType="PsyHotSearch" useGeneratedKeys="true" keyProperty="id">
        insert into psy_hot_search
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword,</if>
            <if test="searchType != null and searchType != ''">search_type,</if>
            <if test="searchCount != null">search_count,</if>
            <if test="lastSearchTime != null">last_search_time,</if>
            <if test="hotScore != null">hot_score,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">#{keyword},</if>
            <if test="searchType != null and searchType != ''">#{searchType},</if>
            <if test="searchCount != null">#{searchCount},</if>
            <if test="lastSearchTime != null">#{lastSearchTime},</if>
            <if test="hotScore != null">#{hotScore},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateHotSearch" parameterType="PsyHotSearch">
        update psy_hot_search
        <trim prefix="SET" suffixOverrides=",">
            <if test="keyword != null and keyword != ''">keyword = #{keyword},</if>
            <if test="searchType != null and searchType != ''">search_type = #{searchType},</if>
            <if test="searchCount != null">search_count = #{searchCount},</if>
            <if test="lastSearchTime != null">last_search_time = #{lastSearchTime},</if>
            <if test="hotScore != null">hot_score = #{hotScore},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteHotSearchById" parameterType="Long">
        delete from psy_hot_search where id = #{id}
    </delete>

    <delete id="deleteHotSearchByIds" parameterType="String">
        delete from psy_hot_search where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    
    <select id="selectByKeywordAndType" resultMap="PsyHotSearchResult">
        <include refid="selectPsyHotSearchVo"/>
        where keyword = #{keyword} and search_type = #{searchType}
    </select>
    
    <update id="incrementSearchCount">
        update psy_hot_search 
        set search_count = search_count + 1, 
            last_search_time = now(),
            update_time = now()
        where keyword = #{keyword} and search_type = #{searchType}
    </update>
    
    <select id="selectHotSearchByType" resultMap="PsyHotSearchResult">
        <include refid="selectPsyHotSearchVo"/>
        where status = '0'
        <if test="searchType != null and searchType != '' and searchType != 'all'">
            and search_type = #{searchType}
        </if>
        order by hot_score desc, search_count desc
        <if test="limit != null">
            limit #{limit}
        </if>
    </select>
    
    <update id="updateHotScore">
        update psy_hot_search 
        set hot_score = #{hotScore}, update_time = now()
        where id = #{id}
    </update>

</mapper>
