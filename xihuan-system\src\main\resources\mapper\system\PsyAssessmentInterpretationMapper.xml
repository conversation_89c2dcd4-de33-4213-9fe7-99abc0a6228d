<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xihuan.system.mapper.PsyAssessmentInterpretationMapper">

    <!-- 结果映射 -->
    <resultMap id="InterpretationResultMap" type="PsyAssessmentInterpretation">
        <id property="id" column="id"/>
        <result property="scaleId" column="scale_id"/>
        <result property="dimension" column="dimension"/>
        <result property="minScore" column="min_score"/>
        <result property="maxScore" column="max_score"/>
        <result property="levelName" column="level_name"/>
        <result property="levelDescription" column="level_description"/>
        <result property="suggestions" column="suggestions"/>
        <result property="color" column="color"/>
        <result property="orderNum" column="order_num"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 带量表的结果映射 -->
    <resultMap id="InterpretationWithScaleMap" type="PsyAssessmentInterpretation" extends="InterpretationResultMap">
        <association property="scale" javaType="PsyAssessmentScale">
            <id property="id" column="s_id"/>
            <result property="scaleName" column="s_scale_name"/>
            <result property="scaleCode" column="s_scale_code"/>
        </association>
    </resultMap>

    <!-- 查询结果解释列表 -->
    <select id="selectInterpretationList" parameterType="PsyAssessmentInterpretation" resultMap="InterpretationWithScaleMap">
        SELECT i.*, 
               s.id as s_id, s.scale_name as s_scale_name, s.scale_code as s_scale_code
        FROM psy_t_interpretation i
        LEFT JOIN psy_t_scale s ON i.scale_id = s.id
        WHERE i.del_flag = 0
        <if test="scaleId != null">
            AND i.scale_id = #{scaleId}
        </if>
        <if test="dimension != null">
            AND (i.dimension = #{dimension} OR i.dimension IS NULL)
        </if>
        <if test="levelName != null and levelName != ''">
            AND i.level_name LIKE CONCAT('%', #{levelName}, '%')
        </if>
        ORDER BY i.scale_id, i.dimension, i.min_score
    </select>

    <!-- 根据ID查询结果解释 -->
    <select id="selectInterpretationById" parameterType="Long" resultMap="InterpretationResultMap">
        SELECT * FROM psy_t_interpretation WHERE id = #{id} AND del_flag = 0
    </select>

    <!-- 根据量表ID查询结果解释列表 -->
    <select id="selectInterpretationsByScaleId" parameterType="Long" resultMap="InterpretationResultMap">
        SELECT * FROM psy_t_interpretation 
        WHERE scale_id = #{scaleId} AND del_flag = 0
        ORDER BY dimension, min_score
    </select>

    <!-- 根据量表ID和维度查询结果解释列表 -->
    <select id="selectInterpretationsByDimension" resultMap="InterpretationResultMap">
        SELECT * FROM psy_t_interpretation 
        WHERE scale_id = #{scaleId} AND dimension = #{dimension} AND del_flag = 0
        ORDER BY min_score
    </select>

    <!-- 根据分数查询结果解释 -->
    <select id="selectInterpretationByScore" resultMap="InterpretationResultMap">
        SELECT * FROM psy_t_interpretation 
        WHERE scale_id = #{scaleId} 
        <if test="dimension != null and dimension != ''">
            AND dimension = #{dimension}
        </if>
        <if test="dimension == null or dimension == ''">
            AND (dimension IS NULL OR dimension = '')
        </if>
        AND #{score} BETWEEN min_score AND max_score
        AND del_flag = 0
        LIMIT 1
    </select>

    <!-- 新增结果解释 -->
    <insert id="insertInterpretation" parameterType="PsyAssessmentInterpretation" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO psy_t_interpretation (
            scale_id, dimension, min_score, max_score, level_name, level_description,
            suggestions, color, order_num, del_flag, create_by, create_time, update_by, update_time
        ) VALUES (
            #{scaleId}, #{dimension}, #{minScore}, #{maxScore}, #{levelName}, #{levelDescription},
            #{suggestions}, #{color}, #{orderNum}, #{delFlag}, #{createBy}, sysdate(), #{updateBy}, sysdate()
        )
    </insert>

    <!-- 修改结果解释 -->
    <update id="updateInterpretation" parameterType="PsyAssessmentInterpretation">
        UPDATE psy_t_interpretation
        <set>
            <if test="scaleId != null">scale_id = #{scaleId},</if>
            <if test="dimension != null">dimension = #{dimension},</if>
            <if test="minScore != null">min_score = #{minScore},</if>
            <if test="maxScore != null">max_score = #{maxScore},</if>
            <if test="levelName != null">level_name = #{levelName},</if>
            <if test="levelDescription != null">level_description = #{levelDescription},</if>
            <if test="suggestions != null">suggestions = #{suggestions},</if>
            <if test="color != null">color = #{color},</if>
            <if test="orderNum != null">order_num = #{orderNum},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = sysdate()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 删除结果解释 -->
    <update id="deleteInterpretationById" parameterType="Long">
        UPDATE psy_t_interpretation SET del_flag = 1 WHERE id = #{id}
    </update>

    <!-- 批量删除结果解释 -->
    <update id="deleteInterpretationByIds" parameterType="Long">
        UPDATE psy_t_interpretation SET del_flag = 1 WHERE id IN
        <foreach collection="array" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 根据量表ID删除结果解释 -->
    <update id="deleteInterpretationsByScaleId" parameterType="Long">
        UPDATE psy_t_interpretation SET del_flag = 1 WHERE scale_id = #{scaleId}
    </update>

    <!-- 批量插入结果解释 -->
    <insert id="batchInsertInterpretations" parameterType="java.util.List">
        INSERT INTO psy_t_interpretation (
            scale_id, dimension, min_score, max_score, level_name, level_description,
            suggestions, color, order_num, del_flag, create_by, create_time, update_by, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.scaleId}, #{item.dimension}, #{item.minScore}, #{item.maxScore}, 
                #{item.levelName}, #{item.levelDescription}, #{item.suggestions}, #{item.color}, 
                #{item.orderNum}, #{item.delFlag}, #{item.createBy}, sysdate(), 
                #{item.updateBy}, sysdate()
            )
        </foreach>
    </insert>

    <!-- 检查分数范围是否重叠 -->
    <select id="checkScoreRangeOverlap" resultType="int">
        SELECT COUNT(1) FROM psy_t_interpretation
        WHERE scale_id = #{scaleId}
        <if test="dimension != null and dimension != ''">
            AND dimension = #{dimension}
        </if>
        <if test="dimension == null or dimension == ''">
            AND (dimension IS NULL OR dimension = '')
        </if>
        AND (
            (#{minScore} BETWEEN min_score AND max_score) OR
            (#{maxScore} BETWEEN min_score AND max_score) OR
            (min_score BETWEEN #{minScore} AND #{maxScore}) OR
            (max_score BETWEEN #{minScore} AND #{maxScore})
        )
        <if test="excludeId != null">
            AND id != #{excludeId}
        </if>
        AND del_flag = 0
    </select>

    <!-- 查询量表的维度列表 -->
    <select id="selectDimensionsByScaleId" parameterType="Long" resultType="String">
        SELECT DISTINCT dimension FROM psy_t_interpretation 
        WHERE scale_id = #{scaleId} AND dimension IS NOT NULL AND dimension != '' AND del_flag = 0
        ORDER BY dimension
    </select>

    <!-- 查询量表的结果等级列表 -->
    <select id="selectLevelsByScaleId" parameterType="Long" resultType="String">
        SELECT DISTINCT level_name FROM psy_t_interpretation 
        WHERE scale_id = #{scaleId} AND del_flag = 0
        ORDER BY min_score
    </select>
</mapper>
