package com.xihuan.system.service.impl;

import com.xihuan.common.core.domain.entity.PsyConsultantOrder;
import com.xihuan.common.core.domain.entity.PsyConsultationRecord;
import com.xihuan.common.exception.ServiceException;
import com.xihuan.common.utils.DateUtils;
import com.xihuan.system.mapper.PsyConsultantOrderMapper;
import com.xihuan.system.mapper.PsyConsultationRecordMapper;
import com.xihuan.system.service.IPsyConsultantInterruptionService;
import com.xihuan.system.service.IPsyConsultationRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 心理咨询记录表Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class PsyConsultationRecordServiceImpl implements IPsyConsultationRecordService {
    
    @Autowired
    private PsyConsultationRecordMapper recordMapper;
    
    @Autowired
    private PsyConsultantOrderMapper orderMapper;
    
    @Autowired
    private IPsyConsultantInterruptionService interruptionService;

    /**
     * 查询咨询记录列表
     * 
     * @param record 咨询记录信息
     * @return 咨询记录集合
     */
    @Override
    public List<PsyConsultationRecord> selectRecordList(PsyConsultationRecord record) {
        return recordMapper.selectRecordList(record);
    }

    /**
     * 查询咨询记录详情（包含用户、咨询师等信息）
     * 
     * @param id 咨询记录ID
     * @return 咨询记录详情
     */
    @Override
    public PsyConsultationRecord selectRecordWithDetails(Long id) {
        return recordMapper.selectRecordWithDetails(id);
    }

    /**
     * 根据ID查询咨询记录
     * 
     * @param id 咨询记录ID
     * @return 咨询记录信息
     */
    @Override
    public PsyConsultationRecord selectRecordById(Long id) {
        return recordMapper.selectRecordById(id);
    }

    /**
     * 新增咨询记录
     * 
     * @param record 咨询记录信息
     * @return 结果
     */
    @Override
    public int insertRecord(PsyConsultationRecord record) {
        record.setCreateTime(DateUtils.getNowDate());
        record.setDelFlag("0");
        return recordMapper.insertRecord(record);
    }

    /**
     * 修改咨询记录
     * 
     * @param record 咨询记录信息
     * @return 结果
     */
    @Override
    public int updateRecord(PsyConsultationRecord record) {
        record.setUpdateTime(DateUtils.getNowDate());
        return recordMapper.updateRecord(record);
    }

    /**
     * 删除咨询记录
     * 
     * @param id 咨询记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRecordById(Long id) {
        // 删除相关中断记录
        interruptionService.deleteInterruptionByRecordId(id);
        
        return recordMapper.deleteRecordById(id);
    }

    /**
     * 批量删除咨询记录
     * 
     * @param ids 需要删除的咨询记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRecordByIds(Long[] ids) {
        // 删除相关中断记录
        for (Long id : ids) {
            interruptionService.deleteInterruptionByRecordId(id);
        }
        
        return recordMapper.deleteRecordByIds(ids);
    }

    /**
     * 根据用户ID查询咨询记录列表
     * 
     * @param userId 用户ID
     * @return 咨询记录集合
     */
    @Override
    public List<PsyConsultationRecord> selectRecordsByUserId(Long userId) {
        return recordMapper.selectRecordsByUserId(userId);
    }

    /**
     * 根据咨询师ID查询咨询记录列表
     * 
     * @param consultantId 咨询师ID
     * @return 咨询记录集合
     */
    @Override
    public List<PsyConsultationRecord> selectRecordsByConsultantId(Long consultantId) {
        return recordMapper.selectRecordsByConsultantId(consultantId);
    }

    /**
     * 根据订单ID查询咨询记录
     * 
     * @param orderId 订单ID
     * @return 咨询记录信息
     */
    @Override
    public PsyConsultationRecord selectRecordByOrderId(Long orderId) {
        return recordMapper.selectRecordByOrderId(orderId);
    }

    /**
     * 开始咨询
     * 
     * @param orderId 订单ID
     * @return 咨询记录
     */
    @Override
    @Transactional
    public PsyConsultationRecord startConsultation(Long orderId) {
        // 获取订单信息
        PsyConsultantOrder order = orderMapper.selectOrderById(orderId);
        if (order == null) {
            throw new ServiceException("订单不存在");
        }
        
        if (!"已支付".equals(order.getStatus())) {
            throw new ServiceException("订单未支付，无法开始咨询");
        }
        
        // 检查是否已有咨询记录
        PsyConsultationRecord existingRecord = recordMapper.selectRecordByOrderId(orderId);
        if (existingRecord != null) {
            throw new ServiceException("该订单已有咨询记录");
        }
        
        // 创建咨询记录
        PsyConsultationRecord record = new PsyConsultationRecord();
        record.setUserId(order.getUserId());
        record.setConsultantId(order.getConsultantId());
        record.setOrderId(orderId);
        record.setStartTime(order.getScheduledTime());
        record.setEndTime(new Date(order.getScheduledTime().getTime() + order.getDuration() * 60 * 1000));
        record.setActualStartTime(DateUtils.getNowDate());
        record.setConsultType(order.getConsultType());
        record.setConsultCount(1);
        record.setDelFlag("0");
        record.setCreateTime(DateUtils.getNowDate());
        
        int result = recordMapper.insertRecord(record);
        
        // 更新订单状态
        if (result > 0) {
            orderMapper.updateOrderStatus(orderId, "咨询中");
        }
        
        return record;
    }

    /**
     * 结束咨询
     * 
     * @param recordId 记录ID
     * @param consultContent 咨询内容摘要
     * @return 结果
     */
    @Override
    @Transactional
    public int endConsultation(Long recordId, String consultContent) {
        PsyConsultationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null) {
            throw new ServiceException("咨询记录不存在");
        }
        
        Date now = DateUtils.getNowDate();
        Date actualStartTime = record.getActualStartTime();
        
        // 计算实际咨询时长
        int duration = 0;
        if (actualStartTime != null) {
            duration = (int) ((now.getTime() - actualStartTime.getTime()) / (1000 * 60));
        }
        
        // 减去中断时长
        int interruptionDuration = interruptionService.sumInterruptionDuration(recordId);
        duration = Math.max(0, duration - interruptionDuration);
        
        int result = recordMapper.updateRecordStatus(recordId, actualStartTime, now, duration);
        
        // 更新咨询内容
        if (consultContent != null && !consultContent.isEmpty()) {
            record.setConsultContent(consultContent);
            recordMapper.updateRecord(record);
        }
        
        // 更新订单状态
        if (result > 0 && record.getOrderId() != null) {
            orderMapper.updateOrderStatus(record.getOrderId(), "已完成");
        }
        
        return result;
    }

    /**
     * 中断咨询
     * 
     * @param recordId 记录ID
     * @param interruptType 中断类型
     * @param reason 中断原因
     * @return 结果
     */
    @Override
    @Transactional
    public int interruptConsultation(Long recordId, String interruptType, String reason) {
        PsyConsultationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null) {
            throw new ServiceException("咨询记录不存在");
        }
        
        // 创建中断记录
        com.xihuan.common.core.domain.entity.PsyConsultantInterruption interruption = 
            new com.xihuan.common.core.domain.entity.PsyConsultantInterruption();
        interruption.setRecordId(recordId);
        interruption.setInterruptType(interruptType);
        interruption.setReason(reason);
        interruption.setCreateTime(DateUtils.getNowDate());
        
        int result = interruptionService.insertInterruption(interruption);
        
        // 更新订单状态
        if (result > 0 && record.getOrderId() != null) {
            orderMapper.updateOrderStatus(record.getOrderId(), "已中断");
        }
        
        return result;
    }

    /**
     * 恢复咨询
     * 
     * @param recordId 记录ID
     * @return 结果
     */
    @Override
    @Transactional
    public int resumeConsultation(Long recordId) {
        PsyConsultationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null) {
            throw new ServiceException("咨询记录不存在");
        }
        
        // 更新订单状态
        if (record.getOrderId() != null) {
            return orderMapper.updateOrderStatus(record.getOrderId(), "咨询中");
        }
        
        return 1;
    }

    /**
     * 用户评价
     * 
     * @param recordId 记录ID
     * @param userRating 用户评价
     * @return 结果
     */
    @Override
    public int rateConsultation(Long recordId, Integer userRating) {
        return recordMapper.updateUserRating(recordId, userRating);
    }

    /**
     * 统计用户咨询数据
     * 
     * @param userId 用户ID
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getUserConsultationStats(Long userId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 咨询次数
        int consultationCount = recordMapper.countUserConsultations(userId);
        stats.put("consultationCount", consultationCount);
        
        // 总咨询时长
        int totalDuration = recordMapper.sumUserConsultationDuration(userId);
        stats.put("totalDuration", totalDuration);
        
        // 平均咨询时长
        int avgDuration = consultationCount > 0 ? totalDuration / consultationCount : 0;
        stats.put("avgDuration", avgDuration);
        
        return stats;
    }

    /**
     * 统计咨询师咨询数据
     * 
     * @param consultantId 咨询师ID
     * @return 统计数据
     */
    @Override
    public Map<String, Object> getConsultantConsultationStats(Long consultantId) {
        Map<String, Object> stats = new HashMap<>();
        
        // 咨询次数
        int consultationCount = recordMapper.countConsultantConsultations(consultantId);
        stats.put("consultationCount", consultationCount);
        
        // 总咨询时长
        int totalDuration = recordMapper.sumConsultantConsultationDuration(consultantId);
        stats.put("totalDuration", totalDuration);
        
        // 平均咨询时长
        int avgDuration = consultationCount > 0 ? totalDuration / consultationCount : 0;
        stats.put("avgDuration", avgDuration);
        
        // 平均评分
        BigDecimal avgRating = recordMapper.calculateConsultantAvgRating(consultantId);
        stats.put("avgRating", avgRating != null ? avgRating : BigDecimal.ZERO);
        
        return stats;
    }

    /**
     * 查询用户与咨询师的咨询记录
     * 
     * @param userId 用户ID
     * @param consultantId 咨询师ID
     * @return 咨询记录集合
     */
    @Override
    public List<PsyConsultationRecord> selectRecordsByUserAndConsultant(Long userId, Long consultantId) {
        return recordMapper.selectRecordsByUserAndConsultant(userId, consultantId);
    }

    /**
     * 查询指定时间段的咨询记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 咨询记录集合
     */
    @Override
    public List<PsyConsultationRecord> selectRecordsByTimeRange(Date startTime, Date endTime) {
        return recordMapper.selectRecordsByTimeRange(startTime, endTime);
    }

    /**
     * 检查咨询记录是否可以评价
     * 
     * @param recordId 记录ID
     * @param userId 用户ID
     * @return 是否可以评价
     */
    @Override
    public boolean canRate(Long recordId, Long userId) {
        PsyConsultationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null || !record.getUserId().equals(userId)) {
            return false;
        }
        
        // 咨询已结束且未评价
        return record.getActualEndTime() != null && record.getUserRating() == null;
    }

    /**
     * 检查咨询记录是否可以中断
     * 
     * @param recordId 记录ID
     * @return 是否可以中断
     */
    @Override
    public boolean canInterrupt(Long recordId) {
        PsyConsultationRecord record = recordMapper.selectRecordById(recordId);
        if (record == null) {
            return false;
        }
        
        // 咨询已开始但未结束
        return record.getActualStartTime() != null && record.getActualEndTime() == null;
    }
}
